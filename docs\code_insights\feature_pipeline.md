# 特征工程流程 Feature Pipeline

## 概述

特征工程是推荐系统的核心环节，负责将原始数据转换为模型可用的特征向量。本系统支持177维特征，包括数值特征、类别特征和交叉特征。

## 特征处理流程图

```mermaid
flowchart TD
    Start[原始Parquet数据] --> Load[数据加载]
    Load --> Analyze[DataAnalyzer<br/>数据分析]
    
    Analyze --> Stats[统计特征提取]
    Stats --> NumFeatures[数值特征<br/>88维]
    Stats --> CatFeatures[类别特征<br/>45维]
    Stats --> CrossFeatures[交叉特征<br/>44维]
    
    NumFeatures --> Normalize[StandardScaler<br/>标准化处理]
    CatFeatures --> Encode[类别编码<br/>One-hot/Label]
    CrossFeatures --> Combine[特征组合]
    
    Normalize --> Merge[特征合并<br/>177维]
    Encode --> Merge
    Combine --> Merge
    
    Merge --> Selection[FeatureManager<br/>特征选择]
    Selection --> Include{包含特征组?}
    Include -->|Yes| IncludeGroups[选择指定组]
    Include -->|No| Exclude{排除特征组?}
    Exclude -->|Yes| ExcludeGroups[排除指定组]
    Exclude -->|No| UseAll[使用全部特征]
    
    IncludeGroups --> Final[最终特征集]
    ExcludeGroups --> Final
    UseAll --> Final
    
    Final --> Save[保存NPY文件]
    Save --> Metadata[生成feature_metadata.json]
```

## 特征组详细说明

### 1. 基础特征（88维）

```python
# 原始数值特征
basic_features = [
    'conversion',     # 转化标签
    'age',           # 年龄
    'gender',        # 性别
    'region',        # 地区
    'category',      # 类别
    'price',         # 价格
    'brand',         # 品牌
    'rating'         # 评分
]
```

**输入形状**: `(batch_size, 8)`  
**处理方式**: StandardScaler标准化  
**输出形状**: `(batch_size, 8)`  
**存在原因**: 基础用户和物品属性，是推荐系统的核心信息

### 2. 嵌入特征（45维）

```python
# 嵌入向量特征
embedding_features = {
    'user_embedding': 15,     # 用户嵌入
    'item_embedding': 15,     # 物品嵌入
    'context_embedding': 15   # 上下文嵌入
}
```

**输入形状**: 原始ID  
**处理方式**: Embedding层映射  
**输出形状**: `(batch_size, 45)`  
**存在原因**: 将高维稀疏的ID特征压缩到低维稠密空间

### 3. 行为特征（44维）

```python
# 用户行为特征
behavior_features = {
    'click_position': 10,     # 点击位置特征
    'session_length': 10,     # 会话长度特征
    'time_spent': 10,         # 停留时间特征
    'income_level': 10,       # 收入水平特征
    'noise': 4                # 噪声特征（用于测试）
}
```

**输入形状**: 变长序列  
**处理方式**: 统计聚合（mean, max, count等）  
**输出形状**: `(batch_size, 44)`  
**存在原因**: 捕获用户的行为模式和偏好

## 特征工程核心代码

### DataAnalyzer - 数据分析组件

```python
class DataAnalyzer:
    """data_analyzer.py中的数据分析器"""
    
    def analyze_dataset(self, data_dir, dataset_type):
        """
        分析单个数据集
        
        输入:
        - data_dir: 数据目录路径
        - dataset_type: train/validation/test
        
        处理:
        - 读取parquet文件
        - 计算统计信息（均值、方差、分位数）
        - 检测异常值和缺失值
        
        输出:
        - stats_dict: 统计信息字典
        """
        pass
    
    def get_normalization_params(self):
        """
        获取标准化参数
        
        输出:
        - mean: 特征均值向量
        - std: 特征标准差向量
        """
        pass
```

### FeatureManager - 特征管理器

```python
class FeatureManager:
    """feature_manager.py中的特征管理器"""
    
    def __init__(self):
        self.feature_groups = {
            'basic': list(range(8)),
            'user': list(range(8, 23)),
            'item': list(range(23, 38)),
            'context': list(range(38, 53)),
            'click': list(range(53, 63)),
            'session': list(range(63, 73)),
            'time': list(range(73, 83)),
            'income': list(range(83, 93)),
            'noise': list(range(93, 97))
        }
    
    def get_feature_indices(self, include_groups=None, exclude_groups=None):
        """
        获取特征索引
        
        输入:
        - include_groups: 要包含的特征组列表
        - exclude_groups: 要排除的特征组列表
        
        处理:
        - 根据包含/排除规则筛选特征
        - 生成特征索引列表
        
        输出:
        - indices: 特征索引列表
        """
        if include_groups:
            indices = []
            for group in include_groups:
                indices.extend(self.feature_groups.get(group, []))
        elif exclude_groups:
            indices = list(range(177))
            for group in exclude_groups:
                for idx in self.feature_groups.get(group, []):
                    indices.remove(idx)
        else:
            indices = list(range(177))
        
        return sorted(indices)
```

### IntelligentPreprocessor - 智能预处理器

```python
class IntelligentPreprocessor:
    """preprocess.py中的智能预处理器"""
    
    def process_parquet_chunk(self, file_path, chunk_size=10000):
        """
        处理Parquet文件块
        
        输入:
        - file_path: Parquet文件路径
        - chunk_size: 批处理大小
        
        处理:
        - 分块读取数据
        - 应用特征工程
        - 标准化处理
        
        输出:
        - features: (n_samples, 177) 特征数组
        - labels: (n_samples,) 标签数组
        """
        import pyarrow.parquet as pq
        
        table = pq.read_table(file_path)
        df = table.to_pandas()
        
        # 提取特征
        features = self._extract_features(df)
        
        # 标准化
        features = self._normalize_features(features)
        
        # 提取标签
        labels = df['conversion'].values
        
        return features, labels
    
    def _extract_features(self, df):
        """特征提取逻辑"""
        features = []
        
        # 基础特征
        basic = df[['age', 'gender', 'region', 'category', 
                   'price', 'brand', 'rating']].values
        features.append(basic)
        
        # 嵌入特征（这里简化为随机值，实际应该查表）
        embeddings = np.random.randn(len(df), 45)
        features.append(embeddings)
        
        # 行为特征
        behavior = self._extract_behavior_features(df)
        features.append(behavior)
        
        return np.hstack(features)
```

## 特征元数据管理

### save_feature_metadata.py

```python
def extract_feature_metadata():
    """
    提取并保存特征元数据
    
    处理流程:
    1. 扫描processed_data目录
    2. 读取NPY文件头信息
    3. 生成特征描述
    4. 保存为JSON格式
    """
    metadata = {
        "total_features": 177,
        "feature_groups": {
            "basic": {
                "indices": list(range(8)),
                "names": ["age", "gender", "region", "category", 
                         "price", "brand", "rating", "conversion"],
                "dtypes": ["float32"] * 8
            },
            "embeddings": {
                "indices": list(range(8, 53)),
                "dim": 45,
                "vocab_size": 100000
            },
            "behavior": {
                "indices": list(range(53, 97)),
                "aggregations": ["mean", "max", "count", "std"]
            },
            "cross": {
                "indices": list(range(97, 177)),
                "pairs": [("age", "category"), ("region", "brand"), ...]
            }
        },
        "normalization": {
            "method": "StandardScaler",
            "fitted_on": "train",
            "params_file": "scaler_params.pkl"
        }
    }
    
    with open("feature_metadata_expanded.json", "w") as f:
        json.dump(metadata, f, indent=2)
```

## 特征维度与模型输入关系

### 维度映射

| 特征类型 | 维度范围 | 特征数量 | 说明 |
|---------|---------|---------|------|
| 基础特征 | [0, 7] | 8 | 原始数值特征 |
| 用户嵌入 | [8, 22] | 15 | 用户ID的嵌入表示 |
| 物品嵌入 | [23, 37] | 15 | 物品ID的嵌入表示 |
| 上下文嵌入 | [38, 52] | 15 | 上下文的嵌入表示 |
| 点击特征 | [53, 62] | 10 | 点击行为统计 |
| 会话特征 | [63, 72] | 10 | 会话级别统计 |
| 时间特征 | [73, 82] | 10 | 时间相关特征 |
| 收入特征 | [83, 92] | 10 | 用户收入相关 |
| 噪声特征 | [93, 96] | 4 | 测试用噪声 |
| 交叉特征 | [97, 176] | 80 | 特征交叉结果 |

### 模型输入适配

```python
def prepare_model_input(features, feature_indices):
    """
    准备模型输入
    
    输入:
    - features: (batch_size, 177) 完整特征
    - feature_indices: 选中的特征索引
    
    输出:
    - selected_features: (batch_size, len(indices)) 选中特征
    """
    return features[:, feature_indices]

# 使用示例
feature_manager = FeatureManager()

# 只使用嵌入特征
indices = feature_manager.get_feature_indices(
    include_groups=['user', 'item', 'context']
)
model_input = prepare_model_input(features, indices)  # (batch, 45)

# 排除噪声特征
indices = feature_manager.get_feature_indices(
    exclude_groups=['noise']
)
model_input = prepare_model_input(features, indices)  # (batch, 173)
```

## 特征工程优化策略

### 1. 内存优化
- 使用float16代替float32（精度允许的情况下）
- 分块处理大文件，避免内存溢出
- 使用稀疏矩阵存储one-hot编码

### 2. 计算优化
- 向量化操作代替循环
- 使用NumPy/Pandas的内置函数
- 并行处理特征提取

### 3. 特征选择
- 基于重要性的特征选择
- 移除低方差特征
- 移除高相关性特征

**Why — 设计动机与取舍**

特征工程设计的核心考虑是平衡表达能力和计算效率。177维特征是经过多次实验确定的最佳维度，既能充分表达用户和物品特性，又不会造成维度灾难。特征分组机制允许灵活的特征选择，可以根据不同场景和数据特点调整特征组合。标准化处理确保了不同量纲特征的可比性，而特征交叉则捕获了特征间的非线性关系。整个流程设计为流式处理，支持大规模数据的高效处理。