"""Common data validation functions."""

import pandas as pd
from typing import List, Optional, Dict, <PERSON>ple


def validate_dataframe(
    df: pd.DataFrame,
    required_columns: List[str],
    *,
    non_null_columns: Optional[List[str]] = None,
    dtypes: Optional[Dict[str, str]] = None,
    ranges: Optional[Dict[str, Tuple[Optional[float], Optional[float]]]] = None
) -> None:
    """Validate DataFrame meets requirements.
    
    Args:
        df: DataFrame to validate
        required_columns: List of columns that must exist
        non_null_columns: Optional list of columns that must not have nulls
        dtypes: Optional dict of column -> expected dtype (placeholder for future)
        ranges: Optional dict of column -> (min, max) range (placeholder for future)
        
    Raises:
        ValueError: If validation fails
    """
    # Check required columns
    missing_columns = set(required_columns) - set(df.columns)
    if missing_columns:
        raise ValueError(f"Missing required columns: {list(missing_columns)}")
    
    # Check non-null columns
    if non_null_columns:
        for col in non_null_columns:
            if col in df.columns and df[col].isnull().any():
                null_count = df[col].isnull().sum()
                raise ValueError(f"Column '{col}' contains {null_count} null values")
    
    # Placeholder for future dtype checking
    if dtypes:
        # TODO: Implement dtype validation in future steps
        pass
    
    # Placeholder for future range checking
    if ranges:
        # TODO: Implement range validation in future steps
        pass