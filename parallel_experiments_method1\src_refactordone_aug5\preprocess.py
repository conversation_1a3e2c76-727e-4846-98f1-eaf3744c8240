# preprocess.py
"""
智能数据预处理脚本 - 并行处理版本
自动分析Parquet文件结构并进行预处理
支持自动检测列类型、数组列维度等
"""

import pandas as pd
import numpy as np
import glob
import os
import logging
import json
from multiprocessing import Pool
from functools import partial
import time
from pathlib import Path
from typing import Dict, List, Any, Optional

from config import (
    TRAIN_DATA_DIR, VALIDATION_DATA_DIR, TEST_DATA_DIR,
    PROCESSED_DATA_DIR, ANALYSIS_OUTPUT_DIR, NUM_WORKERS,
    USE_S3, is_s3_path, CHUNK_ROWS, PERFORMANCE_CONFIG, IS_UNIX
)
from data_analyzer import DataAnalyzer
from common import io as common_io
from common.data_checks import validate_dataframe
from common import features as common_features
from common.feature_registry import build_feature_spec, build_metadata

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 延迟输出启动信息，避免在TRACK模式下显示
def _log_startup_info():
    """延迟输出启动信息"""
    logging.info("=== Starting Intelligent Data Preprocessing ===")

class IntelligentPreprocessor:
    """
    智能预处理器 - 基于数据分析结果进行预处理
    """

    def __init__(self, analysis_results: Dict[str, Any]):
        """
        初始化预处理器

        Args:
            analysis_results: 数据分析结果
        """
        # 只在第一次创建时输出启动信息
        if not hasattr(IntelligentPreprocessor, '_startup_logged'):
            _log_startup_info()
            IntelligentPreprocessor._startup_logged = True

        self.analysis_results = analysis_results
        self.summary = analysis_results.get('summary', {})
        self.label_column = self.summary.get('detected_label_column')
        self.array_columns = self.summary.get('array_columns', [])
        self.numeric_columns = self.summary.get('numeric_columns', [])
        self.categorical_columns = self.summary.get('categorical_columns', [])
        
        # 获取数组列的维度信息
        self.array_dimensions = {}
        # Get the first dataset's columns_analysis (using 'train' as reference)
        columns_analysis = None
        if 'datasets' in analysis_results:
            for dataset_name, dataset_info in analysis_results['datasets'].items():
                if 'columns_analysis' in dataset_info:
                    columns_analysis = dataset_info['columns_analysis']
                    break
        
        if columns_analysis:
            for col in self.array_columns:
                col_info = columns_analysis.get(col, {})
                self.array_dimensions[col] = col_info.get('array_length', 0)
        else:
            logging.warning("Could not find columns_analysis in analysis results")
            
        # Initialize feature metadata
        self.excluded_columns = ["user_id", "item_id", "timestamp"]
        self.feature_metadata = {
            'features': [],
            'groups': {},
            'total_features': 0,
            'label_column': self.label_column,
            'excluded_columns': self.excluded_columns,
            'original_columns': [],  # Will be filled from first parquet file
            'npy_shape': []  # Will be filled after processing
        }
        self.feature_index = 0
        self.metadata_generated = False
        
        # Build metadata structure
        self._build_metadata_structure()
            
        logging.info(f"预处理器初始化完成:")
        logging.info(f"  标签列: {self.label_column}")
        logging.info(f"  数组列: {len(self.array_columns)} 个")
        logging.info(f"  数值列: {len(self.numeric_columns)} 个")
        logging.info(f"  类别列: {len(self.categorical_columns)} 个")
        logging.info(f"  预计特征数: {self.feature_index}")
    
    def _build_metadata_structure(self):
        """Build metadata structure using common registry"""
        # Build feature spec
        spec = build_feature_spec(
            numeric_columns=[col for col in self.numeric_columns 
                           if col != self.label_column and col not in self.excluded_columns],
            categorical_columns=[col for col in self.categorical_columns 
                               if col != self.label_column and col not in self.excluded_columns],
            array_columns=[col for col in self.array_columns 
                         if col != self.label_column and col not in self.excluded_columns],
            array_dimensions=self.array_dimensions,
            label_column=self.label_column
        )
        
        # Build metadata
        self.feature_metadata = build_metadata(spec)
        self.feature_index = self.feature_metadata['total_features']
    
    # Methods _add_feature_to_metadata and _add_expanded_feature_to_metadata removed
    # Now using feature_registry.build_metadata() in _build_metadata_structure()
    
    def save_metadata(self, dataset_name: str, actual_shape: tuple):
        """Save metadata after processing first dataset"""
        if dataset_name == 'train' and not self.metadata_generated:
            self.feature_metadata['total_features'] = self.feature_index
            self.feature_metadata['npy_shape'] = list(actual_shape)
            
            # Verify feature count matches
            if actual_shape[1] != self.feature_index:
                logging.warning(f"Feature count mismatch: metadata={self.feature_index}, actual={actual_shape[1]}")
                # Adjust if needed
                self.feature_metadata['total_features'] = actual_shape[1]
            
            metadata_file = os.path.join(PROCESSED_DATA_DIR, 'feature_metadata_expanded.json')
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.feature_metadata, f, indent=2, ensure_ascii=False)
            
            logging.info(f"✓ Feature metadata saved: {metadata_file}")
            logging.info(f"  Total features: {self.feature_metadata['total_features']}")
            logging.info(f"  Feature groups: {len(self.feature_metadata['groups'])}")
            
            # Log group summary
            for group, indices in sorted(self.feature_metadata['groups'].items()):
                group_name = group if group else "(no prefix)"
                logging.info(f"    {group_name}: {len(indices)} features")
            
            self.metadata_generated = True

    def process_parquet_chunk(self, file_path: str) -> Optional[tuple]:
        """
        处理单个parquet文件块
        
        Args:
            file_path: parquet文件路径
            
        Returns:
            tuple: (features_array, labels_array) 或 None
        """
        try:
            logging.debug(f"处理文件: {file_path}")
            
            # 读取数据
            df = common_io.read_parquet(file_path)
            if df is None or df.empty:
                logging.warning(f"文件为空或读取失败: {file_path}")
                return None
            
            # Capture original columns from first file
            if not self.feature_metadata['original_columns'] and not df.empty:
                self.feature_metadata['original_columns'] = list(df.columns)
                logging.debug(f"Captured original columns: {len(df.columns)} columns")
                
            # 预处理数据
            features, labels = self._preprocess_dataframe(df)
            
            if features is not None and labels is not None:
                logging.debug(f"文件处理成功: {file_path}, 特征形状: {features.shape}, 标签形状: {labels.shape}")
                return features, labels
            else:
                logging.warning(f"数据预处理失败: {file_path}")
                return None
                
        except Exception as e:
            logging.error(f"处理文件时发生错误 {file_path}: {e}")
            return None

    def _preprocess_dataframe(self, df: pd.DataFrame) -> tuple:
        """
        预处理DataFrame
        
        Args:
            df: 输入DataFrame
            
        Returns:
            tuple: (features_array, labels_array)
        """
        try:
            # Validate required columns
            if self.label_column:
                try:
                    validate_dataframe(df, required_columns=[self.label_column])
                except ValueError as e:
                    logging.error(f"标签列 {self.label_column} 不存在")
                    return None, None
            
            # 提取标签
            labels = df[self.label_column].values.astype(np.int64)
            
            # 准备特征配置
            feature_config = {
                'numeric_columns': self.numeric_columns,
                'categorical_columns': self.categorical_columns,
                'array_columns': self.array_columns,
                'array_dimensions': self.array_dimensions,
                'label_column': self.label_column
            }
            
            # 调用common模块的transform_features
            features = common_features.transform_features(df, feature_config=feature_config)
            
            if features is not None:
                return features, labels
            else:
                logging.error("没有有效的特征列")
                return None, None
                
        except Exception as e:
            logging.error(f"DataFrame预处理失败: {e}")
            return None, None



def load_analysis_results() -> Optional[Dict[str, Any]]:
    """加载数据分析结果"""
    analysis_file = os.path.join(ANALYSIS_OUTPUT_DIR, "data_analysis_results.json")
    
    if os.path.exists(analysis_file):
        try:
            with open(analysis_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logging.error(f"加载分析结果失败: {e}")
            return None
    else:
        logging.warning(f"分析结果文件不存在: {analysis_file}")
        return None


def preprocess_all_datasets():
    """预处理所有数据集"""
    logging.info("开始预处理所有数据集...")
    
    # 加载分析结果
    analysis_results = load_analysis_results()
    if not analysis_results:
        logging.error("无法加载数据分析结果，请先运行数据分析")
        return False
        
    # 创建预处理器
    preprocessor = IntelligentPreprocessor(analysis_results)
    
    # 处理各个数据集
    datasets = {
        'train': TRAIN_DATA_DIR,
        'validation': VALIDATION_DATA_DIR,
        'test': TEST_DATA_DIR
    }
    
    for dataset_name, data_dir in datasets.items():
        if data_dir and common_io.path_exists(data_dir):
            logging.info(f"处理 {dataset_name} 数据集...")
            success = process_single_dataset(preprocessor, data_dir, dataset_name)
            if success:
                logging.info(f"{dataset_name} 数据集处理完成")
            else:
                logging.error(f"{dataset_name} 数据集处理失败")
        else:
            logging.warning(f"跳过 {dataset_name} 数据集 (路径不存在)")
            
    logging.info("所有数据集预处理完成")
    return True


def process_single_dataset(preprocessor: IntelligentPreprocessor, 
                          data_dir: str, dataset_name: str) -> bool:
    """处理单个数据集"""
    try:
        # 获取所有parquet文件
        file_paths = common_io.list_parquet_files(data_dir)
        if not file_paths:
            logging.error(f"在 {data_dir} 中未找到parquet文件")
            return False
            
        logging.info(f"找到 {len(file_paths)} 个parquet文件")
        
        # 处理所有文件
        all_features = []
        all_labels = []
        
        for file_path in file_paths:
            result = preprocessor.process_parquet_chunk(file_path)
            if result is not None:
                features, labels = result
                all_features.append(features)
                all_labels.append(labels)
                
        # 合并结果
        if all_features and all_labels:
            final_features = np.concatenate(all_features, axis=0)
            final_labels = np.concatenate(all_labels, axis=0)
            
            # 保存结果
            common_io.ensure_dir(PROCESSED_DATA_DIR)
            feature_file = os.path.join(PROCESSED_DATA_DIR, f"{dataset_name}_features.npy")
            label_file = os.path.join(PROCESSED_DATA_DIR, f"{dataset_name}_labels.npy")
            
            common_io.save_npy(final_features.astype(np.float32), feature_file)
            common_io.save_npy(final_labels.astype(np.int64), label_file)
            
            logging.info(f"数据保存成功:")
            logging.info(f"  特征文件: {feature_file} (形状: {final_features.shape})")
            logging.info(f"  标签文件: {label_file} (形状: {final_labels.shape})")
            
            # Save metadata after processing train dataset
            preprocessor.save_metadata(dataset_name, final_features.shape)
            
            return True
        else:
            logging.error("没有成功处理的数据")
            return False
            
    except Exception as e:
        logging.error(f"处理数据集时发生错误: {e}")
        return False


def main():
    """主函数"""
    success = preprocess_all_datasets()
    if success:
        logging.info("数据预处理成功完成")
    else:
        logging.error("数据预处理失败")


if __name__ == "__main__":
    main()
