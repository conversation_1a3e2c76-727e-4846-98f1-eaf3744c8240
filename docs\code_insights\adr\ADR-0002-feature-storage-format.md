# ADR-0002: 特征存储格式选择

## 状态
已采纳

## 上下文
处理TB级推荐数据时，需要选择合适的数据存储格式。主要考虑：
- 原始数据：Parquet格式（来自数据平台）
- 中间处理：需要高效的读写
- 模型输入：需要快速加载到内存

候选格式包括：
- CSV/TSV：文本格式
- Parquet：列式存储
- NPY/NPZ：NumPy二进制格式
- HDF5：层次化数据格式
- TFRecord：TensorFlow格式
- Feather：快速列式格式

## 决策
采用分层存储策略：
1. **原始数据**：保持Parquet格式
2. **预处理后数据**：使用NPY格式
3. **元数据**：使用JSON格式

## 原因

### 为什么选择NPY作为预处理后格式？

1. **加载速度最快**
   ```
   格式     | 1GB数据加载时间 | 内存占用
   ---------|----------------|----------
   CSV      | 45s            | 2.5GB
   Parquet  | 8s             | 1.2GB
   NPY      | 0.5s           | 1.0GB
   HDF5     | 2s             | 1.1GB
   ```

2. **与PyTorch无缝集成**
   ```python
   # 直接加载为张量
   features = np.load('features.npy')
   tensor = torch.from_numpy(features)
   ```

3. **存储效率高**
   - 二进制格式，无解析开销
   - 支持内存映射（mmap）
   - 压缩率适中

4. **简单可靠**
   - NumPy原生格式，稳定性好
   - 无需额外依赖
   - 跨平台兼容

### 为什么保留Parquet作为原始格式？

1. **列式存储优势**
   - 压缩率高（相比CSV减少70%）
   - 支持谓词下推
   - 可以只读取需要的列

2. **生态系统支持**
   - Spark/Pandas原生支持
   - AWS Athena可直接查询
   - 数据湖标准格式

### 为什么用JSON存储元数据？

1. **人类可读**
   - 便于调试和检查
   - 版本控制友好

2. **灵活性高**
   - 易于添加新字段
   - 支持嵌套结构

## 存储架构

```
data/
├── raw/                        # 原始Parquet文件
│   ├── train/
│   │   ├── part-00000.parquet
│   │   └── ...
│   └── validation/
│
├── processed/                  # NPY格式
│   ├── train/
│   │   ├── features.npy       # (n_samples, 177)
│   │   └── labels.npy         # (n_samples,)
│   └── validation/
│
└── metadata/                   # JSON元数据
    ├── feature_metadata.json
    ├── data_stats.json
    └── normalization_params.json
```

## 格式转换流程

```python
# Parquet → NPY
def convert_parquet_to_npy(parquet_path, output_dir):
    # 1. 读取Parquet
    df = pd.read_parquet(parquet_path)
    
    # 2. 特征工程
    features = extract_features(df)
    labels = df['conversion'].values
    
    # 3. 保存为NPY
    np.save(f'{output_dir}/features.npy', features)
    np.save(f'{output_dir}/labels.npy', labels)
    
    # 4. 生成元数据
    metadata = {
        'n_samples': len(features),
        'n_features': features.shape[1],
        'dtype': str(features.dtype),
        'timestamp': datetime.now().isoformat()
    }
    
    with open(f'{output_dir}/metadata.json', 'w') as f:
        json.dump(metadata, f)
```

## 性能对比

### 存储效率
| 格式 | 1M样本大小 | 压缩率 |
|------|------------|---------|
| CSV | 850MB | 0% |
| Parquet | 250MB | 70% |
| NPY | 680MB | 20% |
| NPZ(压缩) | 340MB | 60% |
| HDF5 | 380MB | 55% |

### 加载性能
| 格式 | 加载时间 | 内存峰值 |
|------|----------|----------|
| CSV | 45s | 2.5x |
| Parquet | 8s | 1.2x |
| NPY | 0.5s | 1.0x |
| NPY(mmap) | 0.1s | 0.1x |
| HDF5 | 2s | 1.1x |

## 后果

### 积极后果
- 训练数据加载提速90倍
- 内存使用减少60%
- 支持超大数据集（通过mmap）
- 简化数据管道

### 消极后果
- 需要预处理步骤
- NPY文件较大（未压缩）
- 失去Parquet的查询能力
- 需要维护两份数据

## 缓解措施

1. **存储成本**
   - 定期清理中间NPY文件
   - 只保留最新版本
   - 使用S3生命周期策略

2. **数据一致性**
   - 元数据记录版本信息
   - 哈希值校验
   - 自动化转换流程

## 替代方案

### 未选方案1：全部使用Parquet
- 优点：单一格式，无需转换
- 缺点：加载速度慢16倍
- 未选原因：训练时I/O成为瓶颈

### 未选方案2：使用HDF5
- 优点：支持分组和属性
- 缺点：需要额外依赖，并发读写复杂
- 未选原因：NPY更简单且足够用

### 未选方案3：使用TFRecord
- 优点：TensorFlow生态
- 缺点：PyTorch支持差，格式复杂
- 未选原因：我们使用PyTorch

## 实施指南

### 数据转换命令
```bash
# 运行并行预处理
python src/run_parallel_processing.py --workers 88

# 验证NPY文件
python src/check_data.py
```

### 内存映射使用
```python
# 大文件使用mmap
features = np.load('features.npy', mmap_mode='r')
# 只有访问时才加载到内存
batch = features[0:1024]  
```

## 参考
- [1] NumPy文档：https://numpy.org/doc/stable/reference/generated/numpy.save.html
- [2] 性能测试报告 benchmark_results_2024_07.md
- [3] Apache Parquet规范：https://parquet.apache.org/