# DataLoader架构与特征转换详细分析

## 概述

DataLoader是深度学习训练流程中的关键组件，负责高效地加载、转换和批处理数据。本文档详细说明推荐系统中DataLoader的设计架构、特征转换流程、以及各种优化策略。

## 1. DataLoader的核心职责

### 1.1 主要功能

```
数据源 → 索引管理 → 采样策略 → 批处理 → 特征转换 → 内存管理 → 输出批次
```

1. **数据索引管理**：维护数据集的索引，支持随机访问
2. **采样策略**：随机采样、顺序采样、加权采样等
3. **批处理组装**：将多个样本组合成批次
4. **特征转换**：动态特征工程和预处理
5. **内存优化**：预取、缓存、内存映射
6. **并行加载**：多进程/多线程数据加载

### 1.2 数据流程图

```
┌──────────────────┐
│   原始数据文件    │
│  (CSV/Parquet)   │
└────────┬─────────┘
         │
         ▼
┌──────────────────┐
│  预处理数据缓存   │
│   (HDF5/NPZ)     │
└────────┬─────────┘
         │
         ▼
┌──────────────────┐
│    Dataset类     │
│  (索引和元数据)   │
└────────┬─────────┘
         │
         ▼
┌──────────────────┐
│   DataLoader     │
│  (批处理和并行)   │
└────────┬─────────┘
         │
         ▼
┌──────────────────┐
│   特征转换器     │
│  (实时转换)      │
└────────┬─────────┘
         │
         ▼
┌──────────────────┐
│    模型输入      │
│  (Tensor批次)    │
└──────────────────┘
```

## 2. Dataset类的实现

### 2.1 基础Dataset设计

```python
import torch
from torch.utils.data import Dataset
import numpy as np
import pandas as pd
import h5py
import json
from pathlib import Path

class RecommendationDataset(Dataset):
    def __init__(self, data_path, mode='train', transform=None):
        """
        data_path: 数据目录路径
        mode: 'train', 'valid', 或 'test'
        transform: 特征转换函数
        """
        self.data_path = Path(data_path)
        self.mode = mode
        self.transform = transform
        
        # 加载元数据
        self._load_metadata()
        
        # 初始化数据访问
        self._init_data_access()
        
        # 加载特征配置
        self._load_feature_config()
    
    def _load_metadata(self):
        """
        加载数据集元数据
        """
        metadata_file = self.data_path / 'metadata.json'
        with open(metadata_file, 'r') as f:
            self.metadata = json.load(f)
        
        # 获取数据集统计信息
        self.n_samples = self.metadata[f'{self.mode}_samples']
        self.n_features = self.metadata['n_features']  # 177个特征
        self.feature_names = self.metadata['feature_names']
        self.feature_types = self.metadata['feature_types']
        
        print(f"加载{self.mode}数据集: {self.n_samples}个样本, {self.n_features}个特征")
    
    def _init_data_access(self):
        """
        初始化数据访问方式
        根据数据大小选择合适的加载策略
        """
        data_file = self.data_path / f'{self.mode}_data.h5'
        
        if self.n_samples * self.n_features * 4 < 1e9:  # 小于1GB
            # 全部加载到内存
            self.data_access_mode = 'memory'
            self._load_all_data()
        else:
            # 使用HDF5延迟加载
            self.data_access_mode = 'lazy'
            self.h5_file = h5py.File(data_file, 'r')
            self.features_dataset = self.h5_file['features']
            self.labels_dataset = self.h5_file['labels']
    
    def _load_all_data(self):
        """
        将所有数据加载到内存
        """
        data_file = self.data_path / f'{self.mode}_data.h5'
        with h5py.File(data_file, 'r') as f:
            self.features = np.array(f['features'])
            self.labels = np.array(f['labels'])
    
    def _load_feature_config(self):
        """
        加载特征配置
        """
        config_file = self.data_path / 'feature_metadata.json'
        with open(config_file, 'r') as f:
            self.feature_config = json.load(f)
        
        # 解析特征组
        self.numeric_features = []
        self.categorical_features = []
        self.array_features = []
        
        for feature_name, config in self.feature_config['features'].items():
            if config['type'] == 'numeric':
                self.numeric_features.append(feature_name)
            elif config['type'] == 'categorical':
                self.categorical_features.append(feature_name)
            elif config['type'] == 'array':
                self.array_features.append(feature_name)
    
    def __len__(self):
        return self.n_samples
    
    def __getitem__(self, idx):
        """
        获取单个样本
        """
        # 获取原始数据
        if self.data_access_mode == 'memory':
            features = self.features[idx]
            label = self.labels[idx]
        else:
            features = self.features_dataset[idx]
            label = self.labels_dataset[idx]
        
        # 构造样本字典
        sample = {
            'features': features,
            'label': label,
            'index': idx
        }
        
        # 应用特征转换
        if self.transform:
            sample = self.transform(sample)
        
        return sample
    
    def close(self):
        """
        清理资源
        """
        if hasattr(self, 'h5_file'):
            self.h5_file.close()
```

### 2.2 高级Dataset功能

```python
class AdvancedRecommendationDataset(Dataset):
    def __init__(self, data_path, feature_processor=None, cache_size=10000):
        self.data_path = data_path
        self.feature_processor = feature_processor
        
        # LRU缓存
        from functools import lru_cache
        self.cache_size = cache_size
        self._cached_getitem = lru_cache(maxsize=cache_size)(self._get_processed_item)
        
        # 负采样配置
        self.negative_sampling_ratio = 4
        self.user_item_matrix = self._build_user_item_matrix()
    
    def _build_user_item_matrix(self):
        """
        构建用户-物品交互矩阵，用于负采样
        """
        interactions = pd.read_csv(f"{self.data_path}/interactions.csv")
        from scipy.sparse import csr_matrix
        
        # 创建稀疏矩阵
        user_ids = interactions['user_id'].values
        item_ids = interactions['item_id'].values
        values = np.ones(len(interactions))
        
        n_users = interactions['user_id'].max() + 1
        n_items = interactions['item_id'].max() + 1
        
        matrix = csr_matrix(
            (values, (user_ids, item_ids)),
            shape=(n_users, n_items)
        )
        
        return matrix
    
    def _get_processed_item(self, idx):
        """
        获取并处理单个样本（可缓存）
        """
        # 基础特征
        features = self._load_features(idx)
        
        # 特征工程
        if self.feature_processor:
            features = self.feature_processor.process(features)
        
        # 负采样（如果需要）
        if self.training:
            negative_samples = self._generate_negative_samples(idx)
            features['negative_items'] = negative_samples
        
        return features
    
    def _generate_negative_samples(self, idx):
        """
        生成负样本
        """
        user_id = self._get_user_id(idx)
        positive_items = self.user_item_matrix[user_id].nonzero()[1]
        
        all_items = np.arange(self.user_item_matrix.shape[1])
        negative_candidates = np.setdiff1d(all_items, positive_items)
        
        # 随机选择负样本
        n_negatives = min(
            self.negative_sampling_ratio,
            len(negative_candidates)
        )
        negative_samples = np.random.choice(
            negative_candidates,
            size=n_negatives,
            replace=False
        )
        
        return negative_samples
    
    def __getitem__(self, idx):
        """
        使用缓存的getitem
        """
        return self._cached_getitem(idx)
```

## 3. 特征转换流水线

### 3.1 特征转换器设计

```python
class FeatureTransformer:
    def __init__(self, feature_config):
        self.feature_config = feature_config
        self.transforms = self._build_transform_pipeline()
    
    def _build_transform_pipeline(self):
        """
        构建特征转换流水线
        """
        transforms = []
        
        # 数值特征标准化
        transforms.append(NumericNormalizer(self.feature_config['numeric_features']))
        
        # 类别特征编码
        transforms.append(CategoricalEncoder(self.feature_config['categorical_features']))
        
        # 数组特征展开
        transforms.append(ArrayExpander(self.feature_config['array_features']))
        
        # 特征交叉
        transforms.append(FeatureCrosser(self.feature_config['cross_features']))
        
        # 缺失值处理
        transforms.append(MissingValueHandler())
        
        return transforms
    
    def transform(self, sample):
        """
        应用所有转换
        """
        for transform in self.transforms:
            sample = transform(sample)
        return sample

class NumericNormalizer:
    def __init__(self, feature_indices, stats_file='feature_stats.json'):
        self.feature_indices = feature_indices
        
        # 加载统计信息
        with open(stats_file, 'r') as f:
            stats = json.load(f)
        
        self.means = np.array([stats['means'][i] for i in feature_indices])
        self.stds = np.array([stats['stds'][i] for i in feature_indices])
    
    def __call__(self, sample):
        """
        标准化数值特征
        """
        features = sample['features']
        
        # 向量化标准化
        features[self.feature_indices] = (
            features[self.feature_indices] - self.means
        ) / (self.stds + 1e-8)
        
        sample['features'] = features
        return sample

class CategoricalEncoder:
    def __init__(self, feature_config):
        self.feature_config = feature_config
        self.embeddings = {}
        
        # 初始化embedding查找表
        for feature_name, config in feature_config.items():
            vocab_size = config['vocab_size']
            embedding_dim = config['embedding_dim']
            
            # 创建可学习的embedding
            self.embeddings[feature_name] = nn.Embedding(
                vocab_size, 
                embedding_dim
            )
    
    def __call__(self, sample):
        """
        编码类别特征
        """
        features = sample['features']
        encoded_features = []
        
        for feature_name, embedding in self.embeddings.items():
            feature_idx = self.feature_config[feature_name]['index']
            cat_value = int(features[feature_idx])
            
            # 获取embedding
            embedded = embedding(torch.LongTensor([cat_value]))
            encoded_features.append(embedded.squeeze(0))
        
        # 拼接所有embedding
        sample['categorical_embeddings'] = torch.cat(encoded_features)
        
        return sample

class ArrayExpander:
    def __init__(self, array_features_config):
        self.config = array_features_config
    
    def __call__(self, sample):
        """
        展开数组特征
        """
        features = sample['features']
        expanded_features = []
        
        for feature_name, config in self.config.items():
            feature_idx = config['index']
            max_length = config['max_length']
            
            # 获取数组
            array = features[feature_idx]
            
            # 截断或填充
            if len(array) > max_length:
                array = array[:max_length]
            else:
                padding = np.zeros(max_length - len(array))
                array = np.concatenate([array, padding])
            
            expanded_features.append(array)
        
        # 更新特征
        sample['array_features'] = np.concatenate(expanded_features)
        
        return sample

class FeatureCrosser:
    def __init__(self, cross_config):
        self.cross_config = cross_config
    
    def __call__(self, sample):
        """
        创建交叉特征
        """
        features = sample['features']
        cross_features = []
        
        for cross_spec in self.cross_config:
            feature1_idx = cross_spec['feature1_idx']
            feature2_idx = cross_spec['feature2_idx']
            
            # 计算交叉（可以是乘积、拼接等）
            if cross_spec['type'] == 'multiply':
                cross = features[feature1_idx] * features[feature2_idx]
            elif cross_spec['type'] == 'concatenate':
                cross = np.array([features[feature1_idx], features[feature2_idx]])
            
            cross_features.append(cross)
        
        sample['cross_features'] = np.array(cross_features)
        
        return sample
```

### 3.2 动态特征生成

```python
class DynamicFeatureGenerator:
    def __init__(self, user_history_db, item_features_db):
        self.user_history_db = user_history_db
        self.item_features_db = item_features_db
    
    def generate_realtime_features(self, user_id, item_id, timestamp):
        """
        生成实时特征
        """
        features = {}
        
        # 用户最近行为特征
        recent_actions = self.get_user_recent_actions(user_id, timestamp)
        features.update(self.extract_action_features(recent_actions))
        
        # 用户-物品相似度特征
        similarity = self.calculate_user_item_similarity(user_id, item_id)
        features['user_item_similarity'] = similarity
        
        # 时间上下文特征
        time_features = self.extract_time_features(timestamp)
        features.update(time_features)
        
        # 物品流行度特征
        popularity = self.get_item_popularity(item_id, timestamp)
        features['item_popularity'] = popularity
        
        return features
    
    def get_user_recent_actions(self, user_id, timestamp, window_hours=24):
        """
        获取用户最近的行为
        """
        end_time = timestamp
        start_time = timestamp - pd.Timedelta(hours=window_hours)
        
        query = f"""
        SELECT action_type, item_id, timestamp
        FROM user_actions
        WHERE user_id = {user_id}
          AND timestamp BETWEEN '{start_time}' AND '{end_time}'
        ORDER BY timestamp DESC
        LIMIT 100
        """
        
        return pd.read_sql(query, self.user_history_db)
    
    def extract_action_features(self, actions):
        """
        从用户行为中提取特征
        """
        features = {}
        
        if len(actions) == 0:
            return {
                'recent_click_count': 0,
                'recent_purchase_count': 0,
                'recent_view_count': 0,
                'time_since_last_action': 999999
            }
        
        # 行为计数
        features['recent_click_count'] = (actions['action_type'] == 'click').sum()
        features['recent_purchase_count'] = (actions['action_type'] == 'purchase').sum()
        features['recent_view_count'] = (actions['action_type'] == 'view').sum()
        
        # 时间特征
        features['time_since_last_action'] = (
            pd.Timestamp.now() - actions['timestamp'].max()
        ).total_seconds() / 3600  # 小时
        
        # 多样性特征
        features['recent_category_diversity'] = actions['item_id'].nunique()
        
        return features
    
    def calculate_user_item_similarity(self, user_id, item_id):
        """
        计算用户-物品相似度
        """
        # 获取用户历史喜欢的物品
        user_liked_items = self.get_user_liked_items(user_id)
        
        if len(user_liked_items) == 0:
            return 0.0
        
        # 获取物品特征向量
        target_item_features = self.item_features_db[item_id]
        liked_items_features = [
            self.item_features_db[item] 
            for item in user_liked_items
        ]
        
        # 计算余弦相似度
        similarities = []
        for liked_features in liked_items_features:
            sim = cosine_similarity(target_item_features, liked_features)
            similarities.append(sim)
        
        # 返回平均相似度
        return np.mean(similarities)
```

## 4. DataLoader的优化实现

### 4.1 智能批处理策略

```python
class SmartDataLoader:
    def __init__(self, dataset, batch_size=32, shuffle=True):
        self.dataset = dataset
        self.base_batch_size = batch_size
        self.shuffle = shuffle
        
        # 动态批大小管理
        self.dynamic_batch_size = batch_size
        self.memory_monitor = MemoryMonitor()
        
        # 采样策略
        self.sampler = self._create_sampler()
        
        # 预取队列
        self.prefetch_queue = queue.Queue(maxsize=3)
        self.prefetch_thread = None
    
    def _create_sampler(self):
        """
        创建采样器
        """
        if self.shuffle:
            # 加权随机采样（处理类别不平衡）
            weights = self._calculate_sample_weights()
            return WeightedRandomSampler(weights, len(self.dataset))
        else:
            return SequentialSampler(self.dataset)
    
    def _calculate_sample_weights(self):
        """
        计算样本权重（用于处理不平衡数据）
        """
        labels = []
        for i in range(len(self.dataset)):
            labels.append(self.dataset[i]['label'])
        
        # 计算类别权重
        from collections import Counter
        label_counts = Counter(labels)
        total = len(labels)
        
        weights = []
        for label in labels:
            # 逆频率加权
            weight = total / (len(label_counts) * label_counts[label])
            weights.append(weight)
        
        return torch.FloatTensor(weights)
    
    def _adjust_batch_size(self):
        """
        根据内存使用动态调整批大小
        """
        memory_usage = self.memory_monitor.get_current_usage()
        
        if memory_usage > 0.9:  # 内存使用超过90%
            self.dynamic_batch_size = max(1, self.dynamic_batch_size // 2)
            print(f"内存紧张，批大小减小到: {self.dynamic_batch_size}")
        elif memory_usage < 0.5:  # 内存使用低于50%
            self.dynamic_batch_size = min(
                self.base_batch_size * 2,
                self.dynamic_batch_size * 2
            )
            print(f"内存充足，批大小增加到: {self.dynamic_batch_size}")
    
    def _prefetch_worker(self):
        """
        预取工作线程
        """
        for batch in self._generate_batches():
            self.prefetch_queue.put(batch)
    
    def _generate_batches(self):
        """
        生成批次
        """
        batch = []
        for idx in self.sampler:
            batch.append(self.dataset[idx])
            
            if len(batch) >= self.dynamic_batch_size:
                yield self._collate_batch(batch)
                batch = []
                
                # 调整批大小
                self._adjust_batch_size()
        
        # 最后一个批次
        if batch:
            yield self._collate_batch(batch)
    
    def _collate_batch(self, batch):
        """
        整理批次数据
        """
        # 提取特征和标签
        features = torch.stack([
            torch.FloatTensor(item['features']) 
            for item in batch
        ])
        labels = torch.stack([
            torch.FloatTensor([item['label']]) 
            for item in batch
        ])
        
        # 额外的批处理
        if 'categorical_embeddings' in batch[0]:
            cat_embeddings = torch.stack([
                item['categorical_embeddings'] 
                for item in batch
            ])
        else:
            cat_embeddings = None
        
        return {
            'features': features,
            'labels': labels,
            'categorical_embeddings': cat_embeddings,
            'batch_size': len(batch)
        }
    
    def start_prefetching(self):
        """
        启动预取
        """
        self.prefetch_thread = threading.Thread(target=self._prefetch_worker)
        self.prefetch_thread.daemon = True
        self.prefetch_thread.start()
    
    def __iter__(self):
        """
        迭代器接口
        """
        self.start_prefetching()
        
        while not self.prefetch_queue.empty() or self.prefetch_thread.is_alive():
            try:
                batch = self.prefetch_queue.get(timeout=1)
                yield batch
            except queue.Empty:
                continue
    
    def __len__(self):
        return len(self.dataset) // self.dynamic_batch_size
```

### 4.2 混合精度数据加载

```python
class MixedPrecisionDataLoader:
    def __init__(self, dataset, use_fp16=True):
        self.dataset = dataset
        self.use_fp16 = use_fp16
        
        # 精度配置
        self.feature_precisions = self._determine_feature_precisions()
    
    def _determine_feature_precisions(self):
        """
        为不同特征确定合适的精度
        """
        precisions = {}
        
        for feature_name, config in self.dataset.feature_config.items():
            if config['type'] == 'categorical':
                # 类别特征使用int8
                precisions[feature_name] = np.int8
            elif config['type'] == 'numeric':
                if config.get('high_precision', False):
                    # 需要高精度的特征用float32
                    precisions[feature_name] = np.float32
                else:
                    # 一般数值特征用float16
                    precisions[feature_name] = np.float16
            elif config['type'] == 'binary':
                # 二值特征用bool
                precisions[feature_name] = np.bool_
        
        return precisions
    
    def convert_batch_precision(self, batch):
        """
        转换批次数据精度
        """
        if not self.use_fp16:
            return batch
        
        # 转换特征精度
        features = batch['features']
        converted_features = []
        
        for i, (feature_name, precision) in enumerate(self.feature_precisions.items()):
            feature_values = features[:, i]
            
            if precision == np.float16:
                # 转换为半精度
                feature_values = feature_values.astype(np.float16)
            elif precision == np.int8:
                # 转换为int8
                feature_values = feature_values.astype(np.int8)
            
            converted_features.append(feature_values)
        
        batch['features'] = np.stack(converted_features, axis=1)
        
        return batch
```

## 5. 特殊数据类型处理

### 5.1 序列特征处理

```python
class SequenceFeatureHandler:
    def __init__(self, max_sequence_length=50):
        self.max_sequence_length = max_sequence_length
    
    def process_sequence_batch(self, sequences):
        """
        处理序列特征批次
        """
        batch_size = len(sequences)
        
        # 获取实际序列长度
        lengths = [len(seq) for seq in sequences]
        max_len = min(max(lengths), self.max_sequence_length)
        
        # 创建填充的张量
        padded = torch.zeros(batch_size, max_len, dtype=torch.long)
        
        # 填充序列
        for i, seq in enumerate(sequences):
            seq_len = min(len(seq), max_len)
            padded[i, :seq_len] = torch.LongTensor(seq[:seq_len])
        
        # 创建mask
        mask = torch.zeros(batch_size, max_len, dtype=torch.bool)
        for i, length in enumerate(lengths):
            mask[i, :min(length, max_len)] = 1
        
        return {
            'sequences': padded,
            'lengths': torch.LongTensor(lengths),
            'mask': mask
        }

class GraphFeatureHandler:
    def __init__(self):
        self.edge_index_cache = {}
    
    def process_graph_batch(self, graphs):
        """
        处理图结构特征
        """
        from torch_geometric.data import Batch
        
        # 将多个图合并为批次
        batch = Batch.from_data_list(graphs)
        
        return {
            'node_features': batch.x,
            'edge_index': batch.edge_index,
            'edge_attr': batch.edge_attr,
            'batch': batch.batch
        }
```

### 5.2 多模态特征处理

```python
class MultiModalDataLoader:
    def __init__(self, text_features, image_features, numeric_features):
        self.text_processor = TextFeatureProcessor()
        self.image_processor = ImageFeatureProcessor()
        self.numeric_processor = NumericFeatureProcessor()
    
    def process_multimodal_batch(self, batch):
        """
        处理多模态批次数据
        """
        processed = {}
        
        # 处理文本特征
        if 'text' in batch:
            text_features = self.text_processor.process(batch['text'])
            processed['text_embeddings'] = text_features
        
        # 处理图像特征
        if 'images' in batch:
            image_features = self.image_processor.process(batch['images'])
            processed['image_features'] = image_features
        
        # 处理数值特征
        if 'numeric' in batch:
            numeric_features = self.numeric_processor.process(batch['numeric'])
            processed['numeric_features'] = numeric_features
        
        # 特征融合
        fused_features = self.fuse_features(processed)
        processed['fused_features'] = fused_features
        
        return processed
    
    def fuse_features(self, features_dict):
        """
        融合多模态特征
        """
        all_features = []
        
        for modality, features in features_dict.items():
            # 归一化到相同维度
            normalized = self.normalize_features(features)
            all_features.append(normalized)
        
        # 拼接或注意力融合
        fused = torch.cat(all_features, dim=-1)
        
        return fused
```

## 6. 性能监控和调试

### 6.1 DataLoader性能分析

```python
class DataLoaderProfiler:
    def __init__(self, dataloader):
        self.dataloader = dataloader
        self.metrics = {
            'batch_load_times': [],
            'transform_times': [],
            'transfer_times': [],
            'queue_wait_times': []
        }
    
    def profile_loading(self, n_batches=100):
        """
        分析数据加载性能
        """
        import time
        
        for i, batch in enumerate(self.dataloader):
            if i >= n_batches:
                break
            
            # 记录各阶段时间
            start = time.time()
            
            # 数据加载时间（已包含在batch获取中）
            load_time = time.time() - start
            self.metrics['batch_load_times'].append(load_time)
            
            # GPU传输时间（如果使用GPU）
            if torch.cuda.is_available():
                start_transfer = time.time()
                batch = {k: v.cuda() for k, v in batch.items() if isinstance(v, torch.Tensor)}
                transfer_time = time.time() - start_transfer
                self.metrics['transfer_times'].append(transfer_time)
        
        # 生成报告
        self.generate_report()
    
    def generate_report(self):
        """
        生成性能报告
        """
        print("\n" + "="*50)
        print("DataLoader 性能分析报告")
        print("="*50)
        
        for metric_name, times in self.metrics.items():
            if times:
                avg_time = np.mean(times) * 1000  # 转换为毫秒
                max_time = np.max(times) * 1000
                min_time = np.min(times) * 1000
                
                print(f"\n{metric_name}:")
                print(f"  平均: {avg_time:.2f} ms")
                print(f"  最大: {max_time:.2f} ms")
                print(f"  最小: {min_time:.2f} ms")
        
        # 计算吞吐量
        total_time = sum(self.metrics['batch_load_times'])
        n_batches = len(self.metrics['batch_load_times'])
        batch_size = self.dataloader.batch_size
        
        throughput = (n_batches * batch_size) / total_time
        print(f"\n总体吞吐量: {throughput:.0f} 样本/秒")
    
    def identify_bottlenecks(self):
        """
        识别性能瓶颈
        """
        bottlenecks = []
        
        # 检查数据加载
        avg_load_time = np.mean(self.metrics['batch_load_times'])
        if avg_load_time > 0.1:  # 超过100ms
            bottlenecks.append({
                'type': '数据加载',
                'severity': 'high',
                'suggestion': '增加num_workers或使用更快的存储'
            })
        
        # 检查GPU传输
        if self.metrics['transfer_times']:
            avg_transfer_time = np.mean(self.metrics['transfer_times'])
            if avg_transfer_time > 0.05:  # 超过50ms
                bottlenecks.append({
                    'type': 'GPU传输',
                    'severity': 'medium',
                    'suggestion': '使用pin_memory=True或减小批大小'
                })
        
        return bottlenecks
```

## 7. 错误处理和容错

```python
class RobustDataLoader:
    def __init__(self, dataset, max_retries=3):
        self.dataset = dataset
        self.max_retries = max_retries
        self.error_log = []
    
    def __iter__(self):
        """
        容错的迭代器
        """
        indices = list(range(len(self.dataset)))
        
        for idx in indices:
            retry_count = 0
            
            while retry_count < self.max_retries:
                try:
                    # 尝试加载数据
                    sample = self.dataset[idx]
                    
                    # 验证数据
                    if self.validate_sample(sample):
                        yield sample
                        break
                    else:
                        raise ValueError(f"样本验证失败: {idx}")
                        
                except Exception as e:
                    retry_count += 1
                    self.error_log.append({
                        'index': idx,
                        'error': str(e),
                        'retry': retry_count
                    })
                    
                    if retry_count >= self.max_retries:
                        print(f"跳过损坏的样本 {idx}: {e}")
                        break
                    
                    # 等待后重试
                    time.sleep(0.1 * retry_count)
    
    def validate_sample(self, sample):
        """
        验证样本完整性
        """
        # 检查必需字段
        required_fields = ['features', 'label']
        for field in required_fields:
            if field not in sample:
                return False
        
        # 检查特征维度
        if len(sample['features']) != self.dataset.n_features:
            return False
        
        # 检查数值有效性
        if np.any(np.isnan(sample['features'])):
            return False
        
        return True
```

## 8. 总结

DataLoader架构的关键设计要点：

1. **数据访问优化**：
   - 延迟加载vs全部加载的智能选择
   - 内存映射文件的使用
   - 多级缓存策略

2. **特征转换流水线**：
   - 模块化的转换器设计
   - 向量化操作优化
   - 动态特征生成

3. **并行和预取**：
   - 多进程数据加载
   - 异步预取队列
   - 智能批处理策略

4. **内存管理**：
   - 动态批大小调整
   - 混合精度数据类型
   - 内存使用监控

5. **容错和监控**：
   - 错误处理和重试机制
   - 性能分析和瓶颈识别
   - 数据验证和完整性检查

通过这些优化，DataLoader能够高效、稳定地为模型训练提供数据，是整个训练流程中的关键组件。