# gradient_monitor.py
"""
梯度监控和调试工具
用于诊断和监控模型训练过程中的梯度爆炸问题
"""

import torch
import numpy as np
# 设置matplotlib使用非交互式后端（适用于无GUI环境）
import matplotlib
matplotlib.use('Agg')  # 必须在导入pyplot之前设置
import matplotlib.pyplot as plt
from collections import defaultdict
import logging

logger = logging.getLogger(__name__)

class GradientMonitor:
    """梯度监控器 - 实时追踪和分析梯度统计信息"""
    
    def __init__(self, model, log_interval=100):
        self.model = model
        self.log_interval = log_interval
        self.step = 0
        self.grad_history = defaultdict(list)
        self.param_history = defaultdict(list)
        self.loss_history = []
        
    def log_gradients(self, loss=None):
        """记录当前步骤的梯度信息"""
        self.step += 1
        
        if loss is not None:
            self.loss_history.append(loss.item())
        
        # 收集所有参数的梯度信息
        grad_stats = {}
        param_stats = {}
        
        for name, param in self.model.named_parameters():
            if param.grad is not None:
                grad = param.grad.data
                
                # 梯度统计
                grad_norm = grad.norm(2).item()
                grad_mean = grad.mean().item()
                grad_std = grad.std().item()
                grad_max = grad.abs().max().item()
                
                # 参数统计
                param_norm = param.data.norm(2).item()
                param_mean = param.data.mean().item()
                param_std = param.data.std().item()
                
                grad_stats[name] = {
                    'norm': grad_norm,
                    'mean': grad_mean,
                    'std': grad_std,
                    'max': grad_max
                }
                
                param_stats[name] = {
                    'norm': param_norm,
                    'mean': param_mean,
                    'std': param_std
                }
                
                # 保存历史
                self.grad_history[name].append(grad_stats[name])
                self.param_history[name].append(param_stats[name])
        
        # 定期打印详细信息
        if self.step % self.log_interval == 0:
            self._print_detailed_stats(grad_stats, param_stats, loss)
            
    def _print_detailed_stats(self, grad_stats, param_stats, loss):
        """打印详细的梯度和参数统计信息"""
        logger.info(f"\n{'='*80}")
        logger.info(f"Step {self.step} - Gradient & Parameter Statistics")
        logger.info(f"{'='*80}")
        
        if loss is not None:
            logger.info(f"Loss: {loss:.6f}")
        
        # 计算总体梯度范数
        total_grad_norm = np.sqrt(sum(stat['norm']**2 for stat in grad_stats.values()))
        logger.info(f"Total Gradient Norm: {total_grad_norm:.4f}")
        
        # 找出梯度最大的层
        max_grad_layer = max(grad_stats.items(), key=lambda x: x[1]['norm'])
        logger.info(f"Max Gradient Layer: {max_grad_layer[0]} (norm={max_grad_layer[1]['norm']:.4f})")
        
        # 详细层级信息
        logger.info(f"\n{'Layer':<40} {'Grad Norm':>12} {'Grad Max':>12} {'Param Norm':>12}")
        logger.info("-" * 80)
        
        for name in sorted(grad_stats.keys()):
            grad = grad_stats[name]
            param = param_stats[name]
            logger.info(f"{name:<40} {grad['norm']:>12.4f} {grad['max']:>12.4f} {param['norm']:>12.4f}")
            
            # 警告极端值
            if grad['norm'] > 100:
                logger.warning(f"⚠️  LARGE GRADIENT in {name}: norm={grad['norm']:.4f}")
            if grad['norm'] < 1e-6:
                logger.warning(f"⚠️  VANISHING GRADIENT in {name}: norm={grad['norm']:.4e}")
                
    def check_gradient_explosion(self, threshold=1000.0):
        """检查是否存在梯度爆炸"""
        exploded_layers = []
        
        for name, param in self.model.named_parameters():
            if param.grad is not None:
                grad_norm = param.grad.data.norm(2).item()
                if grad_norm > threshold:
                    exploded_layers.append((name, grad_norm))
                    
        if exploded_layers:
            logger.error(f"🚨 GRADIENT EXPLOSION DETECTED! Layers with grad norm > {threshold}:")
            for name, norm in exploded_layers:
                logger.error(f"  - {name}: {norm:.4f}")
            return True
        return False
        
    def plot_gradient_history(self, save_path='gradient_history.png'):
        """绘制梯度历史图表并保存为文件
        
        Args:
            save_path: 保存路径，默认为'gradient_history.png'
        """
        if not self.grad_history:
            logger.warning("No gradient history to plot")
            return
            
        # 清除之前的图形，避免内存泄漏
        plt.close('all')
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Gradient History Analysis', fontsize=16)
        
        # 1. Loss history
        if self.loss_history:
            ax = axes[0, 0]
            ax.plot(self.loss_history)
            ax.set_title('Loss History')
            ax.set_xlabel('Step')
            ax.set_ylabel('Loss')
            ax.semilogy()
            
        # 2. Gradient norms by layer
        ax = axes[0, 1]
        for name, history in self.grad_history.items():
            if 'output' in name or 'final' in name:  # 重点关注输出层
                norms = [h['norm'] for h in history]
                ax.plot(norms, label=name, linewidth=2)
        ax.set_title('Gradient Norms (Output Layers)')
        ax.set_xlabel('Step')
        ax.set_ylabel('Gradient Norm')
        ax.legend()
        ax.semilogy()
        
        # 3. Total gradient norm
        ax = axes[1, 0]
        total_norms = []
        for i in range(len(next(iter(self.grad_history.values())))):
            total_norm = np.sqrt(sum(
                self.grad_history[name][i]['norm']**2 
                for name in self.grad_history
            ))
            total_norms.append(total_norm)
        ax.plot(total_norms, 'r-', linewidth=2)
        ax.set_title('Total Gradient Norm')
        ax.set_xlabel('Step')
        ax.set_ylabel('Total Norm')
        ax.semilogy()
        
        # 4. Gradient distribution (last step)
        ax = axes[1, 1]
        last_norms = {name: history[-1]['norm'] 
                     for name, history in self.grad_history.items()}
        sorted_layers = sorted(last_norms.items(), key=lambda x: x[1], reverse=True)[:10]
        layers, norms = zip(*sorted_layers)
        ax.barh(range(len(layers)), norms)
        ax.set_yticks(range(len(layers)))
        ax.set_yticklabels([l.split('.')[-1] for l in layers])
        ax.set_xlabel('Gradient Norm')
        ax.set_title('Top 10 Layers by Gradient Norm (Last Step)')
        
        plt.tight_layout()
        
        # 总是保存文件（适用于无GUI环境）
        if not save_path:
            save_path = 'gradient_history.png'
            
        # 确保文件扩展名正确
        if not save_path.endswith(('.png', '.jpg', '.jpeg', '.pdf')):
            save_path += '.png'
            
        plt.savefig(save_path, dpi=150, bbox_inches='tight', format='png')
        logger.info(f"✅ Gradient history plot saved to: {save_path}")
        
        # 清理内存
        plt.close(fig)
            
    def get_gradient_statistics(self):
        """获取梯度统计摘要"""
        stats = {}
        
        for name, param in self.model.named_parameters():
            if param.grad is not None:
                grad = param.grad.data
                stats[name] = {
                    'shape': list(grad.shape),
                    'norm': grad.norm(2).item(),
                    'mean': grad.mean().item(),
                    'std': grad.std().item(),
                    'min': grad.min().item(),
                    'max': grad.max().item(),
                    'has_nan': torch.isnan(grad).any().item(),
                    'has_inf': torch.isinf(grad).any().item()
                }
                
        return stats
    
    def generate_text_report(self, save_path='gradient_report.txt'):
        """生成文本格式的梯度分析报告（适用于终端环境）"""
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write("="*80 + "\n")
            f.write("Gradient Analysis Report\n")
            f.write("="*80 + "\n\n")
            
            # 1. 训练概览
            f.write(f"Total Steps: {self.step}\n")
            if self.loss_history:
                f.write(f"Final Loss: {self.loss_history[-1]:.6f}\n")
                f.write(f"Min Loss: {min(self.loss_history):.6f}\n")
                f.write(f"Max Loss: {max(self.loss_history):.6f}\n")
            f.write("\n")
            
            # 2. 梯度统计
            f.write("Gradient Statistics (Last Step):\n")
            f.write("-"*80 + "\n")
            
            if self.grad_history:
                # 获取最后一步的统计
                last_stats = []
                for name, history in self.grad_history.items():
                    if history:
                        last_stat = history[-1]
                        last_stats.append((name, last_stat['norm'], last_stat['max']))
                
                # 按梯度范数排序
                last_stats.sort(key=lambda x: x[1], reverse=True)
                
                f.write(f"{'Layer':<50} {'Grad Norm':>15} {'Grad Max':>15}\n")
                f.write("-"*80 + "\n")
                
                for name, norm, max_val in last_stats[:20]:  # 显示前20层
                    f.write(f"{name:<50} {norm:>15.6f} {max_val:>15.6f}\n")
                    
                f.write("\n")
                
                # 3. 梯度异常检测
                f.write("Gradient Anomalies:\n")
                f.write("-"*80 + "\n")
                
                # 检查梯度爆炸
                exploded = [(n, norm) for n, norm, max_val in last_stats if norm > 100]
                if exploded:
                    f.write("⚠️  Gradient Explosion Detected:\n")
                    for name, norm in exploded:
                        f.write(f"  - {name}: {norm:.4f}\n")
                else:
                    f.write("✅ No gradient explosion detected\n")
                
                # 检查梯度消失
                vanished = [(n, norm) for n, norm, max_val in last_stats if norm < 1e-6]
                if vanished:
                    f.write("\n⚠️  Vanishing Gradient Detected:\n")
                    for name, norm in vanished:
                        f.write(f"  - {name}: {norm:.4e}\n")
                else:
                    f.write("✅ No vanishing gradient detected\n")
                
                # 4. 训练稳定性分析
                f.write("\n\nTraining Stability Analysis:\n")
                f.write("-"*80 + "\n")
                
                # 分析总梯度范数的变化
                total_norms = []
                for i in range(len(next(iter(self.grad_history.values())))):
                    total_norm = np.sqrt(sum(
                        self.grad_history[name][i]['norm']**2 
                        for name in self.grad_history
                    ))
                    total_norms.append(total_norm)
                
                if total_norms:
                    f.write(f"Total Gradient Norm Statistics:\n")
                    f.write(f"  Mean: {np.mean(total_norms):.4f}\n")
                    f.write(f"  Std: {np.std(total_norms):.4f}\n")
                    f.write(f"  Min: {np.min(total_norms):.4f}\n")
                    f.write(f"  Max: {np.max(total_norms):.4f}\n")
                    
                    # 检查稳定性
                    variation = np.std(total_norms) / (np.mean(total_norms) + 1e-8)
                    if variation > 2.0:
                        f.write(f"\n⚠️  High gradient variance detected (CV={variation:.2f})\n")
                        f.write("   Training may be unstable!\n")
                    else:
                        f.write(f"\n✅ Gradient variance is acceptable (CV={variation:.2f})\n")
            
            f.write("\n" + "="*80 + "\n")
            f.write(f"Report saved to: {save_path}\n")
            
        logger.info(f"✅ Text report saved to: {save_path}")


def diagnose_model_initialization(model, input_dim, batch_size=32, device='cpu'):
    """诊断模型初始化问题"""
    logger.info("\n" + "="*80)
    logger.info("Model Initialization Diagnosis")
    logger.info("="*80)
    
    # 1. 检查参数初始化
    logger.info("\n1. Parameter Initialization Check:")
    for name, param in model.named_parameters():
        logger.info(f"{name:<40} shape={list(param.shape)} "
                   f"mean={param.mean().item():.4f} "
                   f"std={param.std().item():.4f}")
        
        # 检查异常值
        if param.std().item() > 1.0:
            logger.warning(f"⚠️  Large std in {name}: {param.std().item():.4f}")
        if param.std().item() < 0.01:
            logger.warning(f"⚠️  Small std in {name}: {param.std().item():.4f}")
    
    # 2. 前向传播测试
    logger.info("\n2. Forward Pass Test:")
    model.eval()
    
    # 测试不同的输入范围
    test_inputs = {
        'normal': torch.randn(batch_size, input_dim),
        'uniform': torch.rand(batch_size, input_dim) * 2 - 1,
        'scaled': torch.randn(batch_size, input_dim) * 0.1
    }
    
    for input_type, x in test_inputs.items():
        x = x.to(device)
        with torch.no_grad():
            output = model(x)
            logits = output.cpu().numpy()
            
        logger.info(f"\nInput type: {input_type}")
        logger.info(f"  Output stats: mean={logits.mean():.4f}, "
                   f"std={logits.std():.4f}, "
                   f"min={logits.min():.4f}, "
                   f"max={logits.max():.4f}")
        
        if logits.std() > 100:
            logger.error(f"🚨 EXTREME OUTPUT VARIANCE with {input_type} input!")
    
    # 3. 梯度流测试
    logger.info("\n3. Gradient Flow Test:")
    model.train()
    x = torch.randn(batch_size, input_dim, requires_grad=True).to(device)
    y = torch.randint(0, 2, (batch_size,)).float().to(device)
    
    output = model(x)
    criterion = torch.nn.BCEWithLogitsLoss()
    loss = criterion(output.squeeze(), y)
    loss.backward()
    
    # 检查梯度
    grad_stats = {}
    for name, param in model.named_parameters():
        if param.grad is not None:
            grad_norm = param.grad.norm(2).item()
            grad_stats[name] = grad_norm
            
            if grad_norm > 10:
                logger.warning(f"⚠️  Large gradient in {name}: {grad_norm:.4f}")
            if grad_norm < 1e-6:
                logger.warning(f"⚠️  Vanishing gradient in {name}: {grad_norm:.4e}")
    
    # 打印梯度流摘要
    total_grad_norm = np.sqrt(sum(g**2 for g in grad_stats.values()))
    logger.info(f"\nTotal gradient norm: {total_grad_norm:.4f}")
    
    return grad_stats


def create_gradient_hook(name, gradient_dict):
    """创建梯度钩子函数"""
    def hook(grad):
        gradient_dict[name] = {
            'norm': grad.norm(2).item(),
            'mean': grad.mean().item(),
            'std': grad.std().item(),
            'shape': list(grad.shape)
        }
        
        # 实时警告
        if grad.norm(2).item() > 100:
            logger.warning(f"⚠️  Large gradient in {name}: norm={grad.norm(2).item():.4f}")
            
        return grad
    return hook


def add_gradient_hooks(model):
    """为模型添加梯度钩子"""
    gradient_dict = {}
    hooks = []
    
    for name, param in model.named_parameters():
        if param.requires_grad:
            hook = param.register_hook(create_gradient_hook(name, gradient_dict))
            hooks.append(hook)
            
    return gradient_dict, hooks


def remove_gradient_hooks(hooks):
    """移除梯度钩子"""
    for hook in hooks:
        hook.remove()


if __name__ == "__main__":
    # 使用示例
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 假设已有模型
    from src.models_o3_cc import build_model
    
    model = build_model('dcnv2', input_dim=160, config={})
    
    # 诊断初始化
    diagnose_model_initialization(model, input_dim=160)
    
    # 创建监控器
    monitor = GradientMonitor(model, log_interval=10)
    
    # 模拟训练
    logger.info("\n\nSimulating training with gradient monitoring...")
    for i in range(50):
        x = torch.randn(32, 160)
        y = torch.randint(0, 2, (32,)).float()
        
        output = model(x).squeeze()
        loss = torch.nn.functional.binary_cross_entropy_with_logits(output, y)
        
        loss.backward()
        monitor.log_gradients(loss)
        
        # 检查梯度爆炸
        if monitor.check_gradient_explosion(threshold=100):
            logger.error(f"Training stopped at step {i} due to gradient explosion!")
            break
            
        # 清零梯度
        model.zero_grad()
    
    # 绘制历史（自动保存为文件）
    monitor.plot_gradient_history('gradient_history.png')
    logger.info("梯度监控完成！请查看生成的gradient_history.png文件")