# check_data.py
"""
检查预处理后的数据质量和统计信息
"""

import numpy as np
import os
from config import PARALLEL_EXTENDED_CONFIG

def check_data_stats():
    """检查数据的基本统计信息"""
    processed_dir = PARALLEL_EXTENDED_CONFIG['processed_data_dir']
    
    datasets = ['train', 'validation', 'test']
    
    for dataset_name in datasets:
        feature_file = os.path.join(processed_dir, f"{dataset_name}_features.npy")
        label_file = os.path.join(processed_dir, f"{dataset_name}_labels.npy")
        
        if os.path.exists(feature_file) and os.path.exists(label_file):
            features = np.load(feature_file)
            labels = np.load(label_file)
            
            print(f"\n=== {dataset_name.upper()} 数据集统计 ===")
            print(f"特征形状: {features.shape}")
            print(f"标签形状: {labels.shape}")
            
            # 特征统计
            print(f"\n特征统计:")
            print(f"  最小值: {features.min():.6f}")
            print(f"  最大值: {features.max():.6f}")
            print(f"  均值: {features.mean():.6f}")
            print(f"  标准差: {features.std():.6f}")
            print(f"  是否包含NaN: {np.isnan(features).any()}")
            print(f"  是否包含Inf: {np.isinf(features).any()}")
            
            # 标签统计
            print(f"\n标签统计:")
            print(f"  最小值: {labels.min()}")
            print(f"  最大值: {labels.max()}")
            print(f"  均值: {labels.mean():.6f}")
            print(f"  唯一值: {np.unique(labels)}")
            print(f"  正样本比例: {(labels == 1).mean():.4f}")
            print(f"  负样本比例: {(labels == 0).mean():.4f}")
            print(f"  是否包含NaN: {np.isnan(labels).any()}")
            
            # 检查特征的分布
            print(f"\n特征分布 (前5个特征):")
            for i in range(min(5, features.shape[1])):
                feat = features[:, i]
                print(f"  特征{i}: min={feat.min():.4f}, max={feat.max():.4f}, "
                      f"mean={feat.mean():.4f}, std={feat.std():.4f}")
        else:
            print(f"\n❌ {dataset_name} 数据文件不存在")

if __name__ == "__main__":
    check_data_stats()
