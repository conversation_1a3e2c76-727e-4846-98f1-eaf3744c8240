# parallel_processor.py
"""
多进程Pandas处理器 - 生产版本
包装现有的预处理逻辑，添加多进程支持，使用spawn方法避免fork-safe问题
"""

import os
import sys
import time
import logging
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
from pathlib import Path

# 导入配置和工具
from config import PARALLEL_CONFIG, PARALLEL_EXTENDED_CONFIG
from preprocess import IntelligentPreprocessor, load_analysis_results
from data_analyzer import DataAnalyzer
from common import io as common_io

# 导入进程跟踪器
from minimal_logger import (
    track_worker_start, track_worker_complete, track_worker_timeout,
    track_request, track_response, track_s3_request, track_s3_response,
    track_thread_create, track_thread_complete, track_thread_timeout,
    track_pool_status, track_file_count, track_data_size
)

# 设置原始日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - [PID:%(process)d] %(message)s',
    force=True
)
logger = logging.getLogger(__name__)

class ParallelProcessor:
    """
    多进程数据处理器
    
    核心设计原则：
    1. 包装而不是重写 - 最大化复用现有逻辑
    2. 使用spawn启动方法 - 避免fork-safe问题，兼容Windows
    3. 独立的S3客户端 - 每个进程有自己的连接，避免连接共享
    4. 内存友好 - 保持流式处理，避免OOM
    """
    
    def __init__(self, max_workers: int = None):
        """
        初始化多进程处理器
        
        Args:
            max_workers: 最大工作进程数，None则使用配置中的值
        """
        self.config = PARALLEL_CONFIG
        self.extended_config = PARALLEL_EXTENDED_CONFIG
        
        # 设置工作进程数 - 增强版，考虑S3连接限制
        if max_workers is not None:
            self.max_workers = max_workers
        else:
            self.max_workers = self.config.get('max_workers', 8)

        # [DEBUG] S3并发优化：限制最大worker数以避免S3连接池耗尽
        # AWS S3通常建议每个客户端不超过100个并发连接
        # 考虑到每个worker可能创建多个连接，我们限制worker数量
        original_max_workers = self.max_workers
        if self.max_workers > 20:  # 如果worker数过多，限制为20
            self.max_workers = 20
            logger.warning(f"[DEBUG] S3并发优化: 将worker数从 {original_max_workers} 限制为 {self.max_workers} 以避免S3连接池耗尽")

        # 设置多进程启动方法
        self.start_method = self.config.get('multiprocessing_start_method', 'spawn')
        try:
            mp.set_start_method(self.start_method, force=True)
            logger.info(f"设置多进程启动方法: {self.start_method}")
        except RuntimeError as e:
            logger.warning(f"无法设置启动方法 {self.start_method}: {e}")

        # 加载数据分析结果
        self.analysis_results = self._load_or_create_analysis()

        logger.info(f"多进程处理器初始化完成:")
        logger.info(f"  最大工作进程数: {self.max_workers} (原始: {original_max_workers})")
        logger.info(f"  启动方法: {self.start_method}")
        logger.info(f"  内存限制: {self.config.get('memory_limit_gb', 32)}GB")
        logger.info(f"  S3并发优化: 已启用")
        
    def _load_or_create_analysis(self) -> Dict[str, Any]:
        """加载或创建数据分析结果"""
        try:
            analysis_results = load_analysis_results()
            if analysis_results:
                logger.info("成功加载现有的数据分析结果")
                return analysis_results
        except Exception as e:
            logger.warning(f"加载分析结果失败: {e}")
            
        # 如果没有分析结果，自动运行分析
        logger.info("运行数据分析...")
        try:
            analyzer = DataAnalyzer()
            analysis_results = analyzer.analyze_all_datasets()
            analyzer.save_analysis(analysis_results)
            logger.info("数据分析完成")
            return analysis_results
        except Exception as e:
            logger.error(f"数据分析失败: {e}")
            raise
            
    def process_files_parallel(self, file_paths: List[str], dataset_name: str) -> bool:
        """
        并行处理多个文件

        Args:
            file_paths: 要处理的文件路径列表
            dataset_name: 数据集名称

        Returns:
            bool: 处理是否成功
        """
        # 检查当前日志级别，决定是否显示路径
        root_logger = logging.getLogger()
        show_paths = root_logger.level < 45  # TRACK级别是45

        if show_paths:
            logger.info(f"开始并行处理 {len(file_paths)} 个文件 (数据集: {dataset_name})")
            logger.info(f"文件路径示例: {file_paths[:3] if len(file_paths) >= 3 else file_paths}")
        else:
            logger.info(f"开始并行处理 {len(file_paths)} 个文件 (数据集: {dataset_name})")

        # [DEBUG] 关键调试信息
        logger.info(f"[DEBUG] 并行处理配置:")
        logger.info(f"   Worker进程数: {self.max_workers}")
        logger.info(f"   文件总数: {len(file_paths)}")
        logger.info(f"   每个Worker平均处理: {len(file_paths)/self.max_workers:.1f} 个文件")
        logger.info(f"   预计创建任务数: {len(file_paths)} (每个文件一个任务)")

        start_time = time.time()

        # 创建输出目录
        output_dir = self.extended_config['processed_data_dir']
        common_io.ensure_dir(output_dir)

        # 准备工作函数参数
        worker_args = [(file_path, self.analysis_results, dataset_name, i)
                      for i, file_path in enumerate(file_paths)]

        logger.info(f"[DEBUG] 准备了 {len(worker_args)} 个工作任务")
        
        # 使用ProcessPoolExecutor进行并行处理
        successful_chunks = []
        failed_files = []
        
        try:
            # 跟踪并行处理开始
            track_request("PARALLEL_POOL")
            track_pool_status(0, self.max_workers, "CREATING")

            # [DEBUG] S3连接监控：记录开始时的连接状态
            try:
                common_io.log_connection_stats("before_parallel_pool_create")
            except:
                pass

            logger.info(f"[START] 创建进程池，max_workers={self.max_workers}")
            logger.info(f"[INFO] 将处理 {len(worker_args)} 个文件，每个worker处理约 {len(worker_args)/self.max_workers:.1f} 个文件")

            with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
                track_pool_status(0, self.max_workers, "CREATED")
                logger.info(f"[OK] 进程池创建成功，开始提交 {len(worker_args)} 个任务")

                # 跟踪任务提交
                track_request("TASKS_SUBMIT")

                # 提交所有任务并跟踪每个线程
                future_to_args = {}
                logger.info(f"[DEBUG] 开始提交 {len(worker_args)} 个任务到 {self.max_workers} 个worker进程")

                for i, args in enumerate(worker_args):
                    file_path, _, _, chunk_id = args
                    filename = os.path.basename(file_path)

                    # 条件性显示文件信息
                    if show_paths and (i < 5 or i % 20 == 0):  # 只显示前5个和每20个
                        logger.debug(f"📤 提交任务 {i+1}: {file_path}")
                    elif not show_paths and i % 20 == 0:
                        logger.info(f"📤 提交任务 {i+1}: file_{chunk_id}")

                    future = executor.submit(_process_single_file_worker, args)
                    future_to_args[future] = args
                    track_thread_create(f"THREAD_{i+1}", "FILE_PROCESS")

                    # 每提交10个任务记录一次状态
                    if (i + 1) % 10 == 0:
                        logger.info(f"📤 已提交 {i+1}/{len(worker_args)} 个任务")

                track_pool_status(len(worker_args), self.max_workers, "ALL_SUBMITTED")
                logger.info(f"[OK] 所有任务已提交，开始收集结果...")

                # 跟踪任务提交完成
                track_response("TASKS_SUBMIT", f"COUNT_{len(worker_args)}")

                # [DEBUG] S3连接监控：记录任务提交后的连接状态
                try:
                        common_io.log_connection_stats("after_tasks_submitted")
                except:
                    pass

                # 收集结果
                completed_count = 0
                active_threads = len(worker_args)
                last_status_time = time.time()
                status_interval = 30  # 每30秒输出一次状态

                for future in as_completed(future_to_args):
                    args = future_to_args[future]
                    file_path, _, _, chunk_id = args
                    completed_count += 1
                    active_threads -= 1

                    # 跟踪线程池状态
                    track_pool_status(active_threads, len(worker_args), f"PROGRESS_{completed_count}/{len(worker_args)}")

                    # 定期输出详细状态和S3连接统计
                    current_time = time.time()
                    if current_time - last_status_time >= status_interval:
                        logger.info(f"[INFO] 进度报告: {completed_count}/{len(worker_args)} 完成, "
                                   f"成功: {len(successful_chunks)}, 失败: {len(failed_files)}, "
                                   f"活跃线程: {active_threads}")
                        try:
                                        common_io.log_connection_stats("progress_report")
                        except:
                            pass
                        last_status_time = current_time

                    try:
                        # [DEBUG] 调试：记录开始等待结果的时间
                        wait_start = time.time()
                        logger.debug(f"[DEBUG] 开始等待任务 {completed_count+1} 的结果 (超时: 300s)")

                        result = future.result(timeout=300)  # 5分钟超时
                        wait_time = time.time() - wait_start
                        logger.debug(f"[DEBUG] 任务 {completed_count+1} 结果获取耗时: {wait_time:.1f}s")

                        if result is not None:
                            successful_chunks.append((chunk_id, result))
                            # 跟踪线程成功完成
                            track_thread_complete(f"THREAD_{completed_count}", "SUCCESS")
                            track_response("TASK_PROCESS", f"SUCCESS_{completed_count}/{len(worker_args)}")
                            logger.info(f"[OK] [{completed_count}/{len(worker_args)}] 文件处理成功: {os.path.basename(file_path)}")
                        else:
                            failed_files.append(file_path)
                            # 跟踪线程失败
                            track_thread_complete(f"THREAD_{completed_count}", "FAILED_NO_DATA")
                            track_response("TASK_PROCESS", f"FAILED_{completed_count}/{len(worker_args)}")
                            logger.warning(f"[WARNING] [{completed_count}/{len(worker_args)}] 文件处理失败: {os.path.basename(file_path)}")
                    except TimeoutError as e:
                        failed_files.append(file_path)
                        # 跟踪线程超时
                        track_thread_timeout(f"THREAD_{completed_count}", "FILE_PROCESS")
                        track_response("TASK_PROCESS", f"TIMEOUT_{completed_count}/{len(worker_args)}")
                        logger.error(f"[TIMEOUT] [{completed_count}/{len(worker_args)}] 文件处理超时: {os.path.basename(file_path)}")

                        # 超时时记录S3连接状态
                        try:
                                        common_io.log_connection_stats("timeout_occurred")
                        except:
                            pass

                    except Exception as e:
                        failed_files.append(file_path)
                        # 跟踪线程错误
                        track_thread_complete(f"THREAD_{completed_count}", f"ERROR_{type(e).__name__}")
                        track_response("TASK_PROCESS", f"ERROR_{type(e).__name__}_{completed_count}/{len(worker_args)}")
                        logger.error(f"[ERROR] [{completed_count}/{len(worker_args)}] 文件处理异常: {os.path.basename(file_path)} - {e}")
                        import traceback
                        logger.error(f"详细错误: {traceback.format_exc()}")

                        # 异常时记录S3连接状态
                        try:
                                        common_io.log_connection_stats("error_occurred")
                        except:
                            pass

                logger.info(f"🏁 所有任务完成，成功: {len(successful_chunks)}, 失败: {len(failed_files)}")

                # 跟踪并行处理完成
                track_response("PARALLEL_POOL", f"SUCCESS_{len(successful_chunks)}_FAILED_{len(failed_files)}")

                # [DEBUG] S3连接监控：记录处理完成后的连接状态
                try:
                        common_io.log_connection_stats("after_parallel_pool_complete")
                except:
                    pass

        except Exception as e:
            track_response("PARALLEL_POOL", f"ERROR_{type(e).__name__}")
            logger.error(f"[ERROR] 并行处理过程中发生错误: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False
            
        # 处理结果统计
        processing_time = time.time() - start_time
        success_rate = len(successful_chunks) / len(file_paths) * 100
        
        logger.info(f"并行处理完成:")
        logger.info(f"  处理时间: {processing_time:.2f}秒")
        logger.info(f"  成功文件: {len(successful_chunks)}/{len(file_paths)} ({success_rate:.1f}%)")
        logger.info(f"  失败文件: {len(failed_files)}")
        
        if failed_files:
            logger.warning(f"失败的文件: {[os.path.basename(f) for f in failed_files[:5]]}")
            
        # 如果有成功的结果，合并保存
        if successful_chunks:
            return self._merge_and_save_results(successful_chunks, dataset_name)
        else:
            logger.error("没有成功处理的文件")
            return False
            
    def _merge_and_save_results(self, successful_chunks: List[Tuple[int, Tuple]], 
                               dataset_name: str) -> bool:
        """合并并保存处理结果"""
        try:
            logger.info(f"合并 {len(successful_chunks)} 个数据块...")
            
            # 按chunk_id排序
            successful_chunks.sort(key=lambda x: x[0])
            
            # 提取特征和标签
            all_features = []
            all_labels = []
            
            for chunk_id, (features, labels) in successful_chunks:
                all_features.append(features)
                all_labels.append(labels)
                
            # 合并数据
            final_features = np.concatenate(all_features, axis=0)
            final_labels = np.concatenate(all_labels, axis=0)
            
            # 保存最终结果
            output_dir = self.extended_config['processed_data_dir']
            feature_file = os.path.join(output_dir, f"{dataset_name}_features.npy")
            label_file = os.path.join(output_dir, f"{dataset_name}_labels.npy")
            
            common_io.save_npy(final_features.astype(np.float32), feature_file)
            common_io.save_npy(final_labels.astype(np.int64), label_file)
            
            logger.info(f"数据保存成功:")
            logger.info(f"  特征文件: {feature_file} (形状: {final_features.shape})")
            logger.info(f"  标签文件: {label_file} (形状: {final_labels.shape})")
            
            return True
            
        except Exception as e:
            logger.error(f"合并和保存结果时发生错误: {e}")
            return False
            
    def process_dataset(self, data_dir: str, dataset_name: str) -> bool:
        """
        处理整个数据集

        Args:
            data_dir: 数据目录路径
            dataset_name: 数据集名称

        Returns:
            bool: 处理是否成功
        """
        logger.info(f"处理数据集: {dataset_name}")
        track_request(f"DATASET_{dataset_name.upper()}_PROCESS")

        # 检查数据目录是否存在
        track_s3_request("PATH_CHECK", f"dataset={dataset_name}")
        if not common_io.path_exists(data_dir):
            track_s3_response("PATH_CHECK", "NOT_EXISTS", f"dataset={dataset_name}")
            logger.error(f"数据目录不存在")
            return False
        track_s3_response("PATH_CHECK", "EXISTS", f"dataset={dataset_name}")

        # 获取所有parquet文件
        track_s3_request("LIST_FILES", f"dataset={dataset_name}")
        file_paths = common_io.list_parquet_files(data_dir)
        if not file_paths:
            track_s3_response("LIST_FILES", "NO_FILES", f"dataset={dataset_name}")
            logger.error(f"未找到parquet文件")
            return False

        track_s3_response("LIST_FILES", "SUCCESS", f"dataset={dataset_name}")
        track_file_count(len(file_paths), f"dataset={dataset_name}")
        logger.info(f"找到 {len(file_paths)} 个parquet文件")

        # 并行处理文件
        result = self.process_files_parallel(file_paths, dataset_name)

        if result:
            track_response(f"DATASET_{dataset_name.upper()}_PROCESS", "SUCCESS")
        else:
            track_response(f"DATASET_{dataset_name.upper()}_PROCESS", "FAILED")

        return result


def _process_single_file_worker(args: Tuple[str, Dict[str, Any], str, int]) -> Optional[Tuple]:
    """
    工作进程函数 - 处理单个文件

    这个函数在独立的进程中运行，复用现有的IntelligentPreprocessor逻辑
    每个进程都有独立的S3连接，避免fork-safe问题

    Args:
        args: (file_path, analysis_results, dataset_name, chunk_id)

    Returns:
        Optional[Tuple]: (features_array, labels_array) 或 None
    """
    import signal
    import logging
    import os
    import time

    # 设置超时处理 (UNIX)
    def timeout_handler(signum, frame):
        raise TimeoutError(f"Worker process {os.getpid()} timed out after 10 minutes")

    if hasattr(signal, 'SIGALRM'):
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(600)  # 10分钟超时

    # 在子进程中重新配置日志，确保日志能正确输出
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - [PID:%(process)d] %(message)s',
        force=True  # 强制重新配置
    )
    worker_logger = logging.getLogger(f"worker_{os.getpid()}")

    # 检查是否需要应用TRACK模式的logger level设置
    # 如果父进程设置了TRACK模式，子进程也应该应用相同设置
    try:
        from minimal_logger import PROCESS_TRACK
        # 检查根logger的级别，如果是TRACK模式，则应用相同设置
        if logging.getLogger().level >= PROCESS_TRACK:
            worker_logger.setLevel(PROCESS_TRACK)
            logging.getLogger('s3_utils').setLevel(PROCESS_TRACK)
            logging.getLogger('data_analyzer').setLevel(PROCESS_TRACK)
            logging.getLogger('preprocess').setLevel(PROCESS_TRACK)
    except:
        pass  # 如果导入失败，继续使用默认设置

    # 添加进程跟踪
    from minimal_logger import (
        track_worker_start, track_worker_complete, track_worker_timeout,
        track_s3_request, track_s3_response, track_data_size
    )

    file_path, analysis_results, dataset_name, chunk_id = args
    pid = os.getpid()
    filename = os.path.basename(file_path)

    # 检查当前日志级别，决定是否显示路径
    show_paths = logging.getLogger().level < 45  # TRACK级别是45

    try:
        # 跟踪worker开始
        track_worker_start(pid, "FILE_PROCESS")
        start_time = time.time()

        # 条件性显示文件信息
        if show_paths:
            worker_logger.info(f"[PROCESSING] 进程 {pid} 开始处理文件: {file_path}")
            worker_logger.debug(f"[PROCESSING] 进程 {pid} 文件详情: chunk_id={chunk_id}, dataset={dataset_name}")
            print(f"[PROCESSING] 进程 {pid} 开始处理文件: {file_path}", flush=True)
        else:
            worker_logger.info(f"[PROCESSING] 进程 {pid} 开始处理文件: {filename}")
            print(f"[PROCESSING] 进程 {pid} 开始处理文件: {filename}", flush=True)

        # [DEBUG] S3连接监控：记录worker开始时的连接状态
        try:
            common_io.log_connection_stats(f"worker_{pid}_start")
        except:
            pass

        # [DEBUG] 关键调试信息
        worker_logger.info(f"[DEBUG] Worker {pid} 配置:")
        worker_logger.info(f"   任务ID: {chunk_id}")
        worker_logger.info(f"   数据集: {dataset_name}")
        worker_logger.info(f"   文件大小检查开始...")

        # 在每个进程中创建独立的预处理器实例
        worker_logger.info(f"[INFO] 进程 {pid} 创建预处理器...")
        print(f"[INFO] 进程 {pid} 创建预处理器...", flush=True)

        from preprocess import IntelligentPreprocessor
        preprocessor = IntelligentPreprocessor(analysis_results)

        worker_logger.info(f"[OK] 进程 {pid} 预处理器创建成功")
        print(f"[OK] 进程 {pid} 预处理器创建成功", flush=True)

        # 跟踪S3文件读取请求
        track_s3_request("READ_FILE", f"worker={pid} file={filename}")

        if show_paths:
            worker_logger.info(f"📖 进程 {pid} 开始处理parquet文件: {file_path}")
            print(f"📖 进程 {pid} 开始处理parquet文件: {file_path}", flush=True)
        else:
            worker_logger.info(f"📖 进程 {pid} 开始处理parquet文件: {filename}")
            print(f"📖 进程 {pid} 开始处理parquet文件: {filename}", flush=True)

        # [DEBUG] S3连接监控：记录开始处理文件时的连接状态
        try:
            common_io.log_connection_stats(f"worker_{pid}_before_process")
        except:
            pass

        # [DEBUG] 关键调试：记录处理开始时间
        process_start = time.time()
        worker_logger.info(f"[DEBUG] 进程 {pid} 开始调用 process_parquet_chunk...")
        print(f"[DEBUG] 进程 {pid} 开始调用 process_parquet_chunk...", flush=True)

        result = preprocessor.process_parquet_chunk(file_path)

        process_time = time.time() - process_start
        worker_logger.info(f"📖 进程 {pid} parquet文件处理完成: {filename}, 耗时: {process_time:.1f}s")
        print(f"📖 进程 {pid} parquet文件处理完成: {filename}, 耗时: {process_time:.1f}s", flush=True)

        if result is not None:
            features, labels = result
            elapsed = time.time() - start_time

            # 跟踪S3读取成功和数据大小
            track_s3_response("READ_FILE", "SUCCESS", f"worker={pid} file={filename} time={elapsed:.1f}s")
            track_data_size(features.shape[0], features.shape[1], f"worker={pid}")
            track_worker_complete(pid, f"SUCCESS_{features.shape[0]}rows")

            worker_logger.info(f"[OK] 进程 {pid} 成功处理文件 {filename}: "
                       f"特征={features.shape}, 标签={labels.shape}, 耗时={elapsed:.1f}s")
            print(f"[OK] 进程 {pid} 成功处理文件 {filename}: "
                  f"特征={features.shape}, 标签={labels.shape}, 耗时={elapsed:.1f}s", flush=True)

            # [DEBUG] S3连接监控：记录成功完成时的连接状态
            try:
                common_io.log_connection_stats(f"worker_{pid}_success")
            except:
                pass

            return features, labels
        else:
            # 跟踪S3读取失败
            track_s3_response("READ_FILE", "NO_DATA", f"worker={pid} file={filename}")
            track_worker_complete(pid, "FAILED_NO_DATA")

            worker_logger.warning(f"[WARNING] 进程 {pid} 处理文件失败: {filename}")
            print(f"[WARNING] 进程 {pid} 处理文件失败: {filename}", flush=True)

            # [DEBUG] S3连接监控：记录失败时的连接状态
            try:
                common_io.log_connection_stats(f"worker_{pid}_no_data")
            except:
                pass

            return None

    except TimeoutError as e:
        # 跟踪S3读取超时
        track_s3_response("READ_FILE", "TIMEOUT", f"worker={pid} file={filename}")
        track_worker_timeout(pid)
        worker_logger.error(f"[TIMEOUT] 进程 {pid} 超时: {e}")
        print(f"[TIMEOUT] 进程 {pid} 超时: {e}", flush=True)

        # [DEBUG] S3连接监控：记录超时时的连接状态
        try:
            common_io.log_connection_stats(f"worker_{pid}_timeout")
        except:
            pass

        return None
    except Exception as e:
        # 跟踪S3读取错误
        error_type = type(e).__name__
        track_s3_response("READ_FILE", f"ERROR_{error_type}", f"worker={pid} file={filename}")
        track_worker_complete(pid, f"ERROR_{error_type}")

        error_msg = f"[ERROR] 进程 {pid} 处理文件 {filename} 时发生异常: {error_type} - {e}"
        worker_logger.error(error_msg)
        print(error_msg, flush=True)

        import traceback
        traceback_msg = f"详细错误: {traceback.format_exc()}"
        worker_logger.error(traceback_msg)
        print(traceback_msg, flush=True)

        # [DEBUG] S3连接监控：记录异常时的连接状态
        try:
            common_io.log_connection_stats(f"worker_{pid}_error_{error_type}")
        except:
            pass

        return None

    finally:
        # 清理超时
        if hasattr(signal, 'SIGALRM'):
            signal.alarm(0)
        # 清理S3连接资源
        try:
            common_io.cleanup_s3_filesystem()
            worker_logger.info(f"进程 {pid} S3资源清理完成")
        except Exception as e:
            worker_logger.warning(f"进程 {pid} 清理S3资源时出错: {e}")

        # [DEBUG] S3连接监控：记录worker结束时的连接状态
        try:
            common_io.log_connection_stats(f"worker_{pid}_cleanup")
        except:
            pass


def main():
    """主函数 - 处理所有数据集"""
    processor = ParallelProcessor()
    
    # 处理所有数据集
    datasets = {
        'train': processor.extended_config['train_data_dir'],
        'validation': processor.extended_config['validation_data_dir'],
        'test': processor.extended_config['test_data_dir']
    }
    
    for dataset_name, data_dir in datasets.items():
        if data_dir and common_io.path_exists(data_dir):
            logger.info(f"开始处理 {dataset_name} 数据集...")
            success = processor.process_dataset(data_dir, dataset_name)
            if success:
                logger.info(f"{dataset_name} 数据集处理成功")
            else:
                logger.error(f"{dataset_name} 数据集处理失败")
        else:
            logger.warning(f"跳过 {dataset_name} 数据集 (路径不存在或为空)")


if __name__ == "__main__":
    main()
