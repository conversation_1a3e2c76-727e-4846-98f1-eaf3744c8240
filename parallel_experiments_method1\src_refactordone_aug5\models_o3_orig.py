# models_o3_orig.py
"""
改进版模型定义文件
包含 MLP、DCNv2、DCNv1 和 DLRM 模型的实现。
主要改动
- BatchNorm → LayerNorm，Dropout 放在激活后
- 交叉层使用更高效的 `F.linear`，权重 He 初始化，显式 bias 广播
- 修复 DLRM 上三角交互维度取整，简化工厂注册
"""

from __future__ import annotations

import math
from typing import Sequence, Dict, Any

import torch
import torch.nn as nn
import torch.nn.functional as F


# ===================== 公共构件 =====================

def _dense_block(in_dim: int, out_dim: int, p: float) -> nn.Sequential:
    """Linear → ReLU → Dropout → LayerNorm"""
    return nn.Sequential(
        nn.Linear(in_dim, out_dim),
        nn.ReLU(inplace=True),
        nn.Dropout(p),
        nn.LayerNorm(out_dim),
    )


# ===================== MLP =====================
class MLP(nn.Module):
    """多层感知机"""

    def __init__(self, input_dim: int, hidden_dims: Sequence[int] = (256, 128), dropout_p: float = 0.3):
        super().__init__()
        layers: list[nn.Module] = []
        cur = input_dim
        for h in hidden_dims:
            layers.append(_dense_block(cur, h, dropout_p))
            cur = h
        layers.append(nn.Linear(cur, 1))
        self.net = nn.Sequential(*layers)

    def forward(self, x: torch.Tensor) -> torch.Tensor:  # (B,d)
        return self.net(x)


# ===================== DCN Cross Layers =====================
class CrossLayer(nn.Module):
    """Memory‑efficient DCN/ DCNv2 交叉层: x_{l+1} = x_0 * (w^T x_l) + b + x_l"""

    def __init__(self, d: int):
        super().__init__()
        self.w = nn.Parameter(torch.empty(d))          # (d,)
        self.b = nn.Parameter(torch.zeros(1, d))       # 显式 (B, d) 广播
        nn.init.kaiming_uniform_(self.w.unsqueeze(0), a=math.sqrt(5))

    def forward(self, x0: torch.Tensor, xl: torch.Tensor) -> torch.Tensor:  # both (B,d)
        # (B,1) = (B,d) @ (d,1)
        dot = F.linear(xl, self.w.unsqueeze(0))
        return x0 * dot + self.b + xl


# ===================== DCNv2 =====================
class DCNv2(nn.Module):
    """Deep & Cross Network V2"""

    def __init__(self, input_dim: int, num_cross_layers: int = 3, deep_hidden_dims: Sequence[int] = (256, 128), dropout_p: float = 0.3):
        super().__init__()
        # deep branch
        deep_layers: list[nn.Module] = []
        cur = input_dim
        for h in deep_hidden_dims:
            deep_layers.append(_dense_block(cur, h, dropout_p))
            cur = h
        self.deep_net = nn.Sequential(*deep_layers)

        # cross branch
        self.cross_net = nn.ModuleList([CrossLayer(input_dim) for _ in range(num_cross_layers)])

        # output
        self.output = nn.Linear(input_dim + deep_hidden_dims[-1], 1)

    def forward(self, x: torch.Tensor) -> torch.Tensor:  # (B,d)
        x0 = x
        xl = x
        for layer in self.cross_net:
            xl = layer(x0, xl)
        deep_out = self.deep_net(x)
        return self.output(torch.cat([xl, deep_out], dim=1))


# ===================== DCNv1 =====================
class DCNv1(nn.Module):
    """原版 DCN (2019)"""

    def __init__(self, input_dim: int, num_cross_layers: int = 3, deep_dims: Sequence[int] = (512, 256, 128), dropout_p: float = 0.3):
        super().__init__()
        self.cross = nn.ModuleList([CrossLayer(input_dim) for _ in range(num_cross_layers)])
        deep_layers: list[nn.Module] = []
        cur = input_dim
        for h in deep_dims:
            deep_layers.append(_dense_block(cur, h, dropout_p))
            cur = h
        self.deep = nn.Sequential(*deep_layers)
        self.output = nn.Linear(input_dim + deep_dims[-1], 1)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x0 = x
        xl = x
        for layer in self.cross:
            xl = layer(x0, xl)
        deep_out = self.deep(x)
        return self.output(torch.cat([xl, deep_out], dim=1))


# ===================== DLRM (dense only) =====================
class DLRM(nn.Module):
    """简化稠密版 DLRM (无稀疏 Embedding)"""

    def __init__(self, input_dim: int, bot_dims: Sequence[int] = (512, 256), top_dims: Sequence[int] = (256, 128, 1)):
        super().__init__()
        # bottom MLP
        bot_layers: list[nn.Module] = []
        cur = input_dim
        for h in bot_dims:
            bot_layers.append(_dense_block(cur, h, p=0.0))  # 推荐去掉 Dropout
            cur = h
        self.bottom = nn.Sequential(*bot_layers)

        self.inter_dim = bot_dims[-1] * (bot_dims[-1] - 1) // 2
        # top MLP
        top_layers: list[nn.Module] = []
        cur = bot_dims[-1] + self.inter_dim
        for h in top_dims:
            top_layers.append(nn.Linear(cur, h))
            if h != 1:
                top_layers.append(nn.ReLU(inplace=True))
            cur = h
        self.top = nn.Sequential(*top_layers)

    def forward(self, x: torch.Tensor) -> torch.Tensor:  # (B,d)
        z = F.normalize(self.bottom(x), p=2, dim=1)      # (B,h)
        # dot interaction (上三角无对角)
        B, h = z.size()
        inter = torch.bmm(z.unsqueeze(2), z.unsqueeze(1))  # (B,h,h)
        iu = torch.triu_indices(h, h, offset=1)
        inter_flat = torch.clamp(inter[:, iu[0], iu[1]], -10.0, 10.0)
        concat = torch.cat([z, inter_flat], dim=1)
        return self.top(concat)


# ===================== 工厂方法 =====================
_MODEL_REGISTRY = {
    "mlp": MLP,
    "dcnv2": DCNv2,
    "dcnv1": DCNv1,
    "dlrm": DLRM,
}

def build_model(model_type: str, input_dim: int, cfg: Dict[str, Any] | None = None):
    """根据模型类型和配置构建模型"""
    cfg = cfg or {}
    if model_type not in _MODEL_REGISTRY:
        raise ValueError(f"Unknown model_type {model_type}. Supported: {list(_MODEL_REGISTRY)}")

    # 强制把 list → tuple，防止 torch 嵌套报类型错
    def _t(key: str, default):
        v = cfg.get(key, default)
        return tuple(v) if isinstance(v, (list, tuple)) else v

    if model_type == "mlp":
        return MLP(input_dim,
                   hidden_dims=_t("hidden_dims", (256, 128)),
                   dropout_p=cfg.get("dropout_rate", 0.3))

    if model_type == "dcnv2":
        return DCNv2(input_dim,
                     num_cross_layers=cfg.get("cross_layers", 3),
                     deep_hidden_dims=_t("deep_layers", (256, 128)),
                     dropout_p=cfg.get("dropout_rate", 0.3))

    if model_type == "dcnv1":
        return DCNv1(input_dim,
                     num_cross_layers=cfg.get("cross_layers", 3),
                     deep_dims=_t("deep_dims", (512, 256, 128)),
                     dropout_p=cfg.get("dropout_rate", 0.3))

    if model_type == "dlrm":
        return DLRM(input_dim,
                    bot_dims=_t("bot_dims", (512, 256)),
                    top_dims=_t("top_dims", (256, 128, 1)))
