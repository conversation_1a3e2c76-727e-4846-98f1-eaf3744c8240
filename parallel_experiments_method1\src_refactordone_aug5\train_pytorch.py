# train_pytorch.py
"""
PyTorch基础版本训练脚本 - 用于对比和教学

⚠️  注意：这是基础实现版本，用于演示和对比
- 直接使用原始数据，无标准化
- 基本模型结构，无优化
- 预期结果：损失值高，学习效果差
- 用途：理解数据预处理的重要性

🚀 如需实际训练，请使用：train_pytorch_fixed.py
"""

import torch
import torch.nn as nn
import numpy as np
from torch.utils.data import TensorDataset, DataLoader
from sklearn.metrics import roc_auc_score
import argparse
import os
import sys
import logging
import json
import time
from datetime import datetime
from pathlib import Path

from config import PARALLEL_EXTENDED_CONFIG, MODEL_SPECIFIC_CONFIG

# 设置基本并行优化
def setup_basic_parallelism():
    """设置基本的并行配置"""
    cpu_count = os.cpu_count()

    # 设置PyTorch线程数
    torch.set_num_threads(cpu_count)

    # 设置环境变量
    os.environ['OMP_NUM_THREADS'] = str(cpu_count)
    os.environ['MKL_NUM_THREADS'] = str(cpu_count)

    logger.info(f"🔧 基本并行配置:")
    logger.info(f"  CPU核心数: {cpu_count}")
    logger.info(f"  PyTorch线程: {cpu_count}")
    logger.info(f"  OMP线程: {cpu_count}")
    logger.info(f"  MKL线程: {cpu_count}")

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 应用基本并行优化
setup_basic_parallelism()

class SimpleMLP(nn.Module):
    """简单的MLP模型 - PyTorch版本"""
    
    def __init__(self, input_dim, hidden_dims=[512, 256, 128], dropout_rate=0.2):
        super(SimpleMLP, self).__init__()
        
        layers = []
        prev_dim = input_dim
        
        # 构建隐藏层
        for hidden_dim in hidden_dims:
            layers.append(nn.Linear(prev_dim, hidden_dim))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout_rate))
            prev_dim = hidden_dim
        
        # 输出层
        layers.append(nn.Linear(prev_dim, 1))
        
        self.model = nn.Sequential(*layers)
        
        # 权重初始化
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            torch.nn.init.zeros_(module.bias)
    
    def forward(self, x):
        return self.model(x)

def load_processed_data(dataset_name: str):
    """加载预处理后的数据"""
    processed_dir = PARALLEL_EXTENDED_CONFIG['processed_data_dir']
    feature_file = os.path.join(processed_dir, f"{dataset_name}_features.npy")
    label_file = os.path.join(processed_dir, f"{dataset_name}_labels.npy")
    
    if not os.path.exists(feature_file) or not os.path.exists(label_file):
        logger.error(f"数据文件不存在: {feature_file} 或 {label_file}")
        return None, None
        
    try:
        features = torch.from_numpy(np.load(feature_file)).float()
        labels = torch.from_numpy(np.load(label_file)).float()
        logger.info(f"加载 {dataset_name} 数据: 特征 {features.shape}, 标签 {labels.shape}")
        return features, labels
    except Exception as e:
        logger.error(f"加载数据失败: {e}")
        return None, None

def evaluate_model(model, data_loader, device):
    """评估模型性能"""
    model.eval()
    total_loss = 0
    all_predictions = []
    all_labels = []
    criterion = nn.BCEWithLogitsLoss()
    
    with torch.no_grad():
        for features, labels in data_loader:
            features, labels = features.to(device), labels.to(device)
            outputs = model(features)
            loss = criterion(outputs, labels.unsqueeze(1))
            total_loss += loss.item()
            
            # 收集预测概率和真实标签
            probs = torch.sigmoid(outputs).cpu().numpy()
            all_predictions.extend(probs.flatten())
            all_labels.extend(labels.cpu().numpy().flatten())
    
    avg_loss = total_loss / len(data_loader)
    auc_score = roc_auc_score(all_labels, all_predictions)
    
    return avg_loss, auc_score

def train_model(model_type='mlp', epochs=None, learning_rate=None, batch_size=None):
    """训练模型"""
    logger.info(f"开始训练 {model_type} 模型...")
    
    # 加载数据
    X_train, y_train = load_processed_data('train')
    X_val, y_val = load_processed_data('validation')
    
    if X_train is None or X_val is None:
        logger.error("无法加载训练数据")
        return False
    
    # 获取模型配置
    config = MODEL_SPECIFIC_CONFIG.get(model_type, MODEL_SPECIFIC_CONFIG['mlp'])
    
    # 训练参数 - 支持命令行覆盖
    if epochs is None:
        epochs = config.get('epochs', 10)
    if learning_rate is None:
        learning_rate = config.get('learning_rate', 0.001)
    if batch_size is None:
        batch_size = config.get('batch_size', 1024)
    
    logger.info(f"训练配置:")
    logger.info(f"  Epochs: {epochs}")
    logger.info(f"  Learning Rate: {learning_rate}")
    logger.info(f"  Batch Size: {batch_size}")
    
    # 创建数据集和数据加载器
    train_dataset = TensorDataset(X_train, y_train)
    val_dataset = TensorDataset(X_val, y_val)

    # 基本的并行配置
    num_workers = min(4, os.cpu_count())  # 使用4个worker或CPU核心数（取较小值）

    logger.info(f"DataLoader配置: num_workers={num_workers}")

    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True if torch.cuda.is_available() else False
    )
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True if torch.cuda.is_available() else False
    )
    
    # 创建模型
    input_dim = X_train.shape[1]
    hidden_dims = config.get('hidden_dims', [512, 256, 128])
    model = SimpleMLP(input_dim=input_dim, hidden_dims=hidden_dims)
    
    # 设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.to(device)
    logger.info(f"使用设备: {device}")
    logger.info(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失函数和优化器
    criterion = nn.BCEWithLogitsLoss()
    optimizer = torch.optim.AdamW(model.parameters(), lr=learning_rate)
    
    # 训练循环
    best_val_auc = 0.0
    
    for epoch in range(epochs):
        start_time = time.time()
        
        # 训练阶段
        model.train()
        total_train_loss = 0
        num_batches = 0
        
        for features, labels in train_loader:
            features, labels = features.to(device), labels.to(device)
            
            optimizer.zero_grad()
            outputs = model(features)
            loss = criterion(outputs, labels.unsqueeze(1))
            loss.backward()
            optimizer.step()
            
            total_train_loss += loss.item()
            num_batches += 1
        
        avg_train_loss = total_train_loss / num_batches
        
        # 验证阶段
        val_loss, val_auc = evaluate_model(model, val_loader, device)
        
        epoch_time = time.time() - start_time
        
        logger.info(f"Epoch {epoch+1}/{epochs}: "
                   f"Train Loss: {avg_train_loss:.4f}, "
                   f"Val Loss: {val_loss:.4f}, "
                   f"Val AUC: {val_auc:.4f}, "
                   f"Time: {epoch_time:.2f}s")
        
        # 保存最佳模型
        if val_auc > best_val_auc:
            best_val_auc = val_auc
            logger.info(f"新的最佳验证AUC: {val_auc:.4f}")
    
    # 测试评估
    X_test, y_test = load_processed_data('test')
    if X_test is not None:
        test_dataset = TensorDataset(X_test, y_test)
        test_loader = DataLoader(
            test_dataset,
            batch_size=batch_size,
            shuffle=False,
            num_workers=num_workers,
            pin_memory=True if torch.cuda.is_available() else False
        )
        test_loss, test_auc = evaluate_model(model, test_loader, device)
        logger.info(f"测试结果: Loss: {test_loss:.4f}, AUC: {test_auc:.4f}")
    
    logger.info("模型训练完成")
    return True

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='PyTorch模型训练')
    parser.add_argument('--model_type', choices=['mlp'], default='mlp',
                       help='模型类型')
    parser.add_argument('--epochs', type=int, default=None,
                       help='Number of training epochs (overrides config).')
    parser.add_argument('--batch_size', type=int, default=None,
                       help='Batch size for training (overrides config).')
    parser.add_argument('--learning_rate', type=float, default=None,
                       help='Learning rate (overrides config).')
    
    args = parser.parse_args()
    
    logger.info("="*60)
    logger.info("🤖 PyTorch模型训练开始")
    logger.info("="*60)
    
    # 检查预处理数据是否存在
    processed_dir = PARALLEL_EXTENDED_CONFIG['processed_data_dir']
    if not os.path.exists(processed_dir):
        logger.error("预处理数据目录不存在，请先运行数据预处理")
        logger.error("运行命令: python run_parallel_processing.py")
        return False
    
    # 训练模型 - 传递命令行参数
    success = train_model(
        model_type=args.model_type,
        epochs=args.epochs,
        learning_rate=args.learning_rate,
        batch_size=args.batch_size
    )
    
    if success:
        logger.info("✅ 模型训练成功完成")
    else:
        logger.error("❌ 模型训练失败")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
