"""Common model building utilities."""
from typing import Dict, Any, Optional

# Dynamic imports mapping (matches train_loss_optimized.py)
MODEL_IMPORTS = {
    "models": "from models import build_model as _build_model_impl",
    "models_o3_orig": "from models_o3_orig import build_model as _build_model_impl", 
    "models_o3": "from models_o3 import build_model as _build_model_impl",
    "models_o3_cc": "from models_o3_cc import build_model as _build_model_impl"
}

def build_model(model_type: str, input_dim: int, config: Optional[Dict[str, Any]] = None,
                model_version: str = "models_o3_cc") -> Any:
    """Build model with specified type and configuration.
    
    Args:
        model_type: One of ['mlp', 'dcnv2', 'dcnv1', 'dlrm']
        input_dim: Input feature dimension
        config: Model-specific configuration dict
        model_version: Model version to use (default: models_o3_cc)
        
    Returns:
        nn.Module: The constructed model
    """
    if model_version not in MODEL_IMPORTS:
        raise ValueError(f"Unknown model version: {model_version}. Available: {list(MODEL_IMPORTS.keys())}")
    
    # Dynamic import of the appropriate model builder
    exec(MODEL_IMPORTS[model_version], globals())
    
    # Call the imported build function
    model = _build_model_impl(model_type, input_dim, config or {})
    
    return model