# adaptive_init.py
"""
自适应模型初始化模块
根据实际数据分布动态调整模型初始化，防止梯度爆炸
"""

import torch
import torch.nn as nn
import numpy as np
import logging
from typing import Tuple, Optional, Dict
import warnings

logger = logging.getLogger(__name__)


class AdaptiveInitializer:
    """自适应初始化器，根据数据特征动态调整模型初始化"""
    
    def __init__(self, 
                 target_output_std: float = 1.0,
                 max_attempts: int = 5,
                 verbose: bool = True):
        """
        Args:
            target_output_std: 目标输出标准差
            max_attempts: 最大尝试次数
            verbose: 是否打印详细信息
        """
        self.target_output_std = target_output_std
        self.max_attempts = max_attempts
        self.verbose = verbose
        
    def analyze_data_distribution(self, data_sample: torch.Tensor) -> Dict[str, float]:
        """分析数据分布特征"""
        stats = {
            'mean': data_sample.mean().item(),
            'std': data_sample.std().item(),
            'min': data_sample.min().item(),
            'max': data_sample.max().item(),
            'abs_mean': data_sample.abs().mean().item(),
            'has_nan': torch.isnan(data_sample).any().item(),
            'has_inf': torch.isinf(data_sample).any().item()
        }
        
        # 计算异常值比例
        z_scores = torch.abs((data_sample - stats['mean']) / (stats['std'] + 1e-8))
        stats['outlier_ratio'] = (z_scores > 3).float().mean().item()
        
        if self.verbose:
            logger.info("数据分布分析:")
            logger.info(f"  均值: {stats['mean']:.4f}")
            logger.info(f"  标准差: {stats['std']:.4f}")
            logger.info(f"  范围: [{stats['min']:.4f}, {stats['max']:.4f}]")
            logger.info(f"  异常值比例: {stats['outlier_ratio']:.2%}")
            
        return stats
        
    def check_forward_pass(self, 
                          model: nn.Module, 
                          sample_data: torch.Tensor,
                          device: torch.device) -> Tuple[float, float]:
        """检查模型前向传播的输出分布"""
        model.eval()
        with torch.no_grad():
            output = model(sample_data.to(device))
            output_mean = output.mean().item()
            output_std = output.std().item()
            
        if self.verbose:
            logger.info(f"模型输出统计: mean={output_mean:.4f}, std={output_std:.4f}")
            
        return output_mean, output_std
        
    def adaptive_scale_weights(self, 
                              model: nn.Module,
                              current_std: float,
                              target_std: float = None) -> float:
        """自适应缩放模型权重"""
        if target_std is None:
            target_std = self.target_output_std
            
        if current_std < 1e-6:
            logger.warning("输出标准差接近0，跳过缩放")
            return 1.0
            
        scale_factor = target_std / current_std
        
        # 限制缩放因子的范围，避免过度调整
        scale_factor = np.clip(scale_factor, 0.1, 10.0)
        
        if self.verbose:
            logger.info(f"应用缩放因子: {scale_factor:.4f}")
        
        # 只缩放权重，不缩放bias和norm层
        with torch.no_grad():
            for name, param in model.named_parameters():
                if 'weight' in name and 'norm' not in name:
                    param.data *= scale_factor
                    
        return scale_factor
        
    def smart_init_model(self,
                        model: nn.Module,
                        sample_data: torch.Tensor,
                        sample_labels: torch.Tensor,
                        device: torch.device) -> Dict[str, float]:
        """智能初始化模型"""
        
        # 1. 分析输入数据
        data_stats = self.analyze_data_distribution(sample_data)
        
        # 2. 检查数据质量
        if data_stats['has_nan'] or data_stats['has_inf']:
            raise ValueError("数据包含NaN或Inf值，请先清理数据")
            
        # 3. 根据数据分布调整初始化策略
        if data_stats['std'] > 10:
            logger.warning(f"输入数据标准差很大({data_stats['std']:.2f})，建议加强预处理")
            
        # 4. 迭代调整初始化
        best_std = float('inf')
        best_state = None
        
        for attempt in range(self.max_attempts):
            if self.verbose:
                logger.info(f"\n尝试 {attempt + 1}/{self.max_attempts}")
                
            # 检查当前输出
            output_mean, output_std = self.check_forward_pass(model, sample_data, device)
            
            # 记录最佳状态
            if abs(output_std - self.target_output_std) < abs(best_std - self.target_output_std):
                best_std = output_std
                best_state = model.state_dict()
                
            # 如果已经接近目标，停止
            if abs(output_std - self.target_output_std) < 0.1:
                if self.verbose:
                    logger.info(f"✅ 初始化成功！输出std={output_std:.4f}")
                break
                
            # 如果输出过大或过小，调整权重
            if output_std > self.target_output_std * 2 or output_std < self.target_output_std * 0.5:
                self.adaptive_scale_weights(model, output_std)
            else:
                # 如果接近目标但不够精确，做小幅调整
                scale = 1.0 + (self.target_output_std - output_std) / output_std * 0.5
                self.adaptive_scale_weights(model, output_std, output_std * scale)
                
        # 5. 恢复最佳状态
        if best_state is not None:
            model.load_state_dict(best_state)
            
        # 6. 最终检查
        final_mean, final_std = self.check_forward_pass(model, sample_data, device)
        
        return {
            'initial_data_std': data_stats['std'],
            'final_output_mean': final_mean,
            'final_output_std': final_std,
            'target_std': self.target_output_std,
            'success': abs(final_std - self.target_output_std) < 0.5
        }


def robust_data_preprocessing(X: np.ndarray, 
                             y: np.ndarray = None,
                             clip_percentile: float = 0.1,
                             final_clip_range: Tuple[float, float] = (-10, 10)) -> np.ndarray:
    """
    鲁棒的数据预处理，处理各种数据质量问题
    
    Args:
        X: 输入特征
        y: 标签（可选）
        clip_percentile: 裁剪的百分位数
        final_clip_range: 最终裁剪范围
        
    Returns:
        处理后的特征
    """
    X_processed = X.copy()
    
    # 1. 处理NaN和Inf
    if np.isnan(X_processed).any() or np.isinf(X_processed).any():
        logger.warning("检测到NaN或Inf值，将替换为0")
        X_processed = np.nan_to_num(X_processed, nan=0.0, posinf=0.0, neginf=0.0)
    
    # 2. 逐特征裁剪极端值
    for i in range(X_processed.shape[1]):
        col = X_processed[:, i]
        
        # 跳过常数列
        if col.std() < 1e-8:
            continue
            
        # 计算裁剪边界
        lower = np.percentile(col, clip_percentile)
        upper = np.percentile(col, 100 - clip_percentile)
        
        # 如果上下界相同，使用均值±3倍标准差
        if upper - lower < 1e-8:
            mean = col.mean()
            std = col.std()
            lower = mean - 3 * std
            upper = mean + 3 * std
            
        X_processed[:, i] = np.clip(col, lower, upper)
    
    # 3. 标准化
    from sklearn.preprocessing import RobustScaler
    scaler = RobustScaler()  # 对异常值更鲁棒
    X_processed = scaler.fit_transform(X_processed)
    
    # 4. 最终范围限制
    X_processed = np.clip(X_processed, final_clip_range[0], final_clip_range[1])
    
    # 5. 可选：非线性压缩
    # X_processed = np.tanh(X_processed * 0.5)  # 将值压缩到[-1, 1]
    
    return X_processed


def create_adaptive_model(model_class: type,
                         model_config: dict,
                         sample_data: torch.Tensor,
                         sample_labels: torch.Tensor,
                         device: torch.device = torch.device('cpu')) -> nn.Module:
    """
    创建并自适应初始化模型
    
    Args:
        model_class: 模型类
        model_config: 模型配置
        sample_data: 样本数据用于自适应初始化
        sample_labels: 样本标签
        device: 设备
        
    Returns:
        初始化好的模型
    """
    # 创建模型
    model = model_class(**model_config).to(device)
    
    # 创建自适应初始化器
    initializer = AdaptiveInitializer(
        target_output_std=1.0,  # 目标输出标准差
        max_attempts=5,
        verbose=True
    )
    
    # 执行自适应初始化
    init_stats = initializer.smart_init_model(
        model=model,
        sample_data=sample_data,
        sample_labels=sample_labels,
        device=device
    )
    
    if init_stats['success']:
        logger.info("✅ 模型初始化成功")
    else:
        logger.warning("⚠️ 模型初始化可能不够理想，但可以继续训练")
        
    return model


# 使用示例
if __name__ == "__main__":
    # 测试自适应初始化
    from models_o3_cc import DCNv2
    
    # 模拟一些极端数据
    batch_size = 32
    input_dim = 160
    
    # 创建具有不同尺度的特征
    data = []
    for i in range(input_dim):
        if i % 3 == 0:
            # 小尺度特征
            data.append(np.random.normal(0, 0.1, batch_size))
        elif i % 3 == 1:
            # 大尺度特征
            data.append(np.random.normal(1000, 500, batch_size))
        else:
            # 极端值特征
            col = np.random.normal(0, 1, batch_size)
            col[0] = 999999  # 异常值
            data.append(col)
            
    X = np.array(data).T
    y = np.random.randint(0, 2, batch_size)
    
    # 预处理
    X_processed = robust_data_preprocessing(X)
    
    # 转换为tensor
    X_tensor = torch.from_numpy(X_processed).float()
    y_tensor = torch.from_numpy(y).float()
    
    # 创建自适应初始化的模型
    model = create_adaptive_model(
        model_class=DCNv2,
        model_config={
            'input_dim': input_dim,
            'num_cross_layers': 3,
            'deep_hidden_dims': (256, 128),
            'dropout_p': 0.3,
            'norm_type': 'layer'
        },
        sample_data=X_tensor,
        sample_labels=y_tensor
    )
    
    print("模型创建成功！")