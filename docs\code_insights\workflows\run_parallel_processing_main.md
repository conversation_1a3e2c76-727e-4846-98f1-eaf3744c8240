# 工作流：并行数据处理 run_parallel_processing_main

## 工作流概述

主要的数据预处理入口，负责协调数据分析、并行处理和特征提取的完整流程。

## 调用图

```mermaid
sequenceDiagram
    participant CLI as 命令行
    participant Main as run_parallel_processing.main()
    participant Check as check_data_paths()
    participant Analyze as run_data_analysis()
    participant DA as DataAnalyzer
    participant Process as run_parallel_processing()
    participant PP as ParallelProcessor
    participant Worker as _process_single_file_worker()
    participant IP as IntelligentPreprocessor
    participant Save as _merge_and_save_results()
    participant Meta as save_feature_metadata()

    CLI->>Main: python run_parallel_processing.py --workers 4
    Main->>Check: 检查数据路径配置
    Check-->>Main: 返回有效路径
    
    Main->>Analyze: 运行数据分析
    Analyze->>DA: DataAnalyzer()
    DA->>DA: analyze_all_datasets()
    loop 每个数据集
        DA->>DA: analyze_dataset(dataset_type)
        DA->>DA: _analyze_parquet_files()
        DA->>DA: _compute_statistics()
    end
    DA->>DA: save_analysis_results()
    DA-->>Analyze: 返回分析结果
    
    Main->>Process: 运行并行处理
    Process->>PP: ParallelProcessor(num_workers=4)
    PP->>PP: process_all_datasets()
    
    loop 每个数据集
        PP->>PP: process_dataset(dataset_type)
        PP->>PP: process_files_parallel(files)
        
        par 并行处理
            PP->>Worker: 处理文件1
            Worker->>IP: process_parquet_chunk()
            IP-->>Worker: 返回features, labels
            
            PP->>Worker: 处理文件2
            Worker->>IP: process_parquet_chunk()
            IP-->>Worker: 返回features, labels
            
            PP->>Worker: 处理文件N
            Worker->>IP: process_parquet_chunk()
            IP-->>Worker: 返回features, labels
        end
        
        PP->>Save: 合并并保存结果
        Save->>Save: np.save('features.npy')
        Save->>Save: np.save('labels.npy')
    end
    
    PP-->>Process: 返回处理统计
    
    Main->>Meta: 提取特征元数据
    Meta->>Meta: extract_feature_metadata()
    Meta->>Meta: save_to_json()
    Meta-->>Main: 元数据保存完成
    
    Main-->>CLI: 处理完成
```

## 逐函数IO说明

### main() - 主入口函数
**文件**: `run_parallel_processing.py:108-152`

**输入**:
- 命令行参数: `--workers` (工作进程数)
- 命令行参数: `--track-only` (仅追踪模式)
- 环境变量: 数据路径配置

**处理**:
1. 解析命令行参数
2. 检查数据路径有效性
3. 协调数据分析和并行处理流程
4. 生成处理报告

**输出**:
- 处理统计信息打印到控制台
- 进程追踪日志（如果启用）

### check_data_paths() - 数据路径检查
**文件**: `run_parallel_processing.py:31-56`

**输入**:
- `TRAIN_DATA_DIR`: 训练数据路径（从config导入）
- `VALIDATION_DATA_DIR`: 验证数据路径
- `TEST_DATA_DIR`: 测试数据路径

**处理**:
- 验证路径是否为默认占位符
- 检查路径是否存在（S3或本地）
- 记录有效和无效路径

**输出**:
- `valid_paths`: 字典，包含有效的数据路径
- 日志输出到logger

### run_data_analysis() - 运行数据分析
**文件**: `run_parallel_processing.py:58-75`

**输入**:
- `data_paths`: 有效数据路径字典

**处理**:
1. 创建DataAnalyzer实例
2. 分析每个数据集的统计特性
3. 保存分析结果

**输出**:
- 分析结果保存到 `data_analysis_results.json`
- 统计信息打印到控制台

### run_parallel_processing() - 运行并行处理
**文件**: `run_parallel_processing.py:77-106`

**输入**:
- `data_paths`: 有效数据路径字典
- `num_workers`: 工作进程数（可选）
- `track_only`: 是否仅追踪模式

**处理**:
1. 创建ParallelProcessor实例
2. 并行处理每个数据集
3. 合并处理结果
4. 记录性能统计

**输出**:
- NPY格式的特征和标签文件
- 处理统计信息

## ParallelProcessor类方法

### __init__() - 初始化
**文件**: `parallel_processor.py:45-76`

**输入**:
- `num_workers`: 工作进程数
- `chunk_size`: 每个任务的数据块大小
- `memory_limit_gb`: 内存限制

**处理**:
- 设置并行参数
- 初始化进程池
- 配置内存管理

**输出**:
- 初始化的ParallelProcessor实例

### process_dataset() - 处理单个数据集
**文件**: `parallel_processor.py:156-198`

**输入**:
- `data_dir`: 数据目录路径
- `dataset_type`: 数据集类型（train/validation/test）
- `output_dir`: 输出目录

**处理**:
1. 扫描parquet文件
2. 创建处理任务
3. 分发任务到工作进程
4. 收集处理结果
5. 合并并保存

**输出**:
- `features.npy`: 特征数组文件
- `labels.npy`: 标签数组文件
- 处理统计字典

### process_files_parallel() - 并行处理文件
**文件**: `parallel_processor.py:200-245`

**输入**:
- `file_paths`: 文件路径列表
- `output_dir`: 输出目录

**处理**:
1. 创建任务队列
2. 启动工作进程
3. 分发任务
4. 监控进度
5. 收集结果

**输出**:
- 处理结果列表（features和labels）

### _process_single_file_worker() - 工作进程处理函数
**文件**: `parallel_processor.py:247-285`

**输入**:
- `file_path`: 单个parquet文件路径
- `chunk_size`: 批处理大小
- `stats`: 数据统计信息（用于标准化）

**处理**:
1. 读取parquet文件
2. 分块处理数据
3. 特征提取
4. 标准化
5. 错误处理

**输出**:
- `features`: numpy数组，形状(n_samples, 177)
- `labels`: numpy数组，形状(n_samples,)

### _merge_and_save_results() - 合并保存结果
**文件**: `parallel_processor.py:287-320`

**输入**:
- `results`: 处理结果列表
- `output_dir`: 输出目录
- `dataset_type`: 数据集类型

**处理**:
1. 合并所有features和labels
2. 验证数据完整性
3. 保存为NPY格式
4. 生成元数据

**输出**:
- 保存的NPY文件路径
- 数据集统计信息

## IntelligentPreprocessor类方法

### process_parquet_chunk() - 处理Parquet块
**文件**: `preprocess.py:45-92`

**输入**:
- `file_path`: Parquet文件路径
- `chunk_size`: 块大小
- `analysis_results`: 数据分析结果（可选）

**处理**:
1. 读取Parquet文件
2. 应用智能预处理策略
3. 特征工程
4. 数据清洗
5. 类型转换

**输出**:
- `processed_features`: 处理后的特征
- `labels`: 标签数组

## 配置参数

### 并行处理配置
```python
PARALLEL_CONFIG = {
    'num_workers': 88,           # r5.24xlarge实例
    'chunk_size': 200000,        # 每个任务20万条
    'memory_limit_gb': 700,      # 内存上限
    'method': 'spawn',           # 多进程启动方法
    'prefetch_factor': 2,        # 预取因子
    'timeout': 300               # 任务超时（秒）
}
```

### 性能优化配置
```python
PERFORMANCE_CONFIG = {
    'enable_memory_mapping': True,   # 内存映射
    'use_compression': False,         # 不压缩（速度优先）
    'batch_size': 10000,             # 批处理大小
    'max_retries': 3,                # 失败重试次数
    'gc_interval': 100               # 垃圾回收间隔
}
```

## 错误处理

### 常见错误及处理
1. **内存不足**：自动降低chunk_size
2. **文件损坏**：跳过并记录
3. **进程崩溃**：重试机制
4. **超时**：终止并重新分配任务

## 性能指标

| 指标 | 典型值 | 说明 |
|------|--------|------|
| 文件处理速度 | 5-10 文件/分钟 | 取决于文件大小 |
| 样本处理速度 | 200K 样本/秒 | 88进程并行 |
| 内存使用 | 400-600GB | 峰值使用量 |
| CPU利用率 | 85-95% | 多进程充分利用 |

**Why — 设计动机与取舍**

这个工作流设计的核心是"可靠性"和"效率"。选择多进程而非多线程是因为Python GIL的限制；分块处理是为了控制内存使用；重试机制确保了容错性；进度监控提供了可观察性。整个流程设计为流式处理，可以处理超过内存容量的数据集。通过合理的任务分配和负载均衡，实现了近线性的扩展性。