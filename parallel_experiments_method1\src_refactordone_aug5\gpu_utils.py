# gpu_utils.py
"""
GPU工具函数模块
提供GPU相关的辅助功能
"""

import torch
import logging
import os

logger = logging.getLogger(__name__)

def get_device_config():
    """
    智能检测设备配置
    
    Returns:
        tuple: (device_type, device_count, device)
            - device_type: 'cpu', 'single_gpu', 'multi_gpu'
            - device_count: GPU数量
            - device: torch.device对象
    """
    if torch.cuda.is_available():
        device_count = torch.cuda.device_count()
        device = torch.device("cuda:0")
        
        if device_count > 1:
            logger.info(f"检测到{device_count}个GPU，将使用多GPU训练")
            return 'multi_gpu', device_count, device
        else:
            # 获取GPU信息
            gpu_name = torch.cuda.get_device_name(0)
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1e9
            logger.info(f"检测到单GPU: {gpu_name} ({gpu_memory:.1f}GB)")
            return 'single_gpu', 1, device
    else:
        logger.info("未检测到GPU，使用CPU训练")
        return 'cpu', 0, torch.device("cpu")

def auto_select_batch_size(model, device, initial_batch_size=1024, input_dim=39):
    """
    自动选择最大可用批次大小
    
    Args:
        model: 模型对象
        device: torch设备
        initial_batch_size: 初始批次大小
        input_dim: 输入特征维度
        
    Returns:
        int: 优化后的批次大小
    """
    if device.type != 'cuda':
        return initial_batch_size
    
    # 获取GPU内存信息
    gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / 1e9
    
    # 简单的启发式规则
    if "4090" in torch.cuda.get_device_name(0) or "4080" in torch.cuda.get_device_name(0):
        # RTX 4090/4080有24GB内存
        max_batch_size = 8192
    elif "A10" in torch.cuda.get_device_name(0):
        # A10有24GB内存，但带宽较低
        max_batch_size = 4096
    else:
        # 其他GPU，保守估计
        max_batch_size = int(gpu_memory_gb * 1024 / 8)  # 假设每个样本需要8MB
    
    # 选择2的幂次作为批次大小
    batch_size = initial_batch_size
    while batch_size * 2 <= max_batch_size:
        batch_size *= 2
    
    logger.info(f"GPU内存: {gpu_memory_gb:.1f}GB, 自动选择批次大小: {batch_size}")
    return batch_size

def log_gpu_memory_usage(prefix=""):
    """记录GPU内存使用情况"""
    if torch.cuda.is_available():
        allocated = torch.cuda.memory_allocated() / 1e9
        reserved = torch.cuda.memory_reserved() / 1e9
        logger.info(f"{prefix}GPU内存使用: 已分配={allocated:.2f}GB, 已保留={reserved:.2f}GB")

def setup_distributed(rank, world_size):
    """
    设置分布式训练环境（未来多GPU支持）
    
    Args:
        rank: 当前进程的rank
        world_size: 总进程数
    """
    os.environ['MASTER_ADDR'] = 'localhost'
    os.environ['MASTER_PORT'] = '12355'
    
    # 初始化进程组
    torch.distributed.init_process_group(
        backend='nccl' if torch.cuda.is_available() else 'gloo',
        rank=rank,
        world_size=world_size
    )
    
    if torch.cuda.is_available():
        torch.cuda.set_device(rank)

def cleanup_distributed():
    """清理分布式训练环境"""
    if torch.distributed.is_initialized():
        torch.distributed.destroy_process_group()