# models_o3_cc.py
"""
基于 models_o3.py 的修订版（参考 code_analysis_cc.md）
改动要点：
1. 修复 CrossLayer 维度广播问题 → 点乘实现改为 (xl * w).sum(dim=1, keepdim=True)
2. DLRM.forward 去掉 F.normalize 与 torch.clamp，防止梯度/数值被截断
3. 为所有输出 Linear 层添加 He(<PERSON>) 初始化 & bias 零初始化
其余代码结构保持不变，确保向后兼容 build_model 接口。
"""

from __future__ import annotations

import math
from typing import Sequence, Dict, Any

import torch
import torch.nn as nn
import torch.nn.functional as F

# ========= 工具函数 =========

def _dense_block(in_dim: int, out_dim: int, p: float, norm_type: str = "layer") -> nn.Sequential:
    """Linear → ReLU → Dropout → LayerNorm/BatchNorm"""
    norm_layer = nn.LayerNorm(out_dim) if norm_type == "layer" else nn.BatchNorm1d(out_dim)
    return nn.Sequential(
        nn.Linear(in_dim, out_dim),
        nn.ReLU(inplace=True),
        nn.Dropout(p),
        norm_layer,
    )


def _init_output(layer: nn.Linear):
    """He 初始化输出层权重，bias 置 0"""
    if isinstance(layer, nn.Linear):
        nn.init.kaiming_uniform_(layer.weight, a=math.sqrt(5))
        if layer.bias is not None:
            nn.init.zeros_(layer.bias)

# ========= MLP =========


class MLP(nn.Module):
    """多层感知机"""

    def __init__(self, input_dim: int, hidden_dims: Sequence[int] = (256, 128), dropout_p: float = 0.3, norm_type: str = "layer"):
        super().__init__()
        layers: list[nn.Module] = []
        cur = input_dim
        for h in hidden_dims:
            layers.append(_dense_block(cur, h, dropout_p, norm_type))
            cur = h
        layers.append(nn.Linear(cur, 1))
        self.net = nn.Sequential(*layers)

        _init_output(self.net[-1])

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        return self.net(x)


# ========= DCN Cross Layer =========


class CrossLayer(nn.Module):
    """Memory-efficient DCN/DCNv2 交叉层: x_{l+1} = x_0 * (w^T x_l) + b + x_l"""

    def __init__(self, d: int):
        super().__init__()
        self.w = nn.Parameter(torch.empty(1, d))  # (1,d)
        self.b = nn.Parameter(torch.zeros(1, d))
        nn.init.kaiming_uniform_(self.w, a=math.sqrt(5))

    def forward(self, x0: torch.Tensor, xl: torch.Tensor) -> torch.Tensor:
        # 维度修复: (B,1) = sum((B,d) * (1,d), dim=1)
        dot = (xl * self.w).sum(dim=1, keepdim=True)  # (B,1)
        return x0 * dot + self.b + xl


# ========= DCNv2 =========


class DCNv2(nn.Module):
    """Deep & Cross Network V2"""

    def __init__(self, input_dim: int, num_cross_layers: int = 3, deep_hidden_dims: Sequence[int] = (256, 128), dropout_p: float = 0.3, norm_type: str = "layer"):
        super().__init__()
        # deep branch
        deep_layers: list[nn.Module] = []
        cur = input_dim
        for h in deep_hidden_dims:
            deep_layers.append(_dense_block(cur, h, dropout_p, norm_type))
            cur = h
        self.deep_net = nn.Sequential(*deep_layers)

        # cross branch
        self.cross_net = nn.ModuleList([CrossLayer(input_dim) for _ in range(num_cross_layers)])

        # output
        self.output = nn.Linear(input_dim + deep_hidden_dims[-1], 1)
        _init_output(self.output)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x0 = x
        xl = x
        for layer in self.cross_net:
            xl = layer(x0, xl)
        deep_out = self.deep_net(x)
        return self.output(torch.cat([xl, deep_out], dim=1))


# ========= DCNv1 =========


class DCNv1(nn.Module):
    """原版 DCN (2019)"""

    def __init__(self, input_dim: int, num_cross_layers: int = 3, deep_dims: Sequence[int] = (512, 256, 128), dropout_p: float = 0.3, norm_type: str = "layer"):
        super().__init__()
        self.cross = nn.ModuleList([CrossLayer(input_dim) for _ in range(num_cross_layers)])
        deep_layers: list[nn.Module] = []
        cur = input_dim
        for h in deep_dims:
            deep_layers.append(_dense_block(cur, h, dropout_p, norm_type))
            cur = h
        self.deep = nn.Sequential(*deep_layers)
        self.output = nn.Linear(input_dim + deep_dims[-1], 1)
        _init_output(self.output)

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x0 = x
        xl = x
        for layer in self.cross:
            xl = layer(x0, xl)
        deep_out = self.deep(x)
        return self.output(torch.cat([xl, deep_out], dim=1))


# ========= DLRM (dense only) =========


class DLRM(nn.Module):
    """简化稠密版 DLRM (无稀疏 Embedding)"""

    def __init__(self, input_dim: int, bot_dims: Sequence[int] = (512, 256), top_dims: Sequence[int] = (256, 128, 1), norm_type: str = "layer"):
        super().__init__()
        # bottom MLP
        bot_layers: list[nn.Module] = []
        cur = input_dim
        for h in bot_dims:
            bot_layers.append(_dense_block(cur, h, p=0.0, norm_type=norm_type))  # 去掉 Dropout
            cur = h
        self.bottom = nn.Sequential(*bot_layers)

        self.inter_dim = bot_dims[-1] * (bot_dims[-1] - 1) // 2
        # top MLP
        top_layers: list[nn.Module] = []
        cur = bot_dims[-1] + self.inter_dim
        for h in top_dims:
            top_layers.append(nn.Linear(cur, h))
            if h != 1:
                top_layers.append(nn.ReLU(inplace=True))
            cur = h
        self.top = nn.Sequential(*top_layers)
        _init_output(self.top[-1])  # 最后一个线性层输出

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        z = self.bottom(x)  # 取消 F.normalize，保留原始特征尺度
        # dot interaction (上三角无对角)
        B, h = z.size()
        inter = torch.bmm(z.unsqueeze(2), z.unsqueeze(1))  # (B,h,h)
        iu = torch.triu_indices(h, h, offset=1)
        inter_flat = inter[:, iu[0], iu[1]]  # 去掉 clamp，保留真实值
        concat = torch.cat([z, inter_flat], dim=1)
        return self.top(concat)


# ========= 工厂方法 =========

_MODEL_REGISTRY = {
    "mlp": MLP,
    "dcnv2": DCNv2,
    "dcnv1": DCNv1,
    "dlrm": DLRM,
}


def build_model(model_type: str, input_dim: int, cfg: Dict[str, Any] | None = None):
    """根据模型类型和配置构建模型 (与旧接口保持一致)"""
    cfg = cfg or {}
    if model_type not in _MODEL_REGISTRY:
        raise ValueError(f"Unknown model_type {model_type}. Supported: {list(_MODEL_REGISTRY)}")

    def _t(key: str, default):
        v = cfg.get(key, default)
        return tuple(v) if isinstance(v, (list, tuple)) else v

    if model_type == "mlp":
        return MLP(
            input_dim,
            hidden_dims=_t("hidden_dims", (256, 128)),
            dropout_p=cfg.get("dropout_rate", 0.3),
            norm_type=cfg.get("norm_type", "layer"),
        )

    if model_type == "dcnv2":
        return DCNv2(
            input_dim,
            num_cross_layers=cfg.get("cross_layers", 3),
            deep_hidden_dims=_t("deep_layers", (256, 128)),
            dropout_p=cfg.get("dropout_rate", 0.3),
            norm_type=cfg.get("norm_type", "layer"),
        )

    if model_type == "dcnv1":
        return DCNv1(
            input_dim,
            num_cross_layers=cfg.get("cross_layers", 3),
            deep_dims=_t("deep_dims", (512, 256, 128)),
            dropout_p=cfg.get("dropout_rate", 0.3),
            norm_type=cfg.get("norm_type", "layer"),
        )

    if model_type == "dlrm":
        return DLRM(
            input_dim,
            bot_dims=_t("bot_dims", (512, 256)),
            top_dims=_t("top_dims", (256, 128, 1)),
            norm_type=cfg.get("norm_type", "layer"),
        ) 