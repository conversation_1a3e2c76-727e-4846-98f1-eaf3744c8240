# COMMANDS.md - 正确的命令执行方式 - 必须记住！

## ⚠️ 关键路径信息 - 每次必看！
- **工作目录**: `C:\Users\<USER>\Downloads\win_project\recommendation\parallel_experiments_method1`
- **正确Python路径**: `../recommendation_venv_gpu/Scripts/python.exe` ✅
- **错误Python路径**: `../recommendation_venv/Scripts/python.exe` ❌ (没有PyTorch!)
- **测试套件路径**: `tests/test_compliance.py` (使用GPU虚拟环境运行)

## 重要：处理Unicode输出
Windows环境下，需要设置UTF-8编码以避免Unicode错误：
```bash
set PYTHONIOENCODING=utf-8
```

## 正确的命令格式

### 1. 合规测试
```bash
# 快速测试
../recommendation_venv_gpu/Scripts/python.exe tests/test_compliance.py --quick

# 完整测试
../recommendation_venv_gpu/Scripts/python.exe tests/test_compliance.py
```

### 2. 训练命令
```bash
# 基础训练
../recommendation_venv_gpu/Scripts/python.exe src/train_loss_optimized.py --model_type dcnv2 --epochs 1 --pos_weight_strategy sqrt_balanced

# 带特征选择的训练
../recommendation_venv_gpu/Scripts/python.exe src/train_loss_optimized.py --model_type dcnv2 --epochs 10 --exclude_groups noise
```

### 3. 并行预处理（两个重要入口之一）
```bash
# 使用track-only模式快速验证
../recommendation_venv_gpu/Scripts/python.exe src/run_parallel_processing.py --workers 2 --track-only

# 完整运行（会生成NPY文件）
../recommendation_venv_gpu/Scripts/python.exe src/run_parallel_processing.py --workers 2
```

### 4. 快速测试
```bash
../recommendation_venv_gpu/Scripts/python.exe quick_test.py
```

## 常见错误及解决方案

### 错误1: ModuleNotFoundError: No module named 'torch'
**原因**: 使用了错误的虚拟环境
**解决**: 必须使用 `../recommendation_venv_gpu/` 而不是 `../recommendation_venv/`

### 错误2: /usr/bin/bash: line 1: cd: No such file or directory
**原因**: 在Windows上使用了Linux风格的路径
**解决**: 使用正确的Windows路径格式，直接在当前目录执行

### 错误3: 找不到文件或目录
**原因**: 相对路径错误
**解决**: 始终从 `parallel_experiments_method1` 目录执行命令

## ⭐ 最常用的测试命令组合

### 完整测试流程（依次执行）
```bash
# 1. 运行11个合规性测试
../recommendation_venv_gpu/Scripts/python.exe tests/test_compliance.py --quick

# 2. 测试并行预处理入口
../recommendation_venv_gpu/Scripts/python.exe src/run_parallel_processing.py --workers 2 --track-only

# 3. 测试训练入口
../recommendation_venv_gpu/Scripts/python.exe src/train_loss_optimized.py --model_type dcnv2 --epochs 2 --pos_weight_strategy sqrt_balanced
```

## 重要提醒
1. **永远不要使用** `python` 直接执行，必须使用完整路径
2. **永远不要使用** `/Users/<USER>
3. **永远记住** 当前目录是 `C:\Users\<USER>\Downloads\win_project\recommendation\parallel_experiments_method1`
4. **每次执行前** 先确认路径：`dir` 或 `pwd`
5. **执行测试前** 必须重新阅读本文件确认命令正确

## 验证命令是否正确
执行前先测试Python环境：
```bash
../recommendation_venv_gpu/Scripts/python.exe -c "import torch; print('PyTorch available:', torch.cuda.is_available())"
```

如果返回 `PyTorch available: True`，说明环境正确。