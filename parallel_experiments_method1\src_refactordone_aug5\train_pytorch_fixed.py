# train_pytorch_fixed.py
"""
PyTorch生产版本训练脚本 - 包含完整优化和最佳实践

✅ 生产就绪版本，包含以下优化：
- 数据标准化（StandardScaler）
- 类别平衡处理（WeightedRandomSampler）
- 批标准化（BatchNorm1d）
- 梯度裁剪（防止梯度爆炸）
- 学习率调度（ReduceLROnPlateau）
- 加权损失函数（处理类别不平衡）

📊 预期结果：损失值正常（1-4），模型能够学习
🎯 推荐用于实际训练和生产环境
"""

# -------------------- 线程与并行环境初始化 --------------------
# 必须在 **导入 torch / numpy / sklearn 之前** 设置环境变量，
# 否则 OpenMP/MKL 会读取默认值导致只使用单核。

import os as _os
from config import PERFORMANCE_CONFIG as _PERF_CFG

# 如果用户通过环境变量显式指定了线程数，则尊重用户设置；否则按性能配置自动设置。
_os.environ.setdefault("OMP_NUM_THREADS", str(_PERF_CFG['omp_num_threads']))
_os.environ.setdefault("MKL_NUM_THREADS", str(_PERF_CFG['mkl_num_threads']))

# ----------------------------------------------------------------

import torch

# 设置 PyTorch 计算线程数（需在首次用到张量计算前调用）
torch.set_num_threads(_PERF_CFG['torch_threads'])
# inter-op 线程适当小一些即可
torch.set_num_interop_threads(min(4, _PERF_CFG['torch_threads'] // 2))

import torch.nn as nn
import numpy as np
from torch.utils.data import TensorDataset, DataLoader, WeightedRandomSampler
from sklearn.metrics import roc_auc_score, average_precision_score
from sklearn.preprocessing import StandardScaler
import argparse
import os
import sys
import logging
import json
import time
from datetime import datetime
from pathlib import Path

# 新增PERFORMANCE_CONFIG导入，用于DataLoader并行化
from config import PARALLEL_EXTENDED_CONFIG, MODEL_SPECIFIC_CONFIG, PERFORMANCE_CONFIG

# ============================================================================
# 🔧 模型版本配置 - 只需修改这一行即可切换模型版本
# ============================================================================
MODEL_VERSION = "models_o3_cc"  # 选择: "models", "models_o3_orig", "models_o3", "models_o3_cc"
# ============================================================================

# 自动导入对应的模型构建函数
MODEL_IMPORTS = {
    "models": "from models import build_model",
    "models_o3_orig": "from models_o3_orig import build_model",
    "models_o3": "from models_o3 import build_model",
    "models_o3_cc": "from models_o3_cc import build_model"
}

if MODEL_VERSION not in MODEL_IMPORTS:
    raise ValueError(f"Unknown MODEL_VERSION: {MODEL_VERSION}. Available: {list(MODEL_IMPORTS.keys())}")

# 动态导入
exec(MODEL_IMPORTS[MODEL_VERSION])

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 使用本地配置，确保完全独立
MAIN_MODEL_CONFIG = MODEL_SPECIFIC_CONFIG  # 使用本地配置
USE_MAIN_CONFIG = True
logger.info("✅ 使用本地模型配置（独立版本）")

class SimpleMLP(nn.Module):
    """简单的MLP模型 - PyTorch版本"""
    
    def __init__(self, input_dim, hidden_dims=[512, 256, 128], dropout_rate=0.3):
        super(SimpleMLP, self).__init__()
        
        layers = []
        prev_dim = input_dim
        
        # 构建隐藏层
        for hidden_dim in hidden_dims:
            layers.append(nn.Linear(prev_dim, hidden_dim))
            layers.append(nn.BatchNorm1d(hidden_dim))  # 添加批标准化
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout_rate))
            prev_dim = hidden_dim
        
        # 输出层
        layers.append(nn.Linear(prev_dim, 1))
        
        self.model = nn.Sequential(*layers)
        
        # 权重初始化
        self.apply(self._init_weights)
        
    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            torch.nn.init.xavier_uniform_(module.weight)
            torch.nn.init.zeros_(module.bias)
    
    def forward(self, x):
        return self.model(x)

def load_and_normalize_data():
    """加载并标准化数据"""
    processed_dir = PARALLEL_EXTENDED_CONFIG['processed_data_dir']
    
    # 加载所有数据
    datasets = {}
    for dataset_name in ['train', 'validation', 'test']:
        feature_file = os.path.join(processed_dir, f"{dataset_name}_features.npy")
        label_file = os.path.join(processed_dir, f"{dataset_name}_labels.npy")
        
        if os.path.exists(feature_file) and os.path.exists(label_file):
            features = np.load(feature_file)
            labels = np.load(label_file)
            datasets[dataset_name] = (features, labels)
            logger.info(f"加载 {dataset_name} 数据: 特征 {features.shape}, 标签 {labels.shape}")
        else:
            logger.error(f"数据文件不存在: {feature_file} 或 {label_file}")
            return None
    
    if 'train' not in datasets:
        logger.error("训练数据不存在")
        return None
    
    # 使用训练数据拟合标准化器
    X_train, y_train = datasets['train']
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    
    logger.info("数据标准化完成")
    logger.info(f"标准化后训练数据: mean={X_train_scaled.mean():.6f}, std={X_train_scaled.std():.6f}")
    
    # 标准化其他数据集
    result = {}
    result['train'] = (torch.from_numpy(X_train_scaled).float(), torch.from_numpy(y_train).float())
    
    for dataset_name in ['validation', 'test']:
        if dataset_name in datasets:
            X, y = datasets[dataset_name]
            X_scaled = scaler.transform(X)
            result[dataset_name] = (torch.from_numpy(X_scaled).float(), torch.from_numpy(y).float())
            logger.info(f"标准化 {dataset_name} 数据: mean={X_scaled.mean():.6f}, std={X_scaled.std():.6f}")
    
    return result

def create_balanced_dataloader(features, labels, batch_size, shuffle=True, use_weighted_sampler=False):
    """根据数据规模智能选择采样器，并使用性能配置加速DataLoader

    Args:
        use_weighted_sampler: 是否使用加权采样器。如果使用pos_weight，建议设为False避免双重平衡
    """

    # ------------------- DataLoader 并行/性能参数 -------------------
    perf_cfg = PERFORMANCE_CONFIG  # 从全局配置获取性能参数
    loader_kwargs = {
        'batch_size': batch_size,
        'pin_memory': perf_cfg['pin_memory'],
    }

    # 只有当 num_workers > 0 时才设置相关参数，避免 PyTorch 警告
    if perf_cfg['dataloader_workers'] > 0:
        loader_kwargs.update({
            'num_workers': perf_cfg['dataloader_workers'],
            'prefetch_factor': perf_cfg['prefetch_factor'],
            'persistent_workers': perf_cfg['persistent_workers'],
        })

    dataset = TensorDataset(features, labels)

    # ------------------- 是否需要使用加权采样器 -------------------
    if not shuffle:
        # 验证 / 测试集无需打乱
        return DataLoader(dataset, shuffle=False, **loader_kwargs)

    if not use_weighted_sampler:
        # 不使用加权采样器，直接随机打乱
        logger.info("使用标准随机采样（类别平衡由pos_weight处理）")
        return DataLoader(dataset, shuffle=True, **loader_kwargs)

    labels_np = labels.numpy().astype(int)
    total_samples = len(labels_np)

    # PyTorch multinomial 上限: 2^24 ≈ 16,777,216
    if total_samples >= 2 ** 24:
        # 数据过大，跳过 WeightedRandomSampler，直接随机打乱
        logger.warning(
            "样本数超过 2^24，跳过 WeightedRandomSampler，改用随机 shuffle (pos_weight 已在损失函数中处理类别不平衡)"
        )
        return DataLoader(dataset, shuffle=True, **loader_kwargs)

    # 计算类别权重
    class_counts = np.bincount(labels_np)
    weights = np.zeros(total_samples, dtype=np.float64)
    for idx, label in enumerate(labels_np):
        weights[idx] = total_samples / (2.0 * class_counts[label])

    sampler = WeightedRandomSampler(
        weights=torch.from_numpy(weights),
        num_samples=total_samples,
        replacement=True,
    )

    logger.info("使用加权随机采样器处理类别不平衡")
    return DataLoader(dataset, sampler=sampler, **loader_kwargs)

def evaluate_model(model, data_loader, device, pos_weight=None):
    """评估模型性能 - 使用与训练一致的损失函数，返回loss, ROC AUC, PR AUC"""
    model.eval()
    total_loss = 0
    all_predictions = []
    all_labels = []

    # 使用与训练一致的损失函数
    if pos_weight is not None:
        pos_weight = pos_weight.to(device)
        criterion = nn.BCEWithLogitsLoss(pos_weight=pos_weight)
    else:
        criterion = nn.BCEWithLogitsLoss()

    with torch.no_grad():
        for features, labels in data_loader:
            features, labels = features.to(device), labels.to(device)
            outputs = model(features)
            loss = criterion(outputs, labels.unsqueeze(1))
            total_loss += loss.item()

            # 收集预测概率和真实标签
            probs = torch.sigmoid(outputs).cpu().numpy()
            all_predictions.extend(probs.flatten())
            all_labels.extend(labels.cpu().numpy().flatten())

    avg_loss = total_loss / len(data_loader)

    # 计算ROC AUC，处理可能的异常情况
    try:
        roc_auc = roc_auc_score(all_labels, all_predictions)
    except ValueError as e:
        logger.warning(f"ROC AUC计算失败: {e}")
        roc_auc = 0.5

    # 计算PR AUC (Average Precision Score)
    try:
        pr_auc = average_precision_score(all_labels, all_predictions)
    except ValueError as e:
        logger.warning(f"PR AUC计算失败: {e}")
        pr_auc = 0.5

    return avg_loss, roc_auc, pr_auc

def calculate_optimal_pos_weight(y_train, strategy='balanced', max_weight=5.0):
    """计算最优的pos_weight值

    Args:
        y_train: 训练标签
        strategy: 计算策略 ('balanced', 'sqrt_balanced', 'log_balanced')
        max_weight: 最大权重限制
    """
    labels_np = y_train.numpy() if hasattr(y_train, 'numpy') else y_train
    pos_count = np.sum(labels_np)
    neg_count = len(labels_np) - pos_count

    if pos_count == 0:
        logger.warning("训练数据中没有正样本！")
        return 1.0

    # 计算基础比例
    ratio = neg_count / pos_count

    if strategy == 'balanced':
        pos_weight_value = ratio
    elif strategy == 'sqrt_balanced':
        # 平方根平衡 - 更温和的平衡策略
        pos_weight_value = np.sqrt(ratio)
    elif strategy == 'log_balanced':
        # 对数平衡 - 最温和的平衡策略
        pos_weight_value = np.log(1 + ratio)
    else:
        pos_weight_value = 1.0

    # 限制权重范围
    pos_weight_value = np.clip(pos_weight_value, 1.0, max_weight)

    logger.info(f"数据分布分析:")
    logger.info(f"  总样本数: {len(labels_np):,}")
    logger.info(f"  正样本数: {pos_count:,} ({pos_count/len(labels_np):.4f})")
    logger.info(f"  负样本数: {neg_count:,} ({neg_count/len(labels_np):.4f})")
    logger.info(f"  不平衡比例: {ratio:.2f}:1")
    logger.info(f"  pos_weight策略: {strategy}")
    logger.info(f"  计算得到的pos_weight: {pos_weight_value:.3f}")

    return pos_weight_value

def train_model(model_type='mlp', epochs=None, learning_rate=None, batch_size=None,
                norm_type='layer', pos_weight_strategy='balanced', use_weighted_sampler=False):
    """训练模型 - 纯训练函数，返回训练结果

    Args:
        model_type: 模型类型
        epochs: 训练轮数
        learning_rate: 学习率
        batch_size: 批次大小
        norm_type: 归一化类型
        pos_weight_strategy: pos_weight计算策略
        use_weighted_sampler: 是否使用加权采样器
    """
    logger.info(f"开始训练 {model_type} 模型...")
    logger.info(f"pos_weight策略: {pos_weight_strategy}")
    logger.info(f"使用加权采样器: {use_weighted_sampler}")

    # 加载并标准化数据
    data = load_and_normalize_data()
    if data is None:
        logger.error("无法加载训练数据")
        return None

    X_train, y_train = data['train']
    X_val, y_val = data['validation']

    # 获取模型配置 - 智能选择配置来源
    if USE_MAIN_CONFIG and model_type in MAIN_MODEL_CONFIG:
        # 使用主项目的配置（推荐）
        config = MAIN_MODEL_CONFIG[model_type].copy()
        logger.info(f"📋 使用主项目配置 for {model_type}")
    else:
        # 回退到本地配置
        config = MODEL_SPECIFIC_CONFIG.get(model_type, MODEL_SPECIFIC_CONFIG['mlp']).copy()
        logger.info(f"📋 使用本地配置 for {model_type}")

    # 为了保持并行实验的优化特性，调整一些参数
    if model_type != 'mlp':  # 非MLP模型使用主项目配置但调整训练参数
        config['epochs'] = min(config.get('epochs', 5), 10)  # 限制最大epochs
        config['batch_size'] = min(config.get('batch_size', 1024), 2048)  # 适中的batch size

    # 训练参数 - 支持命令行覆盖
    if epochs is None:
        epochs = config.get('epochs', 10)
    if learning_rate is None:
        learning_rate = config.get('learning_rate', 0.001)
    if batch_size is None:
        batch_size = config.get('batch_size', 1024)

    # 智能学习率设置 - 根据模型类型和批次大小调整
    base_lr = learning_rate
    if model_type == 'dcnv2':
        # DCNv2模型通常需要较小的学习率
        learning_rate = min(base_lr, 0.0005)
    elif model_type == 'dlrm':
        # DLRM模型需要更小的学习率
        learning_rate = min(base_lr, 0.0003)
    else:
        # MLP等其他模型可以使用稍高的学习率
        learning_rate = min(base_lr, 0.002)

    # 根据批次大小调整学习率（线性缩放规则）
    if batch_size > 1024:
        lr_scale = batch_size / 1024
        learning_rate = learning_rate * np.sqrt(lr_scale)  # 平方根缩放更稳定
        logger.info(f"根据批次大小调整学习率: {base_lr:.6f} -> {learning_rate:.6f}")

    logger.info(f"训练配置:")
    logger.info(f"  Epochs: {epochs}")
    logger.info(f"  Learning Rate: {learning_rate:.6f}")
    logger.info(f"  Batch Size: {batch_size}")

    # 创建数据加载器 - 智能选择平衡策略
    if use_weighted_sampler:
        logger.info("使用WeightedRandomSampler处理类别不平衡")
        train_loader = create_balanced_dataloader(X_train, y_train, batch_size, shuffle=True, use_weighted_sampler=True)
    else:
        logger.info("使用pos_weight处理类别不平衡，不使用WeightedRandomSampler")
        train_loader = create_balanced_dataloader(X_train, y_train, batch_size, shuffle=True, use_weighted_sampler=False)

    val_loader = create_balanced_dataloader(X_val, y_val, batch_size, shuffle=False, use_weighted_sampler=False)

    # 创建模型
    input_dim = X_train.shape[1]

    if model_type == 'mlp':
        # 使用原有的SimpleMLP（保持向后兼容）
        hidden_dims = config.get('hidden_dims', [256, 128, 64])  # 减小模型复杂度
        model = SimpleMLP(input_dim=input_dim, hidden_dims=hidden_dims)
    else:
        # 使用主项目的模型
        model_cfg = config.copy()  # 使用当前配置

        # 如果使用 models_o3.py 或 models_o3_cc.py，添加 norm_type 配置
        if MODEL_VERSION in ["models_o3", "models_o3_cc"]:
            model_cfg['norm_type'] = norm_type
            logger.info(f"使用归一化类型: {norm_type}")

        model = build_model(model_type, input_dim, model_cfg)

    # 设备
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    model.to(device)
    logger.info(f"使用设备: {device}")
    logger.info(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")

    # 计算最优的pos_weight
    if use_weighted_sampler:
        # 如果使用加权采样器，则不使用pos_weight避免双重平衡
        pos_weight_value = 1.0
        pos_weight = torch.tensor([pos_weight_value])
        logger.info("使用WeightedRandomSampler，pos_weight设为1.0避免双重平衡")
    else:
        # 使用智能pos_weight计算
        pos_weight_value = calculate_optimal_pos_weight(y_train, pos_weight_strategy)
        pos_weight = torch.tensor([pos_weight_value])

    criterion = nn.BCEWithLogitsLoss(pos_weight=pos_weight.to(device))

    # 优化器设置 - 根据模型类型调整
    if model_type in ['dcnv2', 'dlrm']:
        # 复杂模型使用更小的weight_decay
        optimizer = torch.optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=0.005)
    else:
        # 简单模型使用标准weight_decay
        optimizer = torch.optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=0.01)

    # 改进的学习率调度器 - 更激进的衰减策略
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', factor=0.7, patience=3, min_lr=1e-6
    )

    logger.info(f"优化器配置:")
    logger.info(f"  优化器: AdamW")
    logger.info(f"  Weight Decay: {optimizer.param_groups[0]['weight_decay']}")
    logger.info(f"  调度器: ReduceLROnPlateau (factor=0.7, patience=3)")

    # 训练循环 - 增强监控和早停
    best_val_auc = 0.0
    best_val_loss = float('inf')
    final_train_loss = 0.0
    patience_counter = 0
    early_stopping_patience = 7  # 早停耐心值

    logger.info("="*60)
    logger.info("开始训练循环")
    logger.info("="*60)

    # 🔍 评估初始状态 (第一个epoch之前)
    logger.info("🔍 评估模型初始状态...")
    init_train_loss, init_train_roc_auc, init_train_pr_auc = evaluate_model(model, train_loader, device, pos_weight)
    init_val_loss, init_val_roc_auc, init_val_pr_auc = evaluate_model(model, val_loader, device, pos_weight)

    logger.info("📊 初始状态指标:")
    logger.info(f"  训练集 - Loss: {init_train_loss:.6f}, ROC AUC: {init_train_roc_auc:.6f}, PR AUC: {init_train_pr_auc:.6f}")
    logger.info(f"  验证集 - Loss: {init_val_loss:.6f}, ROC AUC: {init_val_roc_auc:.6f}, PR AUC: {init_val_pr_auc:.6f}")
    logger.info("="*60)

    for epoch in range(epochs):
        start_time = time.time()

        # 训练阶段
        model.train()
        total_train_loss = 0
        num_batches = 0

        # 添加梯度监控
        total_grad_norm = 0

        for features, labels in train_loader:
            features, labels = features.to(device), labels.to(device)

            optimizer.zero_grad()
            outputs = model(features)
            loss = criterion(outputs, labels.unsqueeze(1))

            # 梯度裁剪防止梯度爆炸
            loss.backward()
            grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()

            total_train_loss += loss.item()
            total_grad_norm += grad_norm.item()
            num_batches += 1

        avg_train_loss = total_train_loss / num_batches
        avg_grad_norm = total_grad_norm / num_batches
        final_train_loss = avg_train_loss

        # 验证阶段 - 使用与训练一致的损失函数
        val_loss, val_roc_auc, val_pr_auc = evaluate_model(model, val_loader, device, pos_weight)

        # 更新学习率
        old_lr = optimizer.param_groups[0]['lr']
        scheduler.step(val_loss)
        new_lr = optimizer.param_groups[0]['lr']

        epoch_time = time.time() - start_time

        # 详细的训练日志 - 包含ROC AUC和PR AUC
        logger.info(f"Epoch {epoch+1}/{epochs}: "
                   f"Train Loss: {avg_train_loss:.6f}, "
                   f"Val Loss: {val_loss:.6f}, "
                   f"Val ROC AUC: {val_roc_auc:.6f}, "
                   f"Val PR AUC: {val_pr_auc:.6f}, "
                   f"Grad Norm: {avg_grad_norm:.4f}, "
                   f"LR: {new_lr:.2e}, "
                   f"Time: {epoch_time:.2f}s")

        # 学习率变化提醒
        if new_lr != old_lr:
            logger.info(f"📉 学习率调整: {old_lr:.2e} -> {new_lr:.2e}")

        # 保存最佳模型和早停逻辑 - 使用ROC AUC作为主要指标
        if val_roc_auc > best_val_auc:
            best_val_auc = val_roc_auc
            best_val_loss = val_loss
            patience_counter = 0
            logger.info(f"🎯 新的最佳验证ROC AUC: {val_roc_auc:.6f}, PR AUC: {val_pr_auc:.6f}")
        else:
            patience_counter += 1
            if patience_counter >= early_stopping_patience:
                logger.info(f"⏹️ 早停触发 (patience={early_stopping_patience})")
                break

    # 测试评估
    test_loss = None
    test_roc_auc = None
    test_pr_auc = None
    if 'test' in data:
        X_test, y_test = data['test']
        test_loader = create_balanced_dataloader(X_test, y_test, batch_size, shuffle=False, use_weighted_sampler=False)
        test_loss, test_roc_auc, test_pr_auc = evaluate_model(model, test_loader, device, pos_weight)
        logger.info(f"测试结果: Loss: {test_loss:.4f}, ROC AUC: {test_roc_auc:.4f}, PR AUC: {test_pr_auc:.4f}")

    logger.info("模型训练完成")

    # 训练总结
    logger.info("="*60)
    logger.info("🎯 训练完成 - 最终结果:")
    logger.info(f"  最佳验证Loss: {float(best_val_loss):.6f}")
    logger.info(f"  最佳验证ROC AUC: {best_val_auc:.6f}")
    if test_loss is not None:
        logger.info(f"  测试Loss: {test_loss:.6f}")
        logger.info(f"  测试ROC AUC: {test_roc_auc:.6f}")
        logger.info(f"  测试PR AUC: {test_pr_auc:.6f}")
    logger.info(f"  模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    logger.info(f"  最终学习率: {optimizer.param_groups[0]['lr']:.2e}")
    logger.info("="*60)

    # 返回训练结果字典 - 确保所有数值都是Python原生类型
    results = {
        'model_type': model_type,
        'pos_weight_strategy': pos_weight_strategy,
        'pos_weight_value': float(pos_weight_value),
        'use_weighted_sampler': use_weighted_sampler,
        'epochs': epochs,
        'learning_rate': float(learning_rate),
        'batch_size': batch_size,
        'final_train_loss': float(final_train_loss),
        'best_val_loss': float(best_val_loss),
        'best_val_roc_auc': float(best_val_auc),
        'test_loss': float(test_loss) if test_loss is not None else None,
        'test_roc_auc': float(test_roc_auc) if test_roc_auc is not None else None,
        'test_pr_auc': float(test_pr_auc) if test_pr_auc is not None else None,
        'model_params': sum(p.numel() for p in model.parameters()),
        'final_lr': float(optimizer.param_groups[0]['lr']),
        'device': str(device),
        'norm_type': norm_type
    }

    return results

def main():
    """主函数 - 纯训练脚本，输出结果到标准输出"""
    parser = argparse.ArgumentParser(description='修复版PyTorch模型训练 - Loss优化版本')
    parser.add_argument('--model_type', choices=['mlp', 'dcnv2', 'dcnv1', 'dlrm'], default='mlp',
                       help='模型类型')
    parser.add_argument('--epochs', type=int, default=None,
                       help='Number of training epochs (overrides config).')
    parser.add_argument('--batch_size', type=int, default=None,
                       help='Batch size for training (overrides config).')
    parser.add_argument('--learning_rate', type=float, default=None,
                       help='Learning rate (overrides config).')
    parser.add_argument('--norm_type', choices=['layer', 'batch'], default='batch',
                       help='Normalization type (only for models_o3): layer or batch')
    parser.add_argument('--pos_weight_strategy', choices=['balanced', 'sqrt_balanced', 'log_balanced'],
                       default='balanced', help='pos_weight计算策略')
    parser.add_argument('--use_weighted_sampler', action='store_true',
                       help='使用WeightedRandomSampler (默认使用pos_weight)')

    args = parser.parse_args()

    logger.info("="*60)
    logger.info("🤖 修复版PyTorch模型训练开始 - Loss优化版本")
    logger.info(f"📦 使用模型版本: {MODEL_VERSION}")
    if MODEL_VERSION == "models_o3":
        logger.info(f"🔧 归一化类型: {args.norm_type}")
    logger.info(f"⚖️ pos_weight策略: {args.pos_weight_strategy}")
    logger.info(f"🎯 使用加权采样器: {args.use_weighted_sampler}")
    logger.info("="*60)

    # 检查预处理数据是否存在
    processed_dir = PARALLEL_EXTENDED_CONFIG['processed_data_dir']
    if not os.path.exists(processed_dir):
        logger.error("预处理数据目录不存在，请先运行数据预处理")
        logger.error("运行命令: python run_parallel_processing.py")
        return None

    # 训练模型 - 传递命令行参数
    results = train_model(
        model_type=args.model_type,
        epochs=args.epochs,
        learning_rate=args.learning_rate,
        batch_size=args.batch_size,
        norm_type=args.norm_type,
        pos_weight_strategy=args.pos_weight_strategy,
        use_weighted_sampler=args.use_weighted_sampler
    )

    if results is not None:
        logger.info("✅ 模型训练成功完成")
        # 输出结果到标准输出，供上层脚本解析
        print("TRAINING_RESULTS_START")
        print(json.dumps(results, indent=2))
        print("TRAINING_RESULTS_END")
        return results
    else:
        logger.error("❌ 模型训练失败")
        return None

if __name__ == "__main__":
    results = main()
    sys.exit(0 if results is not None else 1)
