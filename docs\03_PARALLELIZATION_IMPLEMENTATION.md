# 并行化实现详细分析

## 概述

本文档详细说明推荐系统中并行化技术的实现，包括数据预处理、特征工程、模型训练等各个环节的并行优化策略。并行化是提升系统性能的关键技术，通过充分利用多核CPU和GPU资源，显著加速处理速度。

## 1. 并行化的层次结构

### 1.1 系统架构中的并行层次

```
┌─────────────────────────────────────────┐
│         进程级并行 (Process Level)         │
│   - 多进程数据预处理                        │
│   - 分布式特征工程                         │
└─────────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────────┐
│         线程级并行 (Thread Level)          │
│   - 多线程数据加载                         │
│   - 并发I/O操作                           │
└─────────────────────────────────────────┘
                    ↓
┌─────────────────────────────────────────┐
│         指令级并行 (Instruction Level)      │
│   - 向量化计算 (SIMD)                     │
│   - GPU并行计算 (CUDA)                    │
└─────────────────────────────────────────┘
```

### 1.2 项目中的并行化应用场景

1. **数据预处理并行化**：run_parallel_processing.py
2. **特征工程并行化**：特征提取和转换
3. **DataLoader并行化**：多进程数据加载
4. **模型训练并行化**：GPU加速和多GPU训练
5. **批量预测并行化**：并行推理

## 2. 数据预处理的并行化实现

### 2.1 多进程数据处理框架

```python
# run_parallel_processing.py 的核心实现
import multiprocessing as mp
from concurrent.futures import ProcessPoolExecutor, as_completed
import numpy as np
import pandas as pd
from tqdm import tqdm
import time

class ParallelDataProcessor:
    def __init__(self, n_workers=None):
        """
        初始化并行处理器
        n_workers: 工作进程数，None表示使用所有CPU核心
        """
        if n_workers is None:
            self.n_workers = mp.cpu_count()
        else:
            self.n_workers = min(n_workers, mp.cpu_count())
        
        print(f"初始化并行处理器，使用 {self.n_workers} 个工作进程")
        
        # 共享内存管理器
        self.manager = mp.Manager()
        self.shared_dict = self.manager.dict()
        self.progress_queue = self.manager.Queue()
    
    def split_data_for_parallel(self, data, n_splits=None):
        """
        将数据智能分割成多个块用于并行处理
        考虑数据大小和内存限制
        """
        if n_splits is None:
            n_splits = self.n_workers
        
        total_size = len(data)
        chunk_size = max(1, total_size // n_splits)
        
        chunks = []
        for i in range(0, total_size, chunk_size):
            chunk = data[i:i + chunk_size]
            chunks.append({
                'data': chunk,
                'chunk_id': len(chunks),
                'start_idx': i,
                'end_idx': min(i + chunk_size, total_size)
            })
        
        print(f"数据分割完成: {total_size} 条记录分成 {len(chunks)} 块")
        return chunks
    
    def process_chunk(self, chunk_info):
        """
        处理单个数据块的函数
        这个函数会在子进程中执行
        """
        chunk_id = chunk_info['chunk_id']
        data = chunk_info['data']
        
        try:
            # 实际的处理逻辑
            processed_data = []
            
            for idx, record in enumerate(data):
                # 特征提取
                features = self.extract_features(record)
                
                # 特征转换
                transformed = self.transform_features(features)
                
                # 数据清洗
                cleaned = self.clean_data(transformed)
                
                processed_data.append(cleaned)
                
                # 更新进度
                if idx % 100 == 0:
                    self.progress_queue.put({
                        'chunk_id': chunk_id,
                        'progress': idx / len(data)
                    })
            
            return {
                'chunk_id': chunk_id,
                'processed_data': processed_data,
                'success': True
            }
            
        except Exception as e:
            return {
                'chunk_id': chunk_id,
                'error': str(e),
                'success': False
            }
    
    def extract_features(self, record):
        """
        从原始记录中提取特征
        """
        features = {}
        
        # 用户特征
        features['user_id'] = record.get('user_id')
        features['user_age'] = record.get('age', 0)
        features['user_gender'] = record.get('gender', 'unknown')
        
        # 物品特征
        features['item_id'] = record.get('item_id')
        features['item_category'] = record.get('category')
        features['item_price'] = record.get('price', 0)
        
        # 交互特征
        features['timestamp'] = record.get('timestamp')
        features['action_type'] = record.get('action')
        
        return features
    
    def transform_features(self, features):
        """
        特征转换和工程
        """
        transformed = features.copy()
        
        # 数值特征标准化
        if 'user_age' in transformed:
            transformed['user_age_normalized'] = transformed['user_age'] / 100.0
        
        # 类别特征编码
        if 'user_gender' in transformed:
            transformed['user_gender_encoded'] = 1 if transformed['user_gender'] == 'M' else 0
        
        # 时间特征提取
        if 'timestamp' in transformed:
            ts = pd.to_datetime(transformed['timestamp'])
            transformed['hour'] = ts.hour
            transformed['day_of_week'] = ts.dayofweek
            transformed['is_weekend'] = 1 if ts.dayofweek >= 5 else 0
        
        return transformed
    
    def clean_data(self, data):
        """
        数据清洗
        """
        # 处理缺失值
        for key, value in data.items():
            if value is None or (isinstance(value, float) and np.isnan(value)):
                data[key] = self.get_default_value(key)
        
        return data
    
    def get_default_value(self, key):
        """
        获取特征的默认值
        """
        defaults = {
            'user_age': 25,
            'item_price': 0,
            'user_gender_encoded': 0,
            'hour': 12,
            'day_of_week': 3,
            'is_weekend': 0
        }
        return defaults.get(key, 0)
    
    def run_parallel_processing(self, data, track_only=False):
        """
        主并行处理函数
        track_only: 如果为True，只跟踪进度不保存结果（用于测试）
        """
        start_time = time.time()
        
        # 数据分块
        chunks = self.split_data_for_parallel(data)
        
        # 使用进程池执行并行处理
        results = []
        failed_chunks = []
        
        with ProcessPoolExecutor(max_workers=self.n_workers) as executor:
            # 提交所有任务
            future_to_chunk = {
                executor.submit(self.process_chunk, chunk): chunk
                for chunk in chunks
            }
            
            # 进度条
            with tqdm(total=len(chunks), desc="Processing chunks") as pbar:
                for future in as_completed(future_to_chunk):
                    chunk = future_to_chunk[future]
                    
                    try:
                        result = future.result(timeout=300)  # 5分钟超时
                        
                        if result['success']:
                            if not track_only:
                                results.append(result['processed_data'])
                            pbar.update(1)
                        else:
                            failed_chunks.append(result['chunk_id'])
                            print(f"Chunk {result['chunk_id']} failed: {result.get('error')}")
                            
                    except TimeoutError:
                        failed_chunks.append(chunk['chunk_id'])
                        print(f"Chunk {chunk['chunk_id']} timed out")
                    except Exception as e:
                        failed_chunks.append(chunk['chunk_id'])
                        print(f"Chunk {chunk['chunk_id']} error: {str(e)}")
        
        # 合并结果
        if not track_only and results:
            final_result = self.merge_results(results)
        else:
            final_result = None
        
        elapsed_time = time.time() - start_time
        
        # 性能统计
        stats = {
            'total_chunks': len(chunks),
            'successful_chunks': len(chunks) - len(failed_chunks),
            'failed_chunks': len(failed_chunks),
            'elapsed_time': elapsed_time,
            'records_per_second': len(data) / elapsed_time if elapsed_time > 0 else 0,
            'worker_efficiency': (len(chunks) - len(failed_chunks)) / (self.n_workers * elapsed_time)
        }
        
        print(f"\n并行处理完成:")
        print(f"  - 总耗时: {elapsed_time:.2f} 秒")
        print(f"  - 处理速度: {stats['records_per_second']:.0f} 条/秒")
        print(f"  - 成功率: {stats['successful_chunks']}/{stats['total_chunks']}")
        print(f"  - 工作进程效率: {stats['worker_efficiency']:.2%}")
        
        return final_result, stats
    
    def merge_results(self, results):
        """
        合并并行处理的结果
        """
        merged = []
        for chunk_result in results:
            merged.extend(chunk_result)
        return merged
```

### 2.2 进程间通信和同步

```python
class ProcessCommunicator:
    def __init__(self):
        self.manager = mp.Manager()
        self.shared_state = self.manager.dict()
        self.lock = self.manager.Lock()
        self.event = self.manager.Event()
        self.queue = self.manager.Queue()
    
    def update_shared_state(self, key, value):
        """
        线程安全地更新共享状态
        """
        with self.lock:
            self.shared_state[key] = value
    
    def aggregate_results(self, worker_id, result):
        """
        聚合工作进程的结果
        """
        self.queue.put({
            'worker_id': worker_id,
            'result': result,
            'timestamp': time.time()
        })
    
    def wait_for_workers(self, n_workers):
        """
        等待所有工作进程完成
        """
        completed = 0
        results = []
        
        while completed < n_workers:
            try:
                result = self.queue.get(timeout=1)
                results.append(result)
                completed += 1
            except:
                continue
        
        return results
```

## 3. DataLoader的并行化

### 3.1 多进程数据加载实现

```python
import torch
from torch.utils.data import Dataset, DataLoader
import numpy as np

class ParallelDataset(Dataset):
    def __init__(self, data_path, transform=None):
        self.data_path = data_path
        self.transform = transform
        
        # 预先加载数据索引，而不是全部数据
        self.index = self._build_index()
    
    def _build_index(self):
        """
        构建数据索引，支持快速随机访问
        """
        index = []
        # 假设数据分片存储
        for shard_id in range(100):  # 100个数据分片
            shard_file = f"{self.data_path}/shard_{shard_id}.npz"
            if os.path.exists(shard_file):
                # 只记录文件位置，不加载数据
                with np.load(shard_file) as data:
                    n_samples = len(data['features'])
                    for i in range(n_samples):
                        index.append((shard_id, i))
        return index
    
    def __len__(self):
        return len(self.index)
    
    def __getitem__(self, idx):
        """
        按需加载数据，支持并行访问
        """
        shard_id, sample_id = self.index[idx]
        
        # 加载对应的数据分片
        shard_file = f"{self.data_path}/shard_{shard_id}.npz"
        with np.load(shard_file, mmap_mode='r') as data:
            features = data['features'][sample_id]
            label = data['labels'][sample_id]
        
        # 应用转换
        if self.transform:
            features = self.transform(features)
        
        return {
            'features': torch.FloatTensor(features),
            'label': torch.FloatTensor([label])
        }

class OptimizedDataLoader:
    def __init__(self, dataset, batch_size=32, num_workers=4):
        """
        优化的数据加载器配置
        """
        self.dataset = dataset
        self.batch_size = batch_size
        self.num_workers = num_workers
        
        # 根据系统资源自动调整参数
        self._optimize_parameters()
    
    def _optimize_parameters(self):
        """
        自动优化数据加载参数
        """
        import psutil
        
        # 获取系统信息
        cpu_count = psutil.cpu_count(logical=False)
        memory_gb = psutil.virtual_memory().total / 1e9
        
        # 调整工作进程数
        # 经验法则：使用物理核心数的50-75%
        self.num_workers = min(
            self.num_workers,
            max(1, int(cpu_count * 0.75))
        )
        
        # 调整预取因子
        # 根据内存大小决定预取批次数
        if memory_gb > 32:
            self.prefetch_factor = 4
        elif memory_gb > 16:
            self.prefetch_factor = 2
        else:
            self.prefetch_factor = 1
        
        print(f"DataLoader配置优化:")
        print(f"  - 工作进程数: {self.num_workers}")
        print(f"  - 预取因子: {self.prefetch_factor}")
        print(f"  - 批大小: {self.batch_size}")
    
    def create_loader(self, shuffle=True):
        """
        创建优化的DataLoader
        """
        return DataLoader(
            self.dataset,
            batch_size=self.batch_size,
            shuffle=shuffle,
            num_workers=self.num_workers,
            pin_memory=torch.cuda.is_available(),  # GPU可用时启用
            prefetch_factor=self.prefetch_factor,
            persistent_workers=True,  # 保持工作进程存活
            drop_last=True  # 丢弃最后不完整的批次
        )

def collate_fn_parallel(batch):
    """
    自定义的批处理函数，支持并行处理
    """
    # 并行处理每个样本的特征
    features = torch.stack([item['features'] for item in batch])
    labels = torch.stack([item['label'] for item in batch])
    
    # 批量特征工程（向量化操作）
    features = batch_feature_engineering(features)
    
    return {
        'features': features,
        'labels': labels
    }

def batch_feature_engineering(features):
    """
    批量特征工程，使用向量化操作
    """
    # 标准化
    mean = features.mean(dim=0, keepdim=True)
    std = features.std(dim=0, keepdim=True)
    features = (features - mean) / (std + 1e-8)
    
    # 特征交叉（向量化）
    # 假设前10个特征做交叉
    cross_features = features[:, :10].unsqueeze(2) * features[:, :10].unsqueeze(1)
    cross_features = cross_features.reshape(features.size(0), -1)
    
    # 拼接原始特征和交叉特征
    features = torch.cat([features, cross_features], dim=1)
    
    return features
```

### 3.2 数组特征的并行展开

```python
class ArrayFeatureExpander:
    def __init__(self, max_length=50, n_workers=4):
        self.max_length = max_length
        self.n_workers = n_workers
    
    def expand_array_features_parallel(self, data):
        """
        并行展开数组特征
        例如：用户历史行为序列、物品标签列表等
        """
        # 识别数组特征
        array_features = self.identify_array_features(data)
        
        if not array_features:
            return data
        
        # 并行处理每个数组特征
        with ProcessPoolExecutor(max_workers=self.n_workers) as executor:
            futures = []
            
            for feature_name in array_features:
                future = executor.submit(
                    self.expand_single_array_feature,
                    data[feature_name],
                    feature_name
                )
                futures.append((feature_name, future))
            
            # 收集结果
            expanded_data = data.copy()
            for feature_name, future in futures:
                expanded = future.result()
                # 删除原始数组特征
                del expanded_data[feature_name]
                # 添加展开的特征
                expanded_data.update(expanded)
        
        return expanded_data
    
    def expand_single_array_feature(self, array_column, feature_name):
        """
        展开单个数组特征
        """
        expanded = {}
        
        for idx, array in enumerate(array_column):
            if array is None:
                array = []
            
            # 截断或填充到固定长度
            if len(array) > self.max_length:
                array = array[:self.max_length]
            else:
                array = array + [0] * (self.max_length - len(array))
            
            # 创建展开的特征
            for i, value in enumerate(array):
                col_name = f"{feature_name}_{i}"
                if col_name not in expanded:
                    expanded[col_name] = []
                expanded[col_name].append(value)
        
        return expanded
    
    def identify_array_features(self, data):
        """
        识别数据中的数组特征
        """
        array_features = []
        
        for col_name, col_data in data.items():
            # 检查第一个非空值
            for value in col_data:
                if value is not None:
                    if isinstance(value, (list, np.ndarray)):
                        array_features.append(col_name)
                    break
        
        return array_features
```

## 4. 模型训练的并行化

### 4.1 GPU并行计算

```python
import torch
import torch.nn as nn
from torch.nn.parallel import DataParallel, DistributedDataParallel

class ParallelModelTrainer:
    def __init__(self, model, use_cuda=True, multi_gpu=False):
        self.model = model
        self.use_cuda = use_cuda and torch.cuda.is_available()
        self.multi_gpu = multi_gpu and torch.cuda.device_count() > 1
        
        self._setup_parallel_training()
    
    def _setup_parallel_training(self):
        """
        设置并行训练环境
        """
        if self.use_cuda:
            if self.multi_gpu:
                print(f"使用 {torch.cuda.device_count()} 个GPU进行并行训练")
                # 数据并行
                self.model = DataParallel(self.model)
            else:
                print("使用单GPU训练")
            
            self.model = self.model.cuda()
            
            # 启用cuDNN自动优化
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.enabled = True
        else:
            print("使用CPU训练")
    
    def train_parallel(self, dataloader, optimizer, criterion, epochs=10):
        """
        并行训练模型
        """
        for epoch in range(epochs):
            epoch_loss = 0
            batch_times = []
            
            for batch_idx, batch in enumerate(dataloader):
                start_time = time.time()
                
                # 数据移动到GPU（如果可用）
                if self.use_cuda:
                    batch['features'] = batch['features'].cuda(non_blocking=True)
                    batch['labels'] = batch['labels'].cuda(non_blocking=True)
                
                # 前向传播（自动并行）
                outputs = self.model(batch['features'])
                loss = criterion(outputs, batch['labels'])
                
                # 反向传播
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                # 统计
                epoch_loss += loss.item()
                batch_time = time.time() - start_time
                batch_times.append(batch_time)
                
                if batch_idx % 100 == 0:
                    avg_time = np.mean(batch_times[-100:])
                    samples_per_sec = dataloader.batch_size / avg_time
                    
                    print(f"Epoch {epoch}, Batch {batch_idx}: "
                          f"Loss={loss.item():.4f}, "
                          f"Speed={samples_per_sec:.0f} samples/sec")
            
            print(f"Epoch {epoch} completed: Avg Loss={epoch_loss/len(dataloader):.4f}")
```

### 4.2 分布式训练

```python
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP

class DistributedTrainer:
    def __init__(self, rank, world_size, model):
        """
        rank: 当前进程的编号
        world_size: 总进程数
        """
        self.rank = rank
        self.world_size = world_size
        self.model = model
        
        self._setup_distributed()
    
    def _setup_distributed(self):
        """
        初始化分布式训练环境
        """
        # 初始化进程组
        dist.init_process_group(
            backend='nccl' if torch.cuda.is_available() else 'gloo',
            init_method='env://',
            world_size=self.world_size,
            rank=self.rank
        )
        
        # 设置设备
        if torch.cuda.is_available():
            torch.cuda.set_device(self.rank)
            self.device = torch.device(f'cuda:{self.rank}')
        else:
            self.device = torch.device('cpu')
        
        # 模型包装
        self.model = self.model.to(self.device)
        self.model = DDP(self.model, device_ids=[self.rank] if torch.cuda.is_available() else None)
    
    def train_distributed(self, dataloader, optimizer, epochs):
        """
        分布式训练
        """
        for epoch in range(epochs):
            # 设置epoch，确保每个进程的数据顺序不同
            dataloader.sampler.set_epoch(epoch)
            
            for batch in dataloader:
                # 数据到设备
                features = batch['features'].to(self.device)
                labels = batch['labels'].to(self.device)
                
                # 训练步骤
                outputs = self.model(features)
                loss = self.compute_loss(outputs, labels)
                
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                # 同步所有进程
                dist.barrier()
            
            # 聚合所有进程的损失
            if self.rank == 0:
                self.log_epoch_stats(epoch)
    
    def cleanup(self):
        """
        清理分布式环境
        """
        dist.destroy_process_group()
```

## 5. 特征工程的并行化

### 5.1 并行特征提取

```python
from joblib import Parallel, delayed
import pandas as pd

class ParallelFeatureExtractor:
    def __init__(self, n_jobs=-1):
        """
        n_jobs: 并行任务数，-1表示使用所有CPU
        """
        self.n_jobs = n_jobs
    
    def extract_features_parallel(self, data):
        """
        并行提取多组特征
        """
        feature_groups = [
            ('user_features', self.extract_user_features),
            ('item_features', self.extract_item_features),
            ('interaction_features', self.extract_interaction_features),
            ('context_features', self.extract_context_features),
            ('cross_features', self.extract_cross_features)
        ]
        
        # 并行提取各组特征
        results = Parallel(n_jobs=self.n_jobs)(
            delayed(extractor)(data) for name, extractor in feature_groups
        )
        
        # 合并所有特征
        all_features = pd.DataFrame()
        for features in results:
            all_features = pd.concat([all_features, features], axis=1)
        
        return all_features
    
    def extract_user_features(self, data):
        """
        提取用户特征
        """
        features = pd.DataFrame()
        
        # 基础特征
        features['user_id'] = data['user_id']
        features['user_age'] = data['age']
        features['user_gender'] = data['gender'].map({'M': 1, 'F': 0})
        
        # 统计特征（向量化操作）
        features['user_total_clicks'] = data.groupby('user_id')['click'].transform('sum')
        features['user_total_purchases'] = data.groupby('user_id')['purchase'].transform('sum')
        features['user_avg_price'] = data.groupby('user_id')['price'].transform('mean')
        
        # 时间特征
        features['user_active_days'] = data.groupby('user_id')['date'].transform('nunique')
        
        return features
    
    def extract_item_features(self, data):
        """
        提取物品特征
        """
        features = pd.DataFrame()
        
        # 基础特征
        features['item_id'] = data['item_id']
        features['item_category'] = pd.Categorical(data['category']).codes
        features['item_price'] = data['price']
        features['item_brand'] = pd.Categorical(data['brand']).codes
        
        # 统计特征
        features['item_popularity'] = data.groupby('item_id')['user_id'].transform('nunique')
        features['item_click_rate'] = data.groupby('item_id')['click'].transform('mean')
        features['item_purchase_rate'] = data.groupby('item_id')['purchase'].transform('mean')
        
        return features
    
    def extract_interaction_features(self, data):
        """
        提取交互特征
        """
        features = pd.DataFrame()
        
        # 用户-物品交互
        features['user_item_clicks'] = data.groupby(['user_id', 'item_id'])['click'].transform('sum')
        features['user_item_views'] = data.groupby(['user_id', 'item_id'])['view'].transform('sum')
        
        # 用户-类别交互
        features['user_category_affinity'] = data.groupby(['user_id', 'category'])['purchase'].transform('mean')
        
        # 时间窗口统计
        features['clicks_last_7d'] = self.rolling_window_stats(data, 'click', 7)
        features['purchases_last_30d'] = self.rolling_window_stats(data, 'purchase', 30)
        
        return features
    
    def extract_context_features(self, data):
        """
        提取上下文特征
        """
        features = pd.DataFrame()
        
        # 时间特征
        data['datetime'] = pd.to_datetime(data['timestamp'])
        features['hour'] = data['datetime'].dt.hour
        features['day_of_week'] = data['datetime'].dt.dayofweek
        features['is_weekend'] = (features['day_of_week'] >= 5).astype(int)
        features['month'] = data['datetime'].dt.month
        
        # 设备特征
        features['device_type'] = pd.Categorical(data['device']).codes
        features['os_type'] = pd.Categorical(data['os']).codes
        
        # 会话特征
        features['session_length'] = data.groupby('session_id')['timestamp'].transform(lambda x: x.max() - x.min())
        features['session_page_views'] = data.groupby('session_id').size()
        
        return features
    
    def extract_cross_features(self, data):
        """
        提取交叉特征（最耗时，需要优化）
        """
        features = pd.DataFrame()
        
        # 使用向量化操作创建交叉特征
        # 年龄-类别交叉
        features['age_category_cross'] = data['age'].astype(str) + '_' + data['category'].astype(str)
        features['age_category_cross'] = pd.Categorical(features['age_category_cross']).codes
        
        # 时间-行为交叉
        features['hour_action_cross'] = data['hour'].astype(str) + '_' + data['action'].astype(str)
        features['hour_action_cross'] = pd.Categorical(features['hour_action_cross']).codes
        
        # 设备-类别交叉
        features['device_category_cross'] = data['device'].astype(str) + '_' + data['category'].astype(str)
        features['device_category_cross'] = pd.Categorical(features['device_category_cross']).codes
        
        return features
    
    def rolling_window_stats(self, data, column, window_days):
        """
        计算滑动窗口统计特征
        """
        # 使用向量化操作加速
        data = data.sort_values('timestamp')
        window = f'{window_days}D'
        
        return data.set_index('timestamp').groupby('user_id')[column].rolling(window).sum().values
```

## 6. 批量预测的并行化

```python
class ParallelPredictor:
    def __init__(self, model, batch_size=1024, n_workers=4):
        self.model = model
        self.batch_size = batch_size
        self.n_workers = n_workers
        
        # 设置模型为评估模式
        self.model.eval()
    
    def predict_parallel(self, data):
        """
        并行批量预测
        """
        # 创建数据加载器
        dataset = PredictionDataset(data)
        dataloader = DataLoader(
            dataset,
            batch_size=self.batch_size,
            num_workers=self.n_workers,
            pin_memory=torch.cuda.is_available()
        )
        
        predictions = []
        
        with torch.no_grad():
            for batch in tqdm(dataloader, desc="Predicting"):
                # 移动到GPU
                if torch.cuda.is_available():
                    batch = batch.cuda()
                
                # 批量预测
                batch_predictions = self.model(batch)
                
                # 移回CPU并转换为numpy
                batch_predictions = batch_predictions.cpu().numpy()
                predictions.extend(batch_predictions)
        
        return np.array(predictions)
    
    def predict_with_multiprocessing(self, data):
        """
        使用多进程进行预测（CPU密集型）
        """
        # 分割数据
        n_splits = self.n_workers
        data_splits = np.array_split(data, n_splits)
        
        # 多进程预测
        with ProcessPoolExecutor(max_workers=self.n_workers) as executor:
            futures = [
                executor.submit(self._predict_chunk, chunk)
                for chunk in data_splits
            ]
            
            results = []
            for future in tqdm(as_completed(futures), total=len(futures)):
                chunk_predictions = future.result()
                results.append(chunk_predictions)
        
        # 合并结果
        return np.concatenate(results)
    
    def _predict_chunk(self, data_chunk):
        """
        预测单个数据块
        """
        return self.model.predict(data_chunk)
```

## 7. 并行化性能监控

### 7.1 性能指标收集

```python
class ParallelPerformanceMonitor:
    def __init__(self):
        self.metrics = {
            'cpu_usage': [],
            'memory_usage': [],
            'gpu_usage': [],
            'throughput': [],
            'latency': []
        }
    
    def monitor_parallel_execution(self, func, *args, **kwargs):
        """
        监控并行执行的性能
        """
        import psutil
        import GPUtil
        
        # 开始监控
        start_time = time.time()
        initial_memory = psutil.Process().memory_info().rss
        
        # 启动监控线程
        monitoring = True
        def monitor_resources():
            while monitoring:
                # CPU使用率
                cpu_percent = psutil.cpu_percent(interval=1)
                self.metrics['cpu_usage'].append(cpu_percent)
                
                # 内存使用
                memory_percent = psutil.virtual_memory().percent
                self.metrics['memory_usage'].append(memory_percent)
                
                # GPU使用率（如果可用）
                try:
                    gpus = GPUtil.getGPUs()
                    if gpus:
                        gpu_usage = gpus[0].load * 100
                        self.metrics['gpu_usage'].append(gpu_usage)
                except:
                    pass
                
                time.sleep(1)
        
        monitor_thread = threading.Thread(target=monitor_resources)
        monitor_thread.start()
        
        # 执行函数
        result = func(*args, **kwargs)
        
        # 停止监控
        monitoring = False
        monitor_thread.join()
        
        # 计算性能指标
        elapsed_time = time.time() - start_time
        final_memory = psutil.Process().memory_info().rss
        memory_increase = (final_memory - initial_memory) / 1e6  # MB
        
        # 分析结果
        analysis = {
            'execution_time': elapsed_time,
            'memory_increase_mb': memory_increase,
            'avg_cpu_usage': np.mean(self.metrics['cpu_usage']),
            'max_cpu_usage': np.max(self.metrics['cpu_usage']),
            'avg_memory_usage': np.mean(self.metrics['memory_usage']),
            'max_memory_usage': np.max(self.metrics['memory_usage'])
        }
        
        if self.metrics['gpu_usage']:
            analysis['avg_gpu_usage'] = np.mean(self.metrics['gpu_usage'])
            analysis['max_gpu_usage'] = np.max(self.metrics['gpu_usage'])
        
        return result, analysis
    
    def compare_parallel_strategies(self, strategies):
        """
        比较不同的并行策略
        """
        results = {}
        
        for name, strategy_func in strategies.items():
            print(f"测试策略: {name}")
            _, analysis = self.monitor_parallel_execution(strategy_func)
            results[name] = analysis
        
        # 生成比较报告
        self.generate_comparison_report(results)
        
        return results
    
    def generate_comparison_report(self, results):
        """
        生成性能比较报告
        """
        print("\n" + "="*50)
        print("并行策略性能比较报告")
        print("="*50)
        
        for strategy, metrics in results.items():
            print(f"\n策略: {strategy}")
            print(f"  执行时间: {metrics['execution_time']:.2f} 秒")
            print(f"  内存增长: {metrics['memory_increase_mb']:.2f} MB")
            print(f"  平均CPU: {metrics['avg_cpu_usage']:.1f}%")
            print(f"  峰值CPU: {metrics['max_cpu_usage']:.1f}%")
            
            if 'avg_gpu_usage' in metrics:
                print(f"  平均GPU: {metrics['avg_gpu_usage']:.1f}%")
                print(f"  峰值GPU: {metrics['max_gpu_usage']:.1f}%")
```

## 8. 并行化最佳实践

### 8.1 选择合适的并行策略

```python
def choose_parallel_strategy(data_size, operation_type):
    """
    根据数据规模和操作类型选择最佳并行策略
    """
    strategies = {
        'small_cpu': 'threading',      # 小数据量，I/O密集型
        'medium_cpu': 'multiprocessing',  # 中等数据量，CPU密集型
        'large_cpu': 'distributed',    # 大数据量，CPU密集型
        'gpu_available': 'cuda',       # GPU可用
        'multi_gpu': 'data_parallel'   # 多GPU可用
    }
    
    # 判断数据规模
    if data_size < 1e6:  # 小于100万条
        size_category = 'small'
    elif data_size < 1e8:  # 小于1亿条
        size_category = 'medium'
    else:
        size_category = 'large'
    
    # 判断操作类型
    if operation_type in ['io', 'network', 'disk']:
        op_category = 'io'
    else:
        op_category = 'cpu'
    
    # 检查GPU
    if torch.cuda.is_available():
        if torch.cuda.device_count() > 1:
            return strategies['multi_gpu']
        else:
            return strategies['gpu_available']
    
    # 选择CPU策略
    key = f"{size_category}_{op_category}"
    return strategies.get(key, 'multiprocessing')
```

## 9. 总结

并行化在推荐系统中的应用覆盖了整个数据处理和模型训练流程：

1. **数据预处理**：使用多进程并行处理大规模数据
2. **特征工程**：并行提取和转换特征，使用向量化操作
3. **数据加载**：多进程DataLoader和智能预取
4. **模型训练**：GPU加速、数据并行、分布式训练
5. **批量预测**：并行推理和批处理优化

关键优化点：
- 根据任务特性选择合适的并行策略
- 合理配置并行参数（进程数、批大小等）
- 监控和分析并行性能
- 处理好进程间通信和同步
- 注意内存管理和资源平衡