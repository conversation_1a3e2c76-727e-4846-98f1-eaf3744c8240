# 模型版本使用指南

## 概述

项目中包含三个不同版本的模型实现，每个版本都有其特定的优势和使用场景：

## 模型版本对比

| 版本 | 文件名 | 特点 | 推荐场景 |
|------|--------|------|----------|
| **原始版本** | `models.py` | BatchNorm + 手动交叉层实现 | 生产环境，已验证稳定性 |
| **改进版本** | `models_o3_orig.py` | LayerNorm + F.linear优化 | 新项目，追求性能优化 |
| **可配置版本** | `models_o3.py` | 支持norm_type选择 | 实验对比，灵活配置 |

## 详细差异

### 1. 归一化层
- **原始版本**: `BatchNorm1d` - 依赖批次统计，大批次时性能好
- **改进版本**: `LayerNorm` - 与批次大小无关，小批次时更稳定
- **可配置版本**: 支持 `norm_type='layer'` 或 `norm_type='batch'`

### 2. 层顺序
- **原始版本**: `Linear → BatchNorm → ReLU → Dropout`
- **改进版本**: `Linear → ReLU → Dropout → LayerNorm`
- **优势**: 先激活再丢弃更合理，末尾归一化保持数值平衡

### 3. 交叉层实现
- **原始版本**: `torch.sum(x * w, dim=1, keepdim=True)` 手动计算
- **改进版本**: `F.linear(xl, w.unsqueeze(0))` 利用PyTorch优化内核

### 4. 权重初始化
- **原始版本**: `torch.randn() * 0.01` 简单初始化
- **改进版本**: `kaiming_uniform_` He初始化，适合ReLU激活

## 使用方法

### 在 train_pytorch_fixed.py 中选择版本

修改文件中的 `MODEL_VERSION` 变量：

```python
MODEL_VERSION = "models_o3"  # 可选: "models", "models_o3_orig", "models_o3"
```

### 命令行使用

```bash
# 使用默认配置（LayerNorm）
python train_pytorch_fixed.py --model_type dcnv2

# 指定使用BatchNorm（仅models_o3版本支持）
python train_pytorch_fixed.py --model_type dcnv2 --norm_type batch

# 指定使用LayerNorm
python train_pytorch_fixed.py --model_type dcnv2 --norm_type layer
```

### 性能对比实验

```bash
# 测试不同归一化方法的效果
python train_pytorch_fixed.py --model_type dcnv2 --norm_type layer --epochs 10
python train_pytorch_fixed.py --model_type dcnv2 --norm_type batch --epochs 10
```

## 兼容性测试

运行兼容性测试脚本：

```bash
python test_model_versions.py
```

这将测试所有版本和模型类型的兼容性。

## 推荐使用策略

### 1. 生产环境
- 使用 `models.py`（原始版本）
- 已经过验证，稳定可靠
- 适合大批次训练

### 2. 新项目开发
- 使用 `models_o3.py`（可配置版本）
- 默认使用 `norm_type='layer'`
- 享受所有技术改进

### 3. 性能对比实验
- 使用 `models_o3.py`
- 通过 `norm_type` 参数对比不同归一化方法
- 找到最适合数据的配置

### 4. 小批次训练
- 推荐使用 LayerNorm（`norm_type='layer'`）
- 在小批次情况下更稳定

### 5. 大批次训练
- 可以考虑 BatchNorm（`norm_type='batch'`）
- 在大批次时可能有轻微性能优势

## 注意事项

1. **向后兼容性**: `models_o3.py` 完全向后兼容，可以无缝替换
2. **配置文件**: 所有版本都使用相同的配置格式
3. **性能差异**: LayerNorm 通常比 BatchNorm 稍慢，但数值更稳定
4. **内存使用**: 不同版本的内存使用基本相同

## 故障排除

如果遇到问题：

1. 运行 `python test_model_versions.py` 检查兼容性
2. 检查 `MODEL_VERSION` 设置是否正确
3. 确认所需的模型文件存在
4. 查看日志中的模型版本信息
