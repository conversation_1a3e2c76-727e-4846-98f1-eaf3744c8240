{"start_time": "2025-08-05T19:37:23.169765", "end_time": "2025-08-05T19:37:58.394310", "total_tests": 11, "passed": 11, "failed": 0, "results": [{"name": "梯度稳定性测试", "command": "C:\\Users\\<USER>\\Downloads\\win_project\\recommendation\\recommendation_venv\\Scripts\\python.exe tests/test_gradient_debug.py", "success": true, "returncode": 0, "elapsed_time": 4.910528898239136, "stdout": "       0.006662        0.001369\noutput.bias                                               0.003003        0.003003\ncross_net.0.w                                             0.002787        0.002764\ncross_net.1.w                                             0.002544        0.002507\ncross_net.1.b                                             0.000646        0.000627\ncross_net.0.b                                             0.000546        0.000455\n\nGradient Anomalies:\n--------------------------------------------------------------------------------\n✅ No gradient explosion detected\n✅ No vanishing gradient detected\n\n\nTraining Stability Analysis:\n--------------------------------------------------------------------------------\nTotal Gradient Norm Statistics:\n  Mean: 0.5000\n  Std: 0.0000\n  Min: 0.5000\n  Max: 0.5000\n\n✅ Gradient variance is acceptable (CV=0.00)\n\n================================================================================\nReport saved to: gradient_report_dcnv2_sqrt_balanced.txt\n", "stderr": "sers\\matht\\Downloads\\win_project\\recommendation\\recommendation_venv\\Scripts\\python.exe src/train_loss_optimized.py --model_type dcnv2 --epochs 3 --pos_weight_strategy sqrt_balanced --no_amp --gradient_monitor\n2025-08-05 19:37:28,075 - INFO - 训练完成！\n2025-08-05 19:37:28,075 - INFO - ✅ 已生成: gradient_history_dcnv2_sqrt_balanced.png\n2025-08-05 19:37:28,075 - INFO - ✅ 已生成: gradient_report_dcnv2_sqrt_balanced.txt\n2025-08-05 19:37:28,075 - INFO - \n============================================================\n2025-08-05 19:37:28,075 - INFO - 梯度报告预览 (gradient_report_dcnv2_sqrt_balanced.txt):\n2025-08-05 19:37:28,075 - INFO - ============================================================\n2025-08-05 19:37:28,075 - INFO - ============================================================\n\n2025-08-05 19:37:28,075 - INFO - ✅ 所有测试通过！\n2025-08-05 19:37:28,075 - INFO - \n请查看生成的文件：\n2025-08-05 19:37:28,075 - INFO -   - gradient_history_*.png: 梯度历史图表\n2025-08-05 19:37:28,075 - INFO -   - gradient_report_*.txt: 梯度分析文本报告\n", "expected_success": true}, {"name": "数据预处理检查", "command": "C:\\Users\\<USER>\\Downloads\\win_project\\recommendation\\recommendation_venv\\Scripts\\python.exe -c from src.adaptive_init import robust_data_preprocessing; import numpy as np; X = np.random.randn(100, 10); X[0, 0] = 1e6; X_proc = robust_data_preprocessing(X); assert X_proc.max() <= 10 and X_proc.min() >= -10; print('Data preprocessing test passed')", "success": true, "returncode": 0, "elapsed_time": 2.331821918487549, "stdout": "Data preprocessing test passed\n", "stderr": null, "expected_success": true}, {"name": "模型初始化测试", "command": "C:\\Users\\<USER>\\Downloads\\win_project\\recommendation\\recommendation_venv\\Scripts\\python.exe -c import torch; from src.models_o3_cc import build_model; model = build_model('dcnv2', 160, {}); x = torch.randn(32, 160); y = model(x); assert y.std().item() < 100, f'Initial output std too large: {y.std().item()}'; print(f'Initialization test passed, output std={y.std().item():.4f}')", "success": true, "returncode": 0, "elapsed_time": 1.475428819656372, "stdout": "Initialization test passed, output std=0.7408\n", "stderr": null, "expected_success": true}, {"name": "训练脚本基础测试", "command": "C:\\Users\\<USER>\\Downloads\\win_project\\recommendation\\recommendation_venv\\Scripts\\python.exe src/train_loss_optimized.py --model_type dcnv2 --epochs 1 --no_amp", "success": true, "returncode": 0, "elapsed_time": 3.9165117740631104, "stdout": "并行处理系统配置:\n  平台: windows\n  实例类型: r5.4xlarge\n  CPU核心数: 24\n  存储类型: local\n  性能配置: Windows + local + r5.4xlarge (optimized for parallel processing)\n  最大Worker数: 14\n  内存限制: 115GB\n  Chunk大小: 50,000行\n  I/O Worker数: 8\n  启动方法: spawn\n  批次大小: 4096\n  本地环境: 使用本地文件系统\nTRAINING_RESULTS_START\n{\n  \"model_type\": \"dcnv2\",\n  \"pos_weight_strategy\": \"balanced\",\n  \"pos_weight_value\": 2.3333332538604736,\n  \"epochs\": 1,\n  \"learning_rate\": 0.0001,\n  \"batch_size\": 1024,\n  \"best_val_loss\": 4.605403900146484,\n  \"best_val_roc_auc\": 0.5416666666666667,\n  \"test_loss\": 8.417722702026367,\n  \"test_roc_auc\": 0.5,\n  \"test_pr_auc\": 0.6333333333333333,\n  \"model_params\": 9099,\n  \"device\": \"cpu\"\n}\nTRAINING_RESULTS_END\n", "stderr": "-08-05 19:37:35,233 - INFO - 测试集 数据分布:\n2025-08-05 19:37:35,233 - INFO -   总样本数: 10\n2025-08-05 19:37:35,233 - INFO -   正样本数: 5.0 (0.5000)\n2025-08-05 19:37:35,233 - INFO -   负样本数: 5.0 (0.5000)\n2025-08-05 19:37:35,233 - INFO -   不平衡比例: 1.00:1\n2025-08-05 19:37:35,233 - INFO - 使用单进程数据加载（CPU模式）\n2025-08-05 19:37:35,235 - INFO - 最终测试集 详细分析:\n2025-08-05 19:37:35,236 - INFO -   平均Loss: 8.417723\n2025-08-05 19:37:35,236 - INFO -   ROC AUC: 0.500000\n2025-08-05 19:37:35,236 - INFO -   PR AUC: 0.633333\n2025-08-05 19:37:35,236 - INFO - ============================================================\n2025-08-05 19:37:35,236 - INFO - 🎯 训练完成 - 最终结果:\n2025-08-05 19:37:35,236 - INFO -   最佳验证Loss: 4.605404\n2025-08-05 19:37:35,236 - INFO -   最佳验证ROC AUC: 0.541667\n2025-08-05 19:37:35,236 - INFO -   测试Loss: 8.417723\n2025-08-05 19:37:35,236 - INFO -   测试ROC AUC: 0.500000\n2025-08-05 19:37:35,236 - INFO -   测试PR AUC: 0.633333\n2025-08-05 19:37:35,236 - INFO - ============================================================\n", "expected_success": true}, {"name": "自适应初始化测试", "command": "C:\\Users\\<USER>\\Downloads\\win_project\\recommendation\\recommendation_venv\\Scripts\\python.exe src/train_loss_optimized.py --model_type dcnv2 --epochs 1 --no_amp --pos_weight_strategy sqrt_balanced", "success": true, "returncode": 0, "elapsed_time": 3.937994956970215, "stdout": "并行处理系统配置:\n  平台: windows\n  实例类型: r5.4xlarge\n  CPU核心数: 24\n  存储类型: local\n  性能配置: Windows + local + r5.4xlarge (optimized for parallel processing)\n  最大Worker数: 14\n  内存限制: 115GB\n  Chunk大小: 50,000行\n  I/O Worker数: 8\n  启动方法: spawn\n  批次大小: 4096\n  本地环境: 使用本地文件系统\nTRAINING_RESULTS_START\n{\n  \"model_type\": \"dcnv2\",\n  \"pos_weight_strategy\": \"sqrt_balanced\",\n  \"pos_weight_value\": 1.5275251865386963,\n  \"epochs\": 1,\n  \"learning_rate\": 0.0001,\n  \"batch_size\": 1024,\n  \"best_val_loss\": 3.020310163497925,\n  \"best_val_roc_auc\": 0.5416666666666667,\n  \"test_loss\": 5.511231422424316,\n  \"test_roc_auc\": 0.5,\n  \"test_pr_auc\": 0.6333333333333333,\n  \"model_params\": 9099,\n  \"device\": \"cpu\"\n}\nTRAINING_RESULTS_END\n", "stderr": "-08-05 19:37:39,126 - INFO - 测试集 数据分布:\n2025-08-05 19:37:39,126 - INFO -   总样本数: 10\n2025-08-05 19:37:39,126 - INFO -   正样本数: 5.0 (0.5000)\n2025-08-05 19:37:39,126 - INFO -   负样本数: 5.0 (0.5000)\n2025-08-05 19:37:39,126 - INFO -   不平衡比例: 1.00:1\n2025-08-05 19:37:39,126 - INFO - 使用单进程数据加载（CPU模式）\n2025-08-05 19:37:39,127 - INFO - 最终测试集 详细分析:\n2025-08-05 19:37:39,128 - INFO -   平均Loss: 5.511231\n2025-08-05 19:37:39,128 - INFO -   ROC AUC: 0.500000\n2025-08-05 19:37:39,128 - INFO -   PR AUC: 0.633333\n2025-08-05 19:37:39,128 - INFO - ============================================================\n2025-08-05 19:37:39,128 - INFO - 🎯 训练完成 - 最终结果:\n2025-08-05 19:37:39,128 - INFO -   最佳验证Loss: 3.020310\n2025-08-05 19:37:39,128 - INFO -   最佳验证ROC AUC: 0.541667\n2025-08-05 19:37:39,128 - INFO -   测试Loss: 5.511231\n2025-08-05 19:37:39,128 - INFO -   测试ROC AUC: 0.500000\n2025-08-05 19:37:39,128 - INFO -   测试PR AUC: 0.633333\n2025-08-05 19:37:39,128 - INFO - ============================================================\n", "expected_success": true}, {"name": "梯度监控功能测试", "command": "C:\\Users\\<USER>\\Downloads\\win_project\\recommendation\\recommendation_venv\\Scripts\\python.exe src/train_loss_optimized.py --model_type dcnv2 --epochs 1 --no_amp --gradient_monitor", "success": true, "returncode": 0, "elapsed_time": 4.61789608001709, "stdout": "并行处理系统配置:\n  平台: windows\n  实例类型: r5.4xlarge\n  CPU核心数: 24\n  存储类型: local\n  性能配置: Windows + local + r5.4xlarge (optimized for parallel processing)\n  最大Worker数: 14\n  内存限制: 115GB\n  Chunk大小: 50,000行\n  I/O Worker数: 8\n  启动方法: spawn\n  批次大小: 4096\n  本地环境: 使用本地文件系统\nTRAINING_RESULTS_START\n{\n  \"model_type\": \"dcnv2\",\n  \"pos_weight_strategy\": \"balanced\",\n  \"pos_weight_value\": 2.3333332538604736,\n  \"epochs\": 1,\n  \"learning_rate\": 0.0001,\n  \"batch_size\": 1024,\n  \"best_val_loss\": 4.6053361892700195,\n  \"best_val_roc_auc\": 0.5416666666666667,\n  \"test_loss\": 8.417657852172852,\n  \"test_roc_auc\": 0.5,\n  \"test_pr_auc\": 0.6333333333333333,\n  \"model_params\": 9099,\n  \"device\": \"cpu\"\n}\nTRAINING_RESULTS_END\n", "stderr": "-08-05 19:37:43,058 - INFO -   不平衡比例: 1.00:1\n2025-08-05 19:37:43,058 - INFO - 使用单进程数据加载（CPU模式）\n2025-08-05 19:37:43,059 - INFO - 最终测试集 详细分析:\n2025-08-05 19:37:43,059 - INFO -   平均Loss: 8.417658\n2025-08-05 19:37:43,059 - INFO -   ROC AUC: 0.500000\n2025-08-05 19:37:43,059 - INFO -   PR AUC: 0.633333\n2025-08-05 19:37:43,059 - INFO - ============================================================\n2025-08-05 19:37:43,059 - INFO - 🎯 训练完成 - 最终结果:\n2025-08-05 19:37:43,059 - INFO -   最佳验证Loss: 4.605336\n2025-08-05 19:37:43,059 - INFO -   最佳验证ROC AUC: 0.541667\n2025-08-05 19:37:43,059 - INFO -   测试Loss: 8.417658\n2025-08-05 19:37:43,059 - INFO -   测试ROC AUC: 0.500000\n2025-08-05 19:37:43,060 - INFO -   测试PR AUC: 0.633333\n2025-08-05 19:37:43,060 - INFO - ============================================================\n2025-08-05 19:37:43,754 - INFO - ✅ Gradient history plot saved to: gradient_history_dcnv2_balanced.png\n2025-08-05 19:37:43,756 - INFO - ✅ Text report saved to: gradient_report_dcnv2_balanced.txt\n", "expected_success": true}, {"name": "损失优化策略测试", "command": "C:\\Users\\<USER>\\Downloads\\win_project\\recommendation\\recommendation_venv\\Scripts\\python.exe tests/test_loss_optimization.py", "success": true, "returncode": 0, "elapsed_time": 10.658434867858887, "stdout": "module named 'torch'\n\n================================================================================\n📋 测试结果汇总\n================================================================================\n✅ 成功测试: 0/5\n❌ 失败测试: 5/5\n\n❌ 失败测试:\n--------------------------------------------------\n- dcnv2_balanced: unknown error\n  日志: test_logs/loss_test_dcnv2_balanced_20250805_193744.log\n- dcnv2_sqrt_balanced: unknown error\n  日志: test_logs/loss_test_dcnv2_sqrt_balanced_20250805_193746.log\n- dcnv2_log_balanced: unknown error\n  日志: test_logs/loss_test_dcnv2_log_balanced_20250805_193748.log\n- mlp_balanced: unknown error\n  日志: test_logs/loss_test_mlp_balanced_20250805_193750.log\n- mlp_sqrt_balanced: unknown error\n  日志: test_logs/loss_test_mlp_sqrt_balanced_20250805_193752.log\n\n📄 详细报告已保存: test_logs/loss_optimization_report_20250805_193755.json\n\n================================================================================\n🎉 测试完成\n================================================================================\n", "stderr": null, "expected_success": true}, {"name": "模块导入检查", "command": "C:\\Users\\<USER>\\Downloads\\win_project\\recommendation\\recommendation_venv\\Scripts\\python.exe -c from src.gradient_monitor import GradientMonitor; from src.adaptive_init import AdaptiveInitializer; from src.models_o3_cc import build_model; print('All modules imported successfully')", "success": true, "returncode": 0, "elapsed_time": 1.8689913749694824, "stdout": "All modules imported successfully\n", "stderr": null, "expected_success": true}, {"name": "配置文件检查", "command": "C:\\Users\\<USER>\\Downloads\\win_project\\recommendation\\recommendation_venv\\Scripts\\python.exe -c from src.config import PARALLEL_EXTENDED_CONFIG, MODEL_SPECIFIC_CONFIG; assert 'processed_data_dir' in PARALLEL_EXTENDED_CONFIG; assert 'dcnv2' in MODEL_SPECIFIC_CONFIG; print('Config file check passed')", "success": true, "returncode": 0, "elapsed_time": 1.4995973110198975, "stdout": "Config file check passed\n", "stderr": null, "expected_success": true}, {"name": "元数据基线守护", "command": "check_metadata_baseline()", "success": true, "elapsed_time": 0.0010006427764892578, "expected_success": true, "details": {"sha_match": true, "total_match": true, "groups_match": true, "current_sha": "ea54ac5b5017deac9c754feb89b7ffceac074e7a3e2e942d7fccd3b7a623bddc", "expected_sha": "ea54ac5b5017deac9c754feb89b7ffceac074e7a3e2e942d7fccd3b7a623bddc"}}, {"name": "S3工具直连守卫", "command": "check_no_direct_s3_utils()", "success": true, "elapsed_time": 0.0029990673065185547, "expected_success": true, "details": {"files_scanned": 33, "violations": []}}]}