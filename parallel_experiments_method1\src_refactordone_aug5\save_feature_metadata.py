# save_feature_metadata.py
"""
保存特征元数据，记录NPY文件中每个特征对应的原始列名
"""

import os
import json
import numpy as np
import pandas as pd
from config import PROCESSED_DATA_DIR, LABEL_COLUMN_NAME, EXCLUDE_COLUMNS
from preprocess import IntelligentPreprocessor, load_analysis_results
from common import io as common_io
from common.feature_registry import build_feature_spec, build_metadata

def extract_feature_metadata():
    """
    从原始parquet文件中提取特征元数据
    记录每个特征位置对应的原始列名和组信息
    """
    
    # 加载分析结果
    analysis_results = load_analysis_results()
    if not analysis_results:
        # 如果没有分析结果，则从样本文件中提取
        print("Analysis results not found, extracting column info from sample file...")
        
        # 尝试读取一个样本文件
        sample_file = None
        for dataset in ['train', 'validation', 'test']:
            data_dir = os.path.join('local_test_data', 'small', dataset)
            if os.path.exists(data_dir):
                files = common_io.list_parquet_files(data_dir)
                if files:
                    sample_file = files[0]
                    break
        
        if not sample_file:
            raise ValueError("Cannot find sample parquet file to extract column info")
        
        # 读取样本文件
        df = common_io.read_parquet(sample_file)
        all_columns = list(df.columns)
        
        # 模拟分析结果结构
        analysis_results = {
            'summary': {
                'detected_label_column': LABEL_COLUMN_NAME,
                'array_columns': [],
                'numeric_columns': [],
                'categorical_columns': []
            }
        }
        
        # 简单分类列类型
        for col in all_columns:
            if col == LABEL_COLUMN_NAME or col in EXCLUDE_COLUMNS:
                continue
                
            sample_value = df[col].iloc[0]
            if isinstance(sample_value, (list, np.ndarray)):
                analysis_results['summary']['array_columns'].append(col)
            elif pd.api.types.is_numeric_dtype(df[col]):
                analysis_results['summary']['numeric_columns'].append(col)
            else:
                analysis_results['summary']['categorical_columns'].append(col)
    
    # 提取特征列顺序和组信息
    preprocessor = IntelligentPreprocessor(analysis_results)
    
    # Build feature spec using registry
    # Note: Include all columns except label column to match baseline behavior
    # Do NOT filter out excluded columns here - they are included in the baseline
    spec = build_feature_spec(
        numeric_columns=[col for col in preprocessor.numeric_columns 
                       if col != preprocessor.label_column],
        categorical_columns=[col for col in preprocessor.categorical_columns 
                           if col != preprocessor.label_column],
        array_columns=preprocessor.array_columns,
        array_dimensions=preprocessor.array_dimensions,
        label_column=preprocessor.label_column
    )
    
    # Build metadata using registry
    feature_metadata = build_metadata(spec)
    
    # Add extra fields for compatibility
    feature_metadata['label_column'] = LABEL_COLUMN_NAME
    feature_metadata['excluded_columns'] = EXCLUDE_COLUMNS
    
    # 保存元数据
    metadata_file = os.path.join(PROCESSED_DATA_DIR, 'feature_metadata_expanded.json')
    common_io.ensure_dir(PROCESSED_DATA_DIR)
    
    with open(metadata_file, 'w', encoding='utf-8') as f:
        json.dump(feature_metadata, f, indent=2, ensure_ascii=False)
    
    print(f"Feature metadata saved to: {metadata_file}")
    print(f"Total features: {feature_metadata['total_features']}")
    print(f"Feature groups: {list(feature_metadata['groups'].keys())}")
    
    for group, indices in feature_metadata['groups'].items():
        print(f"  {group}: {len(indices)} features")
    
    return feature_metadata

# extract_prefix function removed - now using feature_registry.extract_prefix

if __name__ == "__main__":
    extract_feature_metadata()