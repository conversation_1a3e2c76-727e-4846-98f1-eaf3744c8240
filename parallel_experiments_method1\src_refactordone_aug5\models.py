# models.py
"""
模型定义文件
包含MLP、DCNv2、DCNv1和DLRM模型的实现
"""

import math
import torch
import torch.nn as nn


# ===================== MLP (从train.py迁移) =====================
class MLP(nn.Module):
    """
    多层感知机模型
    """
    def __init__(self, input_dim, hidden_dims=[256, 128], dropout_p=0.3):
        super().__init__()
        layers = []
        current_dim = input_dim

        for hidden_dim in hidden_dims:
            layers.append(nn.Linear(current_dim, hidden_dim))
            layers.append(nn.BatchNorm1d(hidden_dim))
            layers.append(nn.ReLU())
            layers.append(nn.Dropout(dropout_p))
            current_dim = hidden_dim

        layers.append(nn.Linear(current_dim, 1))
        self.net = nn.Sequential(*layers)

    def forward(self, x):
        return self.net(x)


# ===================== DCNv2 (从train.py迁移) =====================
class CrossLayer(nn.Module):
    """
    DCN V2的核心交叉层 - 修复版本
    """
    def __init__(self, input_dim):
        super().__init__()
        # 使用更轻量的实现：向量w而不是矩阵W
        self.w = nn.Parameter(torch.randn(input_dim) * 0.01)
        self.b = nn.Parameter(torch.zeros(input_dim))

    def forward(self, x0, x):
        # x0: 原始输入, x: 上一层的输出
        # x_0 * (w^T * x) + b + x
        # 这里w是向量，w^T * x得到标量，然后与x0逐元素相乘
        x_w = torch.sum(x * self.w, dim=1, keepdim=True)  # (batch_size, 1)
        cross_term = x0 * x_w  # 广播：(batch_size, input_dim) * (batch_size, 1)
        return cross_term + self.b + x


class DCNv2(nn.Module):
    """
    Deep & Cross Network V2模型
    """
    def __init__(self, input_dim, num_cross_layers=3, deep_hidden_dims=[256, 128]):
        super().__init__()

        # Deep部分 - 创建不包含最后输出层的MLP
        deep_layers = []
        current_dim = input_dim
        for hidden_dim in deep_hidden_dims:
            deep_layers.append(nn.Linear(current_dim, hidden_dim))
            deep_layers.append(nn.BatchNorm1d(hidden_dim))
            deep_layers.append(nn.ReLU())
            deep_layers.append(nn.Dropout(0.3))
            current_dim = hidden_dim
        self.deep_net = nn.Sequential(*deep_layers)

        # Cross部分
        self.cross_net = nn.ModuleList([CrossLayer(input_dim) for _ in range(num_cross_layers)])

        # 最后的拼接和输出层
        combined_dim = input_dim + deep_hidden_dims[-1]
        self.output_layer = nn.Linear(combined_dim, 1)

    def forward(self, x):
        # x是原始的、扁平化的输入特征向量
        x0 = x

        # Cross部分前向传播
        cross_out = x
        for layer in self.cross_net:
            cross_out = layer(x0, cross_out)

        # Deep部分前向传播
        deep_out = self.deep_net(x)

        # 拼接Deep和Cross的输出
        combined_out = torch.cat([cross_out, deep_out], dim=1)

        # 通过最后的输出层
        output = self.output_layer(combined_out)
        return output


# ===================== DCN‑V1 =====================
class CrossLayerV1(nn.Module):
    """原版 DCN (2019) 交叉层：x_{l+1} = x_0 * (w^T x_l) + b + x_l"""
    def __init__(self, d):
        super().__init__()
        self.w = nn.Parameter(torch.empty(d))
        self.b = nn.Parameter(torch.zeros(d))
        nn.init.xavier_uniform_(self.w.unsqueeze(0))

    def forward(self, x0, xl):
        # (B,d) · (d,) -> (B,1)
        dot = torch.sum(xl * self.w, dim=1, keepdim=True)  # (B,1)
        return x0 * dot + self.b + xl


class DCNV1(nn.Module):
    def __init__(self, input_dim: int, cross_layers: int = 3,
                 deep_dims=(512, 256, 128)):
        super().__init__()
        self.cross = nn.ModuleList([CrossLayerV1(input_dim)
                                     for _ in range(cross_layers)])
        deep = []
        prev = input_dim
        for h in deep_dims:
            deep += [nn.Linear(prev, h), nn.ReLU()]
            prev = h
        self.deep = nn.Sequential(*deep)
        self.out  = nn.Linear(input_dim + deep_dims[-1], 1)

    def forward(self, x):
        x0, xl = x, x
        for layer in self.cross:
            xl = layer(x0, xl)
        deep_out = self.deep(x)
        concat   = torch.cat([xl, deep_out], dim=1)
        return self.out(concat)


# ===================== DLRM (简化稠密版) =====================
class DLRM(nn.Module):
    """简化版 DLRM：假设所有特征已是稠密 float32。
    如果需要稀疏 Embedding，请在 datasets 侧先做 ID→向量映射。"""
    def __init__(self, input_dim: int, bot_dims=(512, 256),
                 top_dims=(256, 128, 1)):
        super().__init__()
        # bottom MLP (dense features)
        bot = []
        prev = input_dim
        for h in bot_dims:
            bot += [nn.Linear(prev, h), nn.ReLU()]
            prev = h
        self.bottom = nn.Sequential(*bot)
        # DLRM dot‑interaction: bottom_out 与自身做互相关 (上三角除外)
        self.inter_out_dim = int((bot_dims[-1] * (bot_dims[-1] - 1)) / 2)
        # top MLP takes concat([bottom_out, interactions])
        top = []
        prev = bot_dims[-1] + self.inter_out_dim
        for h in top_dims:
            top += [nn.Linear(prev, h)]
            if h != 1:
                top += [nn.ReLU()]
            prev = h
        self.top = nn.Sequential(*top)

    def forward(self, x):  # x: (B,d)
        z = self.bottom(x)            # (B,h)

        # 添加数值稳定性：归一化bottom输出
        z = torch.nn.functional.normalize(z, p=2, dim=1)

        # dot‑interaction (batch wise upper‑triangular)
        batch_size, h = z.size()
        # (B,h,1) * (B,1,h) -> (B,h,h)
        inter = torch.bmm(z.unsqueeze(2), z.unsqueeze(1))
        # 取上三角(不含对角)展平
        iu = torch.triu_indices(h, h, offset=1)
        inter_flat = inter[:, iu[0], iu[1]]   # (B, self.inter_out_dim)

        # 添加数值稳定性：限制交互特征的范围
        inter_flat = torch.clamp(inter_flat, min=-10.0, max=10.0)

        concat = torch.cat([z, inter_flat], dim=1)
        out = self.top(concat)
        return out


# =========== 工厂方法，供 train.py 调用 ===========
def build_model(model_type: str, input_dim: int, cfg: dict):
    """
    根据模型类型和配置构建模型

    Args:
        model_type: 模型类型 ('mlp', 'dcnv2', 'dcnv1', 'dlrm')
        input_dim: 输入特征维度
        cfg: 模型配置字典

    Returns:
        构建好的模型实例
    """
    if model_type == "mlp":
        # MLP参数映射
        hidden_dims = cfg.get('hidden_dims', [256, 128])
        dropout_p = cfg.get('dropout_rate', 0.3)
        return MLP(input_dim, hidden_dims=hidden_dims, dropout_p=dropout_p)

    elif model_type == "dcnv2":
        # DCNv2参数映射
        num_cross_layers = cfg.get('cross_layers', 3)
        deep_hidden_dims = cfg.get('deep_layers', [256, 128])
        return DCNv2(input_dim, num_cross_layers=num_cross_layers, deep_hidden_dims=deep_hidden_dims)

    elif model_type == "dcnv1":
        # DCNv1参数映射
        cross_layers = cfg.get('cross_layers', 3)
        deep_dims = cfg.get('deep_dims', (512, 256, 128))
        return DCNV1(input_dim, cross_layers=cross_layers, deep_dims=deep_dims)

    elif model_type == "dlrm":
        # DLRM参数映射
        bot_dims = cfg.get('bot_dims', (512, 256))
        top_dims = cfg.get('top_dims', (256, 128, 1))
        return DLRM(input_dim, bot_dims=bot_dims, top_dims=top_dims)

    else:
        raise ValueError(f"Unknown model_type {model_type}. Supported types: mlp, dcnv2, dcnv1, dlrm")
