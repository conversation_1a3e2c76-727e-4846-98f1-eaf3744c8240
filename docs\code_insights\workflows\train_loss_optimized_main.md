# 工作流：损失优化训练 train_loss_optimized_main

## 工作流概述

专门针对类别不平衡问题优化的训练工作流，支持多种损失平衡策略和特征选择机制。

## 调用图

```mermaid
flowchart TD
    Start[命令行输入] --> Parse[parse_args<br/>解析参数]
    Parse --> Load[load_and_normalize_data<br/>加载数据]
    
    Load --> LoadTrain[加载训练数据<br/>features.npy, labels.npy]
    Load --> LoadVal[加载验证数据<br/>features.npy, labels.npy]
    Load --> Normalize[StandardScaler<br/>标准化]
    
    LoadTrain --> FM[FeatureManager<br/>特征管理]
    LoadVal --> FM
    FM --> GetIndices[get_feature_indices<br/>获取特征索引]
    
    GetIndices --> FilterFeatures[过滤特征<br/>X[:, indices]]
    
    FilterFeatures --> BuildModel[build_model<br/>构建模型]
    BuildModel --> ModelSelect{模型类型?}
    ModelSelect -->|mlp| MLP[创建MLP]
    ModelSelect -->|dcnv2| DCN2[创建DCNv2]
    ModelSelect -->|dcnv1| DCN1[创建DCNv1]
    ModelSelect -->|dlrm| DLRM[创建DLRM]
    
    MLP --> AdaptiveInit[AdaptiveInitializer<br/>自适应初始化]
    DCN2 --> AdaptiveInit
    DCN1 --> AdaptiveInit
    DLRM --> AdaptiveInit
    
    AdaptiveInit --> CalcPosWeight[calculate_pos_weight<br/>计算正样本权重]
    CalcPosWeight --> Strategy{权重策略?}
    Strategy -->|balanced| Balanced[neg/pos]
    Strategy -->|sqrt| Sqrt[sqrt(neg/pos)]
    Strategy -->|log| Log[log(1+neg/pos)]
    
    Balanced --> CreateLoader[创建DataLoader]
    Sqrt --> CreateLoader
    Log --> CreateLoader
    
    CreateLoader --> TrainLoop[training_loop<br/>训练循环]
    
    TrainLoop --> Epoch{Epoch循环}
    Epoch --> TrainBatch[处理训练批次]
    TrainBatch --> Forward[前向传播]
    Forward --> CalcLoss[计算损失<br/>BCEWithLogitsLoss]
    CalcLoss --> Backward[反向传播]
    Backward --> GradMonitor[GradientMonitor<br/>梯度监控]
    GradMonitor --> GradClip[梯度裁剪]
    GradClip --> UpdateParams[更新参数]
    UpdateParams --> ValEval[验证评估]
    
    ValEval --> CalcMetrics[计算指标<br/>AUC, Loss]
    CalcMetrics --> LRSchedule[学习率调度<br/>ReduceLROnPlateau]
    LRSchedule --> MLflow[MLflow记录]
    MLflow --> CheckStop{早停检查?}
    CheckStop -->|继续| Epoch
    CheckStop -->|停止| SaveModel[保存模型]
    
    SaveModel --> SaveCheckpoint[torch.save<br/>保存检查点]
    SaveCheckpoint --> LogArtifacts[MLflow<br/>记录artifacts]
    LogArtifacts --> End[训练完成]
```

## 逐函数IO说明

### main() - 主入口函数
**文件**: `train_loss_optimized.py:450-520`

**输入**:
- 命令行参数:
  - `--model_type`: 模型类型 (mlp/dcnv1/dcnv2/dlrm)
  - `--epochs`: 训练轮数 (默认10)
  - `--batch_size`: 批大小 (默认1024)
  - `--learning_rate`: 学习率 (默认0.001)
  - `--pos_weight_strategy`: 正样本权重策略
  - `--include_groups`: 包含的特征组
  - `--exclude_groups`: 排除的特征组
  - `--use_adaptive_init`: 是否使用自适应初始化
  - `--monitor_gradients`: 是否监控梯度

**处理**:
1. 解析命令行参数
2. 加载和预处理数据
3. 特征选择
4. 模型构建和初始化
5. 训练和评估
6. 保存模型和记录实验

**输出**:
- 训练好的模型文件
- MLflow实验记录
- 训练历史和指标

### load_and_normalize_data() - 数据加载和标准化
**文件**: `train_loss_optimized.py:85-130`

**输入**:
- 数据文件路径（从配置读取）
- `processed_data/train/features.npy`
- `processed_data/train/labels.npy`
- `processed_data/validation/features.npy`
- `processed_data/validation/labels.npy`

**处理**:
1. 使用numpy加载NPY文件
2. 创建StandardScaler实例
3. 在训练集上拟合scaler
4. 转换训练集和验证集
5. 处理NaN和Inf值

**输出**:
- `X_train`: 标准化的训练特征 (n_train, 177)
- `y_train`: 训练标签 (n_train,)
- `X_val`: 标准化的验证特征 (n_val, 177)
- `y_val`: 验证标签 (n_val,)
- `scaler`: 拟合好的StandardScaler对象

### train_model_optimized() - 优化训练函数
**文件**: `train_loss_optimized.py:200-380`

**输入**:
- `model`: PyTorch模型实例
- `X_train`: 训练特征数组
- `y_train`: 训练标签数组
- `X_val`: 验证特征数组
- `y_val`: 验证标签数组
- `epochs`: 训练轮数
- `batch_size`: 批大小
- `learning_rate`: 学习率
- `pos_weight_strategy`: 权重策略
- `device`: 计算设备 (cuda/cpu)
- `patience`: 早停耐心值

**处理**:
1. 计算正样本权重
2. 创建数据集和数据加载器
3. 设置优化器和损失函数
4. 训练循环:
   - 前向传播
   - 计算损失
   - 反向传播
   - 梯度裁剪
   - 参数更新
5. 验证评估
6. 学习率调整
7. 早停检查

**输出**:
- `best_model_state`: 最佳模型状态字典
- `history`: 训练历史（losses, metrics）
- `best_val_auc`: 最佳验证AUC

### calculate_pos_weight() - 计算正样本权重
**文件**: `train_loss_optimized.py:132-165`

**输入**:
- `y_train`: 训练标签数组
- `strategy`: 权重策略字符串
  - 'balanced': 完全平衡
  - 'sqrt_balanced': 平方根平衡
  - 'log_balanced': 对数平衡
  - 'custom': 自定义值

**处理**:
1. 统计正负样本数量
2. 根据策略计算权重:
   - balanced: `neg_count / pos_count`
   - sqrt_balanced: `sqrt(neg_count / pos_count)`
   - log_balanced: `log(1 + neg_count / pos_count)`
3. 转换为PyTorch张量

**输出**:
- `pos_weight`: torch.FloatTensor，正样本权重

### build_model() - 构建模型
**文件**: `common/models.py` (通过import)

**输入**:
- `model_type`: 模型类型字符串
- `input_size`: 输入特征维度
- `hidden_sizes`: 隐藏层大小列表
- `dropout_rate`: Dropout率

**处理**:
1. 根据model_type选择模型类
2. 实例化模型
3. 应用权重初始化

**输出**:
- 模型实例（MLP/DCNv1/DCNv2/DLRM）

### AdaptiveInitializer.initialize() - 自适应初始化
**文件**: `adaptive_init.py:50-95`

**输入**:
- `model`: 待初始化的模型
- `X_sample`: 数据样本（用于分析）

**处理**:
1. 分析输入数据分布
2. 计算数据标准差
3. 根据层类型选择初始化方法:
   - Linear层: Xavier或He初始化
   - Embedding层: 正态分布初始化
4. 调整初始化尺度

**输出**:
- 初始化后的模型（就地修改）

### GradientMonitor.get_gradient_stats() - 梯度统计
**文件**: `gradient_monitor.py:75-120`

**输入**:
- `model`: 正在训练的模型

**处理**:
1. 遍历模型参数
2. 计算梯度范数
3. 检测梯度消失/爆炸
4. 统计零梯度参数

**输出**:
- 梯度统计字典:
  - `mean_norm`: 平均梯度范数
  - `max_norm`: 最大梯度范数
  - `min_norm`: 最小梯度范数
  - `num_zeros`: 零梯度参数数
  - `num_exploding`: 爆炸梯度数

### evaluate_model() - 模型评估
**文件**: `train_loss_optimized.py:382-420`

**输入**:
- `model`: 训练好的模型
- `X_val`: 验证特征
- `y_val`: 验证标签
- `criterion`: 损失函数
- `device`: 计算设备

**处理**:
1. 设置模型为评估模式
2. 禁用梯度计算
3. 批量预测
4. 计算损失
5. 计算AUC
6. 计算精确率和召回率

**输出**:
- `val_loss`: 验证损失
- `val_auc`: 验证AUC
- `val_precision`: 精确率
- `val_recall`: 召回率

## 特征管理集成

### FeatureManager.get_feature_indices()
**文件**: `feature_manager.py:45-85`

**输入**:
- `include_groups`: 要包含的特征组列表
- `exclude_groups`: 要排除的特征组列表

**处理**:
1. 加载特征组定义
2. 根据包含/排除规则筛选
3. 生成特征索引列表
4. 验证索引有效性

**输出**:
- 特征索引列表（整数列表）

## MLflow集成

### MLflow实验追踪
```python
with mlflow.start_run():
    # 记录参数
    mlflow.log_params({
        'model_type': args.model_type,
        'input_size': input_size,
        'epochs': args.epochs,
        'batch_size': args.batch_size,
        'learning_rate': args.learning_rate,
        'pos_weight_strategy': args.pos_weight_strategy,
        'feature_groups': selected_groups
    })
    
    # 记录指标
    for epoch, metrics in enumerate(history):
        mlflow.log_metrics({
            'train_loss': metrics['train_loss'],
            'val_loss': metrics['val_loss'],
            'val_auc': metrics['val_auc'],
            'learning_rate': metrics['lr']
        }, step=epoch)
    
    # 保存模型
    mlflow.pytorch.log_model(
        model,
        'model',
        registered_model_name=f'recommendation_{args.model_type}'
    )
    
    # 记录额外artifacts
    mlflow.log_artifact('feature_indices.json')
    mlflow.log_artifact('training_config.yaml')
```

## 性能优化技巧

### 1. 混合精度训练
```python
from torch.cuda.amp import autocast, GradScaler

scaler = GradScaler()
with autocast():
    outputs = model(batch_X)
    loss = criterion(outputs, batch_y)

scaler.scale(loss).backward()
scaler.step(optimizer)
scaler.update()
```

### 2. 数据并行
```python
if torch.cuda.device_count() > 1:
    model = nn.DataParallel(model)
```

### 3. 梯度累积
```python
accumulation_steps = 4
for i, (batch_X, batch_y) in enumerate(train_loader):
    outputs = model(batch_X)
    loss = criterion(outputs, batch_y)
    loss = loss / accumulation_steps
    loss.backward()
    
    if (i + 1) % accumulation_steps == 0:
        optimizer.step()
        optimizer.zero_grad()
```

## 常见问题处理

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| Loss不下降 | 学习率过小/过大 | 调整学习率或使用自适应优化器 |
| Loss为NaN | 梯度爆炸 | 使用梯度裁剪和批标准化 |
| AUC很低 | 类别严重不平衡 | 使用sqrt_balanced策略 |
| 过拟合 | 模型过于复杂 | 增加dropout，减少模型容量 |
| 训练很慢 | 批大小过小 | 增加批大小或使用梯度累积 |

**Why — 设计动机与取舍**

这个训练工作流的设计重点是解决推荐系统中普遍存在的类别不平衡问题。三种pos_weight策略提供了灵活性，sqrt_balanced在实践中表现最佳。特征选择机制允许快速实验不同的特征组合。自适应初始化和梯度监控确保了训练的稳定性。整个流程通过MLflow完整追踪，便于实验对比和模型管理。设计上在训练效率和模型性能之间找到了平衡点。