# 详细调用关系图 (Detailed Call Flow Diagrams)

基于现有代码库的深度分析，绘制完整的函数调用关系和数据流图。

## D1: 完整数据处理流水线调用图

```mermaid
graph TB
    subgraph "主入口点"
        Main[run_parallel_processing.py::main]
        Args[argparse参数解析]
        Config[加载PARALLEL_CONFIG]
        Main --> Args
        Args --> Config
    end
    
    subgraph "数据分析阶段"
        DA_Entry[run_data_analysis]
        DA_Class[DataAnalyzer.__init__]
        DA_Analyze[DataAnalyzer.analyze_dataset]
        DA_Single[DataAnalyzer.analyze_single_file]
        DA_Stats[统计信息计算]
        DA_Save[DataAnalyzer.save_analysis_results]
        
        Config --> DA_Entry
        DA_Entry --> DA_Class
        DA_Class --> DA_Analyze
        DA_Analyze --> DA_Single
        DA_Single --> DA_Stats
        DA_Stats --> DA_Save
    end
    
    subgraph "并行处理阶段"
        PP_Entry[run_parallel_processing]
        PP_Class[ParallelProcessor.__init__]
        PP_Process[ParallelProcessor.process_dataset]
        PP_Parallel[ParallelProcessor.process_files_parallel]
        PP_Executor[ProcessPoolExecutor]
        PP_Worker[ParallelProcessor._process_single_file_worker]
        PP_Merge[ParallelProcessor._merge_and_save_results]
        
        DA_Save --> PP_Entry
        PP_Entry --> PP_Class
        PP_Class --> PP_Process
        PP_Process --> PP_Parallel
        PP_Parallel --> PP_Executor
        PP_Executor --> PP_Worker
        PP_Worker --> PP_Merge
    end
    
    subgraph "预处理核心"
        IP_Load[load_analysis_results]
        IP_Class[IntelligentPreprocessor.__init__]
        IP_Process[IntelligentPreprocessor.process_parquet_chunk]
        IP_Extract[IntelligentPreprocessor._extract_features]
        IP_Normalize[IntelligentPreprocessor._normalize_features]
        IP_Save[保存features.npy和labels.npy]
        
        PP_Worker --> IP_Load
        IP_Load --> IP_Class
        IP_Class --> IP_Process
        IP_Process --> IP_Extract
        IP_Extract --> IP_Normalize
        IP_Normalize --> IP_Save
    end
    
    subgraph "I/O工具层"
        S3_Utils[s3_utils.py]
        S3_List[list_parquet_files]
        S3_Read[read_parquet_file]
        S3_Exists[path_exists]
        Common_IO[common/io.py]
        IO_Save[save_npy]
        
        DA_Single --> S3_Read
        PP_Parallel --> S3_List
        PP_Worker --> S3_Exists
        IP_Save --> IO_Save
        S3_Read --> S3_Utils
        IO_Save --> Common_IO
    end
    
    style Main fill:#e1f5fe
    style DA_Entry fill:#f3e5f5
    style PP_Entry fill:#e8f5e8
    style IP_Class fill:#fff3e0
    style S3_Utils fill:#fce4ec
```

## D2: 模型训练完整调用链

```mermaid
graph TB
    subgraph "训练入口"
        Train_Main[train_loss_optimized.py::main]
        Train_Args[argparse.ArgumentParser]
        Train_Device[get_device_config]
        Train_Opt[train_model_optimized]
        
        Train_Main --> Train_Args
        Train_Args --> Train_Device
        Train_Device --> Train_Opt
    end
    
    subgraph "数据加载"
        Load_Data[load_and_normalize_data]
        Load_Train[load_processed_data_train]
        Load_Val[load_processed_data_validation]
        Load_Test[load_processed_data_test]
        Load_NPY[np.load读取npy文件]

        Train_Opt --> Load_Data
        Load_Data --> Load_Train
        Load_Data --> Load_Val
        Load_Data --> Load_Test
        Load_Train --> Load_NPY
    end
    
    subgraph "特征管理"
        FM_Check[检查include_groups/exclude_groups]
        FM_Init[FeatureManager.__init__]
        FM_Load[_load_metadata读取JSON]
        FM_Indices[get_feature_indices]
        FM_Filter[特征过滤逻辑]
        
        Train_Opt --> FM_Check
        FM_Check --> FM_Init
        FM_Init --> FM_Load
        FM_Load --> FM_Indices
        FM_Indices --> FM_Filter
    end
    
    subgraph "模型构建"
        Model_Config[get_model_config]
        Model_Build[build_model工厂方法]
        Model_Types[MLP/DCNv2/DCNv1/DLRM选择]
        Model_Init[模型.__init__]
        
        Train_Opt --> Model_Config
        Model_Config --> Model_Build
        Model_Build --> Model_Types
        Model_Types --> Model_Init
    end
    
    subgraph "自适应初始化"
        AI_Check[enable_adaptive_init检查]
        AI_Create[create_adaptive_model]
        AI_Init[AdaptiveInitializer.__init__]
        AI_Smart[smart_init_model]
        AI_Forward[forward_pass_analysis]
        AI_Scale[_calculate_scale]
        
        Model_Init --> AI_Check
        AI_Check --> AI_Create
        AI_Create --> AI_Init
        AI_Init --> AI_Smart
        AI_Smart --> AI_Forward
        AI_Forward --> AI_Scale
    end
    
    style Train_Main fill:#e1f5fe
    style Load_Data fill:#f3e5f5
    style Model_Build fill:#e8f5e8
    style AI_Check fill:#fff3e0
```

## D3: 训练循环详细调用图

```mermaid
graph TB
    subgraph "训练准备"
        TL_Setup[训练循环设置]
        TL_Loader[DataLoader创建]
        TL_Optimizer[optimizer设置]
        TL_Criterion[criterion设置]
        TL_Scheduler[scheduler设置]
        TL_AMP[AMP scaler设置]
        
        TL_Setup --> TL_Loader
        TL_Setup --> TL_Optimizer
        TL_Setup --> TL_Criterion
        TL_Setup --> TL_Scheduler
        TL_Setup --> TL_AMP
    end
    
    subgraph "梯度监控初始化"
        GM_Check[enable_gradient_monitor检查]
        GM_Init[GradientMonitor.__init__]
        GM_Hook[register_hooks]
        GM_Model[绑定到model]
        
        TL_Setup --> GM_Check
        GM_Check --> GM_Init
        GM_Init --> GM_Hook
        GM_Hook --> GM_Model
    end
    
    subgraph "训练主循环"
        TL_Epoch[epoch循环遍历]
        TL_Batch[batch循环遍历]
        TL_Forward[model前向传播]
        TL_Loss[criterion损失计算]
        TL_Backward[loss反向传播]
        TL_Clip[clip_grad_norm梯度裁剪]
        TL_Step[optimizer优化步骤]

        TL_Loader --> TL_Epoch
        TL_Epoch --> TL_Batch
        TL_Batch --> TL_Forward
        TL_Forward --> TL_Loss
        TL_Loss --> TL_Backward
        TL_Backward --> TL_Clip
        TL_Clip --> TL_Step
    end
    
    subgraph "梯度监控"
        GM_Log[gradient_monitor.log_gradients]
        GM_Stats[get_gradient_stats]
        GM_Check_Exp[check_gradient_explosion]
        GM_Warning[梯度异常警告]
        
        TL_Backward --> GM_Log
        GM_Log --> GM_Stats
        GM_Stats --> GM_Check_Exp
        GM_Check_Exp --> GM_Warning
    end
    
    subgraph "验证阶段"
        Val_Epoch[validate_epoch]
        Val_Forward[model.eval() + forward]
        Val_Loss[验证损失计算]
        Val_AUC[AUC计算]
        Val_Best[最佳模型保存]
        
        TL_Epoch --> Val_Epoch
        Val_Epoch --> Val_Forward
        Val_Forward --> Val_Loss
        Val_Loss --> Val_AUC
        Val_AUC --> Val_Best
    end
    
    subgraph "混合精度训练"
        AMP_Autocast[torch_cuda_amp_autocast]
        AMP_Scale[scaler_scale_loss]
        AMP_Unscale[scaler_unscale]
        AMP_Update[scaler_update]

        TL_Forward --> AMP_Autocast
        TL_Loss --> AMP_Scale
        TL_Clip --> AMP_Unscale
        TL_Step --> AMP_Update
    end
    
    style TL_Setup fill:#e1f5fe
    style TL_Epoch fill:#f3e5f5
    style GM_Log fill:#e8f5e8
    style Val_Epoch fill:#fff3e0
```

## D4: 特征工程详细流程图

```mermaid
graph TB
    subgraph "特征元数据生成"
        SMF_Entry[save_feature_metadata.py]
        SMF_Extract[extract_feature_metadata]
        SMF_Scan[scan_processed_data扫描NPY文件]
        SMF_Analyze[分析特征维度和类型]
        SMF_Generate[generate_metadata]
        SMF_JSON[保存为feature_metadata.json]

        SMF_Entry --> SMF_Extract
        SMF_Extract --> SMF_Scan
        SMF_Scan --> SMF_Analyze
        SMF_Analyze --> SMF_Generate
        SMF_Generate --> SMF_JSON
    end

    subgraph "特征管理器初始化"
        FM_Init[FeatureManager.__init__]
        FM_Load[_load_metadata读取JSON]
        FM_Parse[解析metadata结构]
        FM_Build[_build_indices]
        FM_Groups[_build_feature_groups]
        FM_Names[_build_name_index]

        SMF_JSON --> FM_Init
        FM_Init --> FM_Load
        FM_Load --> FM_Parse
        FM_Parse --> FM_Build
        FM_Build --> FM_Groups
        FM_Build --> FM_Names
    end

    subgraph "特征选择接口"
        FS_Get[get_feature_indices]
        FS_Validate[参数验证]
        FS_Names[by_names处理]
        FS_Groups[by_groups处理]
        FS_Types[by_types处理]
        FS_Exclude[exclude参数处理]
        FS_Intersect[求交集]
        FS_Return[返回索引列表]

        FM_Build --> FS_Get
        FS_Get --> FS_Validate
        FS_Validate --> FS_Names
        FS_Validate --> FS_Groups
        FS_Validate --> FS_Types
        FS_Validate --> FS_Exclude
        FS_Names --> FS_Intersect
        FS_Groups --> FS_Intersect
        FS_Types --> FS_Intersect
        FS_Exclude --> FS_Intersect
        FS_Intersect --> FS_Return
    end

    subgraph "特征组定义(基于代码)"
        Groups[特征组类型]
        Basic[基础特征_conversion_age_gender等]
        User[user前缀_user_embedding展开列]
        Item[item前缀_item_embedding展开列]
        Context[context前缀_context_embedding展开列]
        Click[click前缀_click_position等]
        Session[session前缀_session_length等]
        Time[time前缀_time_spent等]
        Income[income前缀_income_level等]
        Noise[noise前缀_noise_1_noise_2等]

        FS_Groups --> Groups
        Groups --> Basic
        Groups --> User
        Groups --> Item
        Groups --> Context
        Groups --> Click
        Groups --> Session
        Groups --> Time
        Groups --> Income
        Groups --> Noise
    end

    subgraph "数据类型处理"
        Types[数据类型分类]
        Numeric[numeric: float32数值列]
        Categorical[categorical: 分类列]
        Array[array: 原始embedding列]
        Expanded[expanded: 展开后的列]

        FS_Types --> Types
        Types --> Numeric
        Types --> Categorical
        Types --> Array
        Array --> Expanded
    end

    style SMF_Entry fill:#e1f5fe
    style FM_Init fill:#f3e5f5
    style FS_Get fill:#e8f5e8
    style Groups fill:#fff3e0
```

## D5: S3和I/O系统详细调用图

```mermaid
graph TB
    subgraph "S3工具系统"
        S3_Manager[S3FileManager单例]
        S3_Init[__init__初始化]
        S3_Config[读取S3_CONFIG]
        S3_FS[_create_s3fs创建文件系统]
        S3_Local[_create_process_local_fs]
        S3_Pool[连接池管理]

        S3_Manager --> S3_Init
        S3_Init --> S3_Config
        S3_Config --> S3_FS
        S3_FS --> S3_Local
        S3_Local --> S3_Pool
    end

    subgraph "文件操作实现"
        List_Files[list_parquet_files]
        List_S3[S3路径处理]
        List_Local[本地路径处理]
        List_Glob[glob模式匹配]
        Read_File[read_parquet_file]
        Read_Pandas[pd.read_parquet]
        Read_Columns[columns参数处理]

        List_Files --> List_S3
        List_Files --> List_Local
        List_S3 --> List_Glob
        Read_File --> Read_Pandas
        Read_File --> Read_Columns
    end

    subgraph "通用I/O工具"
        Common_IO[common/io.py]
        IO_Read[read_parquet统一接口]
        IO_S3_Check[is_s3路径检查]
        IO_Save[save_npy原子写入]
        IO_Temp[临时文件处理]
        IO_Atomic[原子替换]

        Common_IO --> IO_Read
        IO_Read --> IO_S3_Check
        Common_IO --> IO_Save
        IO_Save --> IO_Temp
        IO_Temp --> IO_Atomic
    end

    subgraph "路径处理和检测"
        Path_Detect[路径类型检测]
        S3_Path[is_s3_path检查]
        Path_Clean[路径清理]
        Path_Norm[路径标准化]
        Path_Exists[path_exists检查]

        IO_S3_Check --> Path_Detect
        Path_Detect --> S3_Path
        List_S3 --> Path_Clean
        Path_Clean --> Path_Norm
        Read_File --> Path_Exists
    end

    subgraph "错误处理和重试"
        Error_Handle[错误处理]
        Retry_Logic[重试机制]
        Timeout_Handle[超时处理]
        Fallback[本地降级]
        Log_Error[错误日志]

        Read_File --> Error_Handle
        Error_Handle --> Retry_Logic
        Retry_Logic --> Timeout_Handle
        Timeout_Handle --> Fallback
        Error_Handle --> Log_Error
    end

    subgraph "性能优化"
        Perf_Opt[性能优化]
        Connection_Pool[连接池复用]
        Parallel_Read[并行读取]
        Buffer_Mgmt[缓冲区管理]
        Memory_Map[内存映射]

        S3_Pool --> Perf_Opt
        Perf_Opt --> Connection_Pool
        Perf_Opt --> Parallel_Read
        Perf_Opt --> Buffer_Mgmt
        Perf_Opt --> Memory_Map
    end

    style S3_Manager fill:#e1f5fe
    style List_Files fill:#f3e5f5
    style Common_IO fill:#e8f5e8
    style Error_Handle fill:#fff3e0
```

## D6: 模型架构和前向传播调用图

```mermaid
graph TB
    subgraph "模型工厂系统"
        Factory[build_model工厂函数]
        Type_Check[model_type参数检查]
        Config_Map[配置参数映射]
        Model_Create[模型类实例化]

        Factory --> Type_Check
        Type_Check --> Config_Map
        Config_Map --> Model_Create
    end

    subgraph "MLP模型实现"
        MLP_Init[MLP.__init__]
        MLP_Layers[构建hidden_layers]
        MLP_Forward[forward方法]
        MLP_Sequential[Sequential层组合]
        MLP_Linear[nn.Linear层]
        MLP_ReLU[nn.ReLU激活]
        MLP_Dropout[nn.Dropout]
        MLP_Output[输出层]

        Model_Create --> MLP_Init
        MLP_Init --> MLP_Layers
        MLP_Layers --> MLP_Sequential
        MLP_Sequential --> MLP_Linear
        MLP_Linear --> MLP_ReLU
        MLP_ReLU --> MLP_Dropout
        MLP_Layers --> MLP_Forward
        MLP_Forward --> MLP_Output
    end

    subgraph "DCNv2模型实现"
        DCN_Init[DCNv2.__init__]
        DCN_Cross[CrossNetwork构建]
        DCN_Deep[DeepNetwork构建]
        DCN_Forward[forward方法]
        DCN_Cross_Forward[cross_network前向]
        DCN_Deep_Forward[deep_network前向]
        DCN_Concat[torch.cat拼接]
        DCN_Final[最终输出层]

        Model_Create --> DCN_Init
        DCN_Init --> DCN_Cross
        DCN_Init --> DCN_Deep
        DCN_Cross --> DCN_Forward
        DCN_Deep --> DCN_Forward
        DCN_Forward --> DCN_Cross_Forward
        DCN_Forward --> DCN_Deep_Forward
        DCN_Cross_Forward --> DCN_Concat
        DCN_Deep_Forward --> DCN_Concat
        DCN_Concat --> DCN_Final
    end

    subgraph "DLRM模型实现"
        DLRM_Init[DLRM.__init__]
        DLRM_Bottom[bottom_mlp构建]
        DLRM_Inter[dot_interaction层]
        DLRM_Top[top_mlp构建]
        DLRM_Forward[forward方法]
        DLRM_Bottom_Forward[bottom前向传播]
        DLRM_Interaction[特征交互计算]
        DLRM_Top_Forward[top前向传播]

        Model_Create --> DLRM_Init
        DLRM_Init --> DLRM_Bottom
        DLRM_Init --> DLRM_Inter
        DLRM_Init --> DLRM_Top
        DLRM_Bottom --> DLRM_Forward
        DLRM_Inter --> DLRM_Forward
        DLRM_Top --> DLRM_Forward
        DLRM_Forward --> DLRM_Bottom_Forward
        DLRM_Bottom_Forward --> DLRM_Interaction
        DLRM_Interaction --> DLRM_Top_Forward
    end

    subgraph "权重初始化系统"
        Init_System[权重初始化]
        Default_Init[默认初始化]
        Xavier_Init[Xavier初始化]
        Kaiming_Init[Kaiming初始化]
        Custom_Init[自定义初始化]
        Adaptive_Init[自适应初始化]

        MLP_Layers --> Init_System
        DCN_Cross --> Init_System
        DLRM_Bottom --> Init_System
        Init_System --> Default_Init
        Init_System --> Xavier_Init
        Init_System --> Kaiming_Init
        Init_System --> Custom_Init
        Custom_Init --> Adaptive_Init
    end

    style Factory fill:#e1f5fe
    style MLP_Init fill:#f3e5f5
    style DCN_Init fill:#e8f5e8
    style DLRM_Init fill:#fff3e0
```

## D7: 监控和日志系统调用图

```mermaid
graph TB
    subgraph "梯度监控系统"
        GM_Class[GradientMonitor类]
        GM_Init[__init__初始化]
        GM_Hook[register_hooks注册钩子]
        GM_Log[log_gradients记录梯度]
        GM_Stats[get_gradient_stats统计]
        GM_Check[check_gradient_explosion检查]
        GM_Plot[plot_gradient_history绘图]
        GM_Save[保存统计文件]

        GM_Class --> GM_Init
        GM_Init --> GM_Hook
        GM_Hook --> GM_Log
        GM_Log --> GM_Stats
        GM_Stats --> GM_Check
        GM_Stats --> GM_Plot
        GM_Plot --> GM_Save
    end

    subgraph "自适应初始化系统"
        AI_Class[AdaptiveInitializer类]
        AI_Init[__init__初始化]
        AI_Smart[smart_init_model主方法]
        AI_Forward[forward_pass_analysis]
        AI_Scale[_calculate_scale计算缩放]
        AI_Apply[_apply_scaling应用缩放]
        AI_Validate[_validate_initialization验证]
        AI_Stats[返回初始化统计]

        AI_Class --> AI_Init
        AI_Init --> AI_Smart
        AI_Smart --> AI_Forward
        AI_Forward --> AI_Scale
        AI_Scale --> AI_Apply
        AI_Apply --> AI_Validate
        AI_Validate --> AI_Stats
    end

    subgraph "最小日志器系统"
        ML_Class[MinimalLogger类]
        ML_Init[__init__初始化]
        ML_Process[process_track进程跟踪]
        ML_Request[log_request请求日志]
        ML_Response[log_response响应日志]
        ML_Worker[log_worker_start工作器日志]
        ML_Complete[log_worker_complete完成日志]
        ML_Summary[log_summary汇总日志]

        ML_Class --> ML_Init
        ML_Init --> ML_Process
        ML_Process --> ML_Request
        ML_Process --> ML_Response
        ML_Process --> ML_Worker
        ML_Worker --> ML_Complete
        ML_Complete --> ML_Summary
    end

    subgraph "训练集成"
        Train_Monitor[训练中的监控集成]
        Train_GM[enable_gradient_monitor]
        Train_AI[enable_adaptive_init]
        Train_Log[日志记录集成]
        Train_Track[进度跟踪]

        Train_Monitor --> Train_GM
        Train_Monitor --> Train_AI
        Train_Monitor --> Train_Log
        Train_Log --> Train_Track

        Train_GM --> GM_Log
        Train_AI --> AI_Smart
        Train_Track --> ML_Process
    end

    subgraph "并行处理集成"
        PP_Monitor[并行处理监控]
        PP_Track[跟踪处理进度]
        PP_Worker_Log[Worker日志记录]
        PP_Stats[统计信息收集]
        PP_Request[请求响应跟踪]

        PP_Monitor --> PP_Track
        PP_Track --> PP_Worker_Log
        PP_Worker_Log --> PP_Stats
        PP_Stats --> PP_Request

        PP_Track --> ML_Request
        PP_Worker_Log --> ML_Worker
        PP_Stats --> ML_Summary
    end

    style GM_Class fill:#e1f5fe
    style AI_Class fill:#f3e5f5
    style ML_Class fill:#e8f5e8
    style Train_Monitor fill:#fff3e0
```

## D8: 配置系统和依赖关系图

```mermaid
graph TB
    subgraph "配置中心"
        Config_File[config.py]
        Platform_Detect[平台检测]
        Storage_Detect[存储类型检测]
        Instance_Config[实例配置获取]
        Parallel_Config[并行配置生成]
        Performance_Config[性能配置生成]

        Config_File --> Platform_Detect
        Config_File --> Storage_Detect
        Config_File --> Instance_Config
        Instance_Config --> Parallel_Config
        Instance_Config --> Performance_Config
    end

    subgraph "平台适配逻辑"
        Platform_Check[platform_system调用]
        Windows_Config[Windows配置]
        Unix_Config[Unix配置]
        EC2_Check[IS_EC2_DEPLOYMENT检测]
        S3_Path_Check[is_s3_path检查]
        Storage_Type[STORAGE_TYPE确定]

        Platform_Detect --> Platform_Check
        Platform_Check --> Windows_Config
        Platform_Check --> Unix_Config
        Storage_Detect --> S3_Path_Check
        S3_Path_Check --> Storage_Type
        Storage_Type --> EC2_Check
    end

    subgraph "实例配置计算"
        CPU_Count[mp_cpu_count调用]
        Memory_Detect[内存检测]
        Instance_Type[实例类型判断]
        Worker_Calc[Worker数量计算]
        Chunk_Size[分块大小计算]

        Instance_Config --> CPU_Count
        Instance_Config --> Memory_Detect
        CPU_Count --> Instance_Type
        Instance_Type --> Worker_Calc
        Worker_Calc --> Chunk_Size
    end

    subgraph "性能优化配置"
        Dataloader_Workers[dataloader_workers]
        Pin_Memory[pin_memory设置]
        Prefetch_Factor[prefetch_factor]
        Batch_Multiplier[batch_size_multiplier]
        Thread_Config[线程配置]

        Performance_Config --> Dataloader_Workers
        Performance_Config --> Pin_Memory
        Performance_Config --> Prefetch_Factor
        Performance_Config --> Batch_Multiplier
        Performance_Config --> Thread_Config
    end

    subgraph "模块依赖导入"
        PP_Import[parallel_processor.py导入]
        DA_Import[data_analyzer.py导入]
        IP_Import[preprocess.py导入]
        FM_Import[feature_manager.py导入]
        Train_Import[train_loss_optimized.py导入]

        Parallel_Config --> PP_Import
        Storage_Type --> DA_Import
        Performance_Config --> IP_Import
        Config_File --> FM_Import
        Config_File --> Train_Import
    end

    style Config_File fill:#e1f5fe
    style Platform_Check fill:#f3e5f5
    style CPU_Count fill:#e8f5e8
    style Performance_Config fill:#fff3e0
```

## D9: 数据流和内存管理调用图

```mermaid
graph TB
    subgraph "数据加载器系统"
        DL_Create[DataLoader创建]
        DL_Dataset[TensorDataset]
        DL_Batch[batch_size设置]
        DL_Shuffle[shuffle设置]
        DL_Workers[num_workers设置]
        DL_Pin[pin_memory设置]
        DL_Iter[__iter__迭代器]

        DL_Create --> DL_Dataset
        DL_Create --> DL_Batch
        DL_Create --> DL_Shuffle
        DL_Create --> DL_Workers
        DL_Create --> DL_Pin
        DL_Dataset --> DL_Iter
    end

    subgraph "内存管理系统"
        Memory_Monitor[内存监控]
        Chunk_Process[分块处理]
        Cache_Mgmt[缓存管理]
        GC_Control[垃圾回收控制]
        Memory_Limit[内存限制检查]
        OOM_Prevention[OOM预防]

        DL_Batch --> Memory_Monitor
        Memory_Monitor --> Chunk_Process
        Memory_Monitor --> Cache_Mgmt
        Memory_Monitor --> GC_Control
        GC_Control --> Memory_Limit
        Memory_Limit --> OOM_Prevention
    end

    subgraph "数据预处理流水线"
        Preprocess_Pipeline[预处理流水线]
        Load_NPY[加载NPY文件]
        Normalize_Data[数据标准化]
        Feature_Select[特征选择]
        Data_Transform[数据变换]
        Tensor_Convert[转换为Tensor]

        Chunk_Process --> Preprocess_Pipeline
        Preprocess_Pipeline --> Load_NPY
        Load_NPY --> Normalize_Data
        Normalize_Data --> Feature_Select
        Feature_Select --> Data_Transform
        Data_Transform --> Tensor_Convert
    end

    subgraph "特征工程流水线"
        Feature_Pipeline[特征工程流水线]
        Feature_Extract[特征提取]
        Feature_Combine[特征组合]
        Feature_Encode[特征编码]
        Feature_Scale[特征缩放]
        Feature_Validate[特征验证]

        Data_Transform --> Feature_Pipeline
        Feature_Pipeline --> Feature_Extract
        Feature_Extract --> Feature_Combine
        Feature_Combine --> Feature_Encode
        Feature_Encode --> Feature_Scale
        Feature_Scale --> Feature_Validate
    end

    subgraph "并行处理系统"
        Parallel_System[并行处理系统]
        Process_Pool[进程池]
        Thread_Pool[线程池]
        Queue_Mgmt[队列管理]
        Task_Dispatch[任务分发]
        Result_Collect[结果收集]

        Chunk_Process --> Parallel_System
        Parallel_System --> Process_Pool
        Parallel_System --> Thread_Pool
        Process_Pool --> Queue_Mgmt
        Queue_Mgmt --> Task_Dispatch
        Task_Dispatch --> Result_Collect
    end

    subgraph "设备管理系统"
        Device_Mgmt[设备管理]
        CPU_Process[CPU处理]
        GPU_Process[GPU处理]
        Device_Transfer[设备间传输]
        CUDA_Memory[CUDA内存管理]
        AMP_Process[混合精度处理]

        Tensor_Convert --> Device_Mgmt
        Device_Mgmt --> CPU_Process
        Device_Mgmt --> GPU_Process
        GPU_Process --> Device_Transfer
        Device_Transfer --> CUDA_Memory
        CUDA_Memory --> AMP_Process
    end

    style DL_Create fill:#e1f5fe
    style Memory_Monitor fill:#f3e5f5
    style Feature_Pipeline fill:#e8f5e8
    style Parallel_System fill:#fff3e0
```

## D10: 完整系统交互和数据流图

```mermaid
graph TB
    subgraph "用户入口层"
        User_Entry[用户入口]
        Run_Script[run_parallel_processing.py]
        Train_Script[train_loss_optimized.py]
        Check_Script[check_data.py]

        User_Entry --> Run_Script
        User_Entry --> Train_Script
        User_Entry --> Check_Script
    end

    subgraph "配置和初始化层"
        Config_Load[配置加载]
        Platform_Init[平台初始化]
        Storage_Init[存储初始化]
        Logger_Init[日志器初始化]

        Run_Script --> Config_Load
        Train_Script --> Config_Load
        Config_Load --> Platform_Init
        Platform_Init --> Storage_Init
        Storage_Init --> Logger_Init
    end

    subgraph "数据处理层"
        Data_Analysis[数据分析]
        Parallel_Process[并行处理]
        Feature_Extract[特征提取]
        Data_Save[数据保存]

        Logger_Init --> Data_Analysis
        Data_Analysis --> Parallel_Process
        Parallel_Process --> Feature_Extract
        Feature_Extract --> Data_Save
    end

    subgraph "模型训练层"
        Data_Load[数据加载]
        Feature_Mgmt[特征管理]
        Model_Build[模型构建]
        Training_Loop[训练循环]
        Model_Save[模型保存]

        Data_Save --> Data_Load
        Data_Load --> Feature_Mgmt
        Feature_Mgmt --> Model_Build
        Model_Build --> Training_Loop
        Training_Loop --> Model_Save
    end

    subgraph "监控和优化层"
        Gradient_Monitor[梯度监控]
        Adaptive_Init[自适应初始化]
        Performance_Monitor[性能监控]
        Error_Handle[错误处理]

        Training_Loop --> Gradient_Monitor
        Model_Build --> Adaptive_Init
        Parallel_Process --> Performance_Monitor
        Data_Analysis --> Error_Handle
    end

    subgraph "存储和I/O层"
        S3_Storage[S3存储]
        Local_Storage[本地存储]
        NPY_Files[NPY文件]
        JSON_Metadata[JSON元数据]
        Model_Artifacts[模型文件]

        Data_Save --> NPY_Files
        Feature_Extract --> JSON_Metadata
        Model_Save --> Model_Artifacts
        S3_Storage --> NPY_Files
        Local_Storage --> NPY_Files
    end

    subgraph "工具和辅助层"
        S3_Utils[S3工具]
        Common_IO[通用I/O]
        Minimal_Logger[最小日志器]
        Feature_Selection[特征选择]

        S3_Storage --> S3_Utils
        Local_Storage --> Common_IO
        Performance_Monitor --> Minimal_Logger
        Feature_Mgmt --> Feature_Selection
    end

    style User_Entry fill:#e1f5fe
    style Data_Analysis fill:#f3e5f5
    style Training_Loop fill:#e8f5e8
    style Gradient_Monitor fill:#fff3e0
    style S3_Storage fill:#fce4ec
```

## 总结：基于代码的调用关系分析

### 核心发现

1. **主要入口点**：
   - `run_parallel_processing.py::main()` - 数据处理入口
   - `train_loss_optimized.py::main()` - 训练入口
   - 两者通过NPY文件和JSON元数据连接

2. **关键调用链**：
   - 数据处理：`DataAnalyzer` → `ParallelProcessor` → `IntelligentPreprocessor`
   - 模型训练：`load_and_normalize_data` → `FeatureManager` → `build_model` → `training_loop`
   - 监控系统：`GradientMonitor` + `AdaptiveInitializer` + `MinimalLogger`

3. **配置驱动架构**：
   - `config.py`作为配置中心，驱动所有模块的行为
   - 平台自适应：Windows/Unix/EC2环境自动适配
   - 存储自适应：S3/本地存储自动切换

4. **并行处理模式**：
   - 数据处理：多进程并行（`ProcessPoolExecutor`）
   - 模型训练：单进程GPU加速（混合精度训练）
   - I/O操作：异步和缓存优化

5. **特征工程流水线**：
   - 元数据驱动：`feature_metadata.json`定义特征结构
   - 动态选择：`FeatureManager`支持灵活的特征组合
   - 类型感知：自动处理numeric/categorical/array类型

### 架构优势

- **模块化设计**：每个组件职责单一，接口清晰
- **配置驱动**：通过配置文件控制行为，易于调优
- **错误恢复**：多层错误处理和重试机制
- **性能优化**：针对不同环境的性能调优
- **监控完备**：全面的梯度、性能和进度监控

这些详细的调用关系图展示了一个设计良好的机器学习训练系统，具有清晰的分层架构和完善的错误处理机制。
