"""S3 backend selector and utilities for real/mock/skip modes."""

import os
import sys
from pathlib import Path
from typing import Literal


def select_s3_backend() -> Literal['real', 'mock', 'skip']:
    """Select S3 backend based on environment and configuration.
    
    Priority order:
    1. S3_BACKEND environment variable (explicit)
    2. AWS credentials detection (if auto)
    3. Platform defaults (Windows=mock, non-Windows=real)
    4. Fallback to skip
    
    Returns:
        'real', 'mock', or 'skip'
    """
    # Get explicit backend setting
    backend_env = os.environ.get('S3_BACKEND', 'auto').lower()
    
    # Handle explicit settings
    if backend_env in ('real', 'mock', 'skip'):
        return backend_env
    
    # Handle auto mode
    if backend_env == 'auto':
        # Check for AWS credentials
        has_aws_creds = bool(
            os.environ.get('AWS_ACCESS_KEY_ID') or
            os.environ.get('AWS_PROFILE') or
            os.environ.get('AWS_ROLE_ARN')
        )
        
        if has_aws_creds:
            return 'real'
        
        # Platform defaults
        if sys.platform == 'win32':
            return 'mock'
        else:
            return 'real'
    
    # Unknown value, fallback to skip
    return 'skip'


def get_mock_s3_root() -> Path:
    """Get the root directory for mock S3 storage.
    
    Returns:
        Path object for mock S3 root
    """
    root = os.environ.get('MOCK_S3_ROOT', './_s3mock')
    return Path(root)


def to_mock_path(s3_url: str, root: Path = None) -> Path:
    """Convert S3 URL to local mock path.
    
    Args:
        s3_url: S3 URL like 's3://bucket/path/to/file.parquet'
        root: Root directory for mock storage (default: from MOCK_S3_ROOT)
        
    Returns:
        Local path like '{MOCK_S3_ROOT}/bucket/path/to/file.parquet'
    """
    if not s3_url.startswith('s3://'):
        raise ValueError(f"Invalid S3 URL: {s3_url}")
    
    if root is None:
        root = get_mock_s3_root()
    
    # Remove s3:// prefix and convert to path
    path_parts = s3_url[5:].split('/')
    return root / Path(*path_parts)


# Configuration defaults (environment variables)
def get_io_retry_max() -> int:
    """Get maximum retry count for I/O operations."""
    return int(os.environ.get('IO_RETRY_MAX', '0'))


def get_io_retry_backoff() -> float:
    """Get retry backoff multiplier."""
    return float(os.environ.get('IO_RETRY_BACKOFF', '0.5'))


def get_io_timeout_s() -> float:
    """Get I/O timeout in seconds (0 = no timeout)."""
    return float(os.environ.get('IO_TIMEOUT_S', '0'))