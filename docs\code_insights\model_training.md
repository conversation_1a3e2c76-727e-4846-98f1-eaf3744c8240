# 模型训练流程 Model Training

## 训练流水线概览

```mermaid
sequenceDiagram
    participant User as 用户
    participant CLI as 命令行接口
    participant Main as train_loss_optimized.main()
    participant Loader as 数据加载器
    participant FM as FeatureManager
    participant Model as 模型构建
    participant Init as AdaptiveInitializer
    participant Train as training_loop()
    participant Monitor as GradientMonitor
    participant MLflow as MLflow追踪
    participant Save as 模型保存

    User->>CLI: python train_loss_optimized.py --model_type dcnv2
    CLI->>Main: 解析参数
    Main->>Loader: load_and_normalize_data()
    Loader-->>Main: 返回 X_train, y_train, X_val, y_val
    
    Main->>FM: FeatureManager.get_feature_indices()
    FM-->>Main: 返回特征索引
    
    Main->>Model: build_model(model_type='dcnv2', input_size=177)
    Model-->>Main: 返回DCNv2模型实例
    
    Main->>Init: AdaptiveInitializer.initialize()
    Init->>Model: 调整权重初始化
    Init-->>Main: 初始化完成
    
    Main->>Train: training_loop(model, train_loader, val_loader)
    
    loop 每个Epoch
        Train->>Monitor: 监控梯度
        Monitor-->>Train: 梯度统计
        Train->>MLflow: 记录指标
        Train->>Train: 反向传播和参数更新
    end
    
    Train-->>Main: 返回训练历史
    Main->>Save: torch.save(model.state_dict())
    Main->>MLflow: 记录模型artifacts
    Main-->>User: 训练完成
```

## 核心训练脚本分析

### train_loss_optimized.py - 损失优化版本

```python
def main():
    """
    主训练入口
    
    命令行参数:
    --model_type: 模型类型 (mlp/dcnv1/dcnv2/dlrm)
    --epochs: 训练轮数 (默认10)
    --batch_size: 批大小 (默认1024)
    --learning_rate: 学习率 (默认0.001)
    --pos_weight_strategy: 正样本权重策略
    --include_groups: 包含的特征组
    --exclude_groups: 排除的特征组
    """
    args = parse_args()
    
    # 数据加载和标准化
    X_train, y_train, X_val, y_val = load_and_normalize_data()
    
    # 特征选择
    feature_manager = FeatureManager()
    feature_indices = feature_manager.get_feature_indices(
        include_groups=args.include_groups,
        exclude_groups=args.exclude_groups
    )
    
    # 调整特征维度
    X_train = X_train[:, feature_indices]
    X_val = X_val[:, feature_indices]
    input_size = len(feature_indices)
    
    # 构建模型
    model = build_model(args.model_type, input_size)
    
    # 自适应初始化
    if args.use_adaptive_init:
        initializer = AdaptiveInitializer(X_train, y_train)
        initializer.initialize(model)
    
    # 训练
    history = train_model_optimized(
        model, X_train, y_train, X_val, y_val,
        epochs=args.epochs,
        batch_size=args.batch_size,
        learning_rate=args.learning_rate,
        pos_weight_strategy=args.pos_weight_strategy
    )
    
    return model, history

def train_model_optimized(model, X_train, y_train, X_val, y_val, **kwargs):
    """
    优化的训练函数
    
    输入:
    - model: PyTorch模型
    - X_train: (n_samples, n_features) 训练特征
    - y_train: (n_samples,) 训练标签
    - X_val: 验证集特征
    - y_val: 验证集标签
    
    处理:
    1. 计算类别权重
    2. 创建数据加载器
    3. 设置优化器和调度器
    4. 训练循环
    5. 梯度监控
    
    输出:
    - history: 训练历史字典
    """
    # 计算pos_weight
    pos_weight = calculate_pos_weight(y_train, kwargs['pos_weight_strategy'])
    
    # 创建数据集和加载器
    train_dataset = TensorDataset(
        torch.FloatTensor(X_train),
        torch.FloatTensor(y_train)
    )
    train_loader = DataLoader(
        train_dataset, 
        batch_size=kwargs['batch_size'],
        shuffle=True
    )
    
    # 优化器
    optimizer = torch.optim.Adam(
        model.parameters(), 
        lr=kwargs['learning_rate']
    )
    
    # 损失函数
    criterion = nn.BCEWithLogitsLoss(pos_weight=pos_weight)
    
    # 学习率调度器
    scheduler = ReduceLROnPlateau(
        optimizer, mode='min', patience=3
    )
    
    # 梯度监控器
    gradient_monitor = GradientMonitor(model)
    
    # 训练循环
    history = {'train_loss': [], 'val_loss': [], 'val_auc': []}
    
    for epoch in range(kwargs['epochs']):
        # 训练阶段
        model.train()
        train_loss = 0
        
        for batch_X, batch_y in train_loader:
            optimizer.zero_grad()
            outputs = model(batch_X).squeeze()
            loss = criterion(outputs, batch_y)
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), 1.0)
            
            optimizer.step()
            train_loss += loss.item()
        
        # 验证阶段
        val_loss, val_auc = evaluate_model(model, X_val, y_val)
        
        # 记录历史
        history['train_loss'].append(train_loss / len(train_loader))
        history['val_loss'].append(val_loss)
        history['val_auc'].append(val_auc)
        
        # 学习率调整
        scheduler.step(val_loss)
        
        # 梯度统计
        grad_stats = gradient_monitor.get_gradient_stats()
        
        # MLflow记录
        mlflow.log_metrics({
            'train_loss': train_loss / len(train_loader),
            'val_loss': val_loss,
            'val_auc': val_auc,
            'grad_norm': grad_stats['mean_norm']
        }, step=epoch)
    
    return history
```

## 模型架构详解

### DCNv2模型结构

```python
class DCNv2(nn.Module):
    """
    Deep & Cross Network v2
    
    参数大小计算:
    - Cross Network: input_size * num_cross_layers * input_size
    - Deep Network: sum(hidden_sizes[i] * hidden_sizes[i+1])
    - 总参数: ~50K-200K depending on configuration
    """
    
    def __init__(self, input_size=177, hidden_sizes=[256, 128], 
                 num_cross_layers=3, dropout_rate=0.1):
        super().__init__()
        
        # Cross Network
        self.cross_layers = nn.ModuleList([
            CrossLayer(input_size) for _ in range(num_cross_layers)
        ])
        
        # Deep Network
        layers = []
        prev_size = input_size
        for hidden_size in hidden_sizes:
            layers.extend([
                nn.Linear(prev_size, hidden_size),
                nn.BatchNorm1d(hidden_size),
                nn.ReLU(),
                nn.Dropout(dropout_rate)
            ])
            prev_size = hidden_size
        self.deep_network = nn.Sequential(*layers)
        
        # Combination Layer
        final_size = input_size + hidden_sizes[-1]
        self.final_layer = nn.Linear(final_size, 1)
    
    def forward(self, x):
        # Cross Network
        cross_output = x
        for cross_layer in self.cross_layers:
            cross_output = cross_layer(cross_output, x)
        
        # Deep Network
        deep_output = self.deep_network(x)
        
        # Combine
        combined = torch.cat([cross_output, deep_output], dim=1)
        output = self.final_layer(combined)
        
        return output
```

### 模型参数与特征维度关系

| 模型 | 输入维度 | 隐藏层 | 参数量 | 内存占用 | 推理延迟 |
|------|---------|--------|--------|----------|----------|
| MLP | 177 | [256, 128, 64] | ~75K | ~300KB | <1ms |
| DCNv1 | 177 | [256, 128] + 2 cross | ~100K | ~400KB | <2ms |
| DCNv2 | 177 | [256, 128] + 3 cross | ~150K | ~600KB | <2ms |
| DLRM | 177 | [256, 128] + embeddings | ~500K | ~2MB | <5ms |

### 为什么选择这些参数大小？

1. **输入维度177**：
   - 平衡了特征表达能力和计算效率
   - 避免维度过高导致的过拟合
   - 可通过特征选择动态调整

2. **隐藏层[256, 128]**：
   - 逐层递减的结构有助于特征抽象
   - 256维足够捕获特征交互
   - 128维作为最终表示较为紧凑

3. **Cross层数3**：
   - 3阶交叉足够捕获大部分特征交互
   - 更多层数收益递减
   - 计算成本与效果的平衡

## 损失优化策略

### 三种pos_weight策略对比

```python
def calculate_pos_weight(y_train, strategy='balanced'):
    """
    计算正样本权重
    
    输入:
    - y_train: 训练标签
    - strategy: 权重策略
    
    策略说明:
    - balanced: pos_weight = neg_count / pos_count
    - sqrt_balanced: pos_weight = sqrt(neg_count / pos_count)
    - log_balanced: pos_weight = log(1 + neg_count / pos_count)
    """
    pos_count = (y_train == 1).sum()
    neg_count = (y_train == 0).sum()
    
    if strategy == 'balanced':
        # 完全平衡，可能过度补偿
        pos_weight = neg_count / pos_count  # 约等于9.0
    elif strategy == 'sqrt_balanced':
        # 温和平衡，实践中效果最好
        pos_weight = np.sqrt(neg_count / pos_count)  # 约等于3.0
    elif strategy == 'log_balanced':
        # 保守平衡，避免过拟合
        pos_weight = np.log(1 + neg_count / pos_count)  # 约等于2.3
    else:
        pos_weight = 1.0
    
    return torch.FloatTensor([pos_weight])
```

### 策略选择依据

| 策略 | pos_weight值 | 适用场景 | 优点 | 缺点 |
|------|-------------|---------|------|------|
| balanced | ~9.0 | 极度不平衡 | 完全补偿 | 可能过拟合正样本 |
| sqrt_balanced | ~3.0 | 中度不平衡 | 平衡效果好 | 需要调参 |
| log_balanced | ~2.3 | 轻度不平衡 | 稳定 | 补偿可能不足 |

## 训练优化技术

### 1. 自适应初始化

```python
class AdaptiveInitializer:
    """adaptive_init.py中的自适应初始化器"""
    
    def initialize(self, model, X_sample):
        """
        基于数据特征的权重初始化
        
        处理:
        1. 分析输入数据分布
        2. 根据激活函数选择初始化方法
        3. 调整初始化尺度
        """
        data_std = X_sample.std()
        
        for module in model.modules():
            if isinstance(module, nn.Linear):
                # Xavier初始化，根据数据调整
                fan_in = module.weight.size(1)
                std = data_std * np.sqrt(2.0 / fan_in)
                module.weight.data.normal_(0, std)
                if module.bias is not None:
                    module.bias.data.zero_()
```

### 2. 梯度监控

```python
class GradientMonitor:
    """gradient_monitor.py中的梯度监控器"""
    
    def get_gradient_stats(self):
        """
        获取梯度统计信息
        
        输出:
        - mean_norm: 平均梯度范数
        - max_norm: 最大梯度范数
        - num_zeros: 梯度为零的参数数
        """
        stats = {
            'mean_norm': 0,
            'max_norm': 0,
            'num_zeros': 0
        }
        
        for param in self.model.parameters():
            if param.grad is not None:
                grad_norm = param.grad.norm().item()
                stats['mean_norm'] += grad_norm
                stats['max_norm'] = max(stats['max_norm'], grad_norm)
                if grad_norm < 1e-8:
                    stats['num_zeros'] += 1
        
        return stats
```

### 3. 学习率调度

```python
# 使用ReduceLROnPlateau自动调整学习率
scheduler = ReduceLROnPlateau(
    optimizer,
    mode='min',          # 监控val_loss
    factor=0.5,          # 减半学习率
    patience=3,          # 3个epoch无改善则调整
    min_lr=1e-6         # 最小学习率
)
```

## 模型评估与注册

### 评估指标

```python
def evaluate_model(model, X_val, y_val):
    """
    模型评估
    
    输出:
    - loss: 验证损失
    - auc: AUC得分
    - precision: 精确率
    - recall: 召回率
    """
    model.eval()
    with torch.no_grad():
        outputs = model(torch.FloatTensor(X_val)).squeeze()
        probs = torch.sigmoid(outputs).numpy()
        
        # 计算指标
        loss = F.binary_cross_entropy_with_logits(
            outputs, torch.FloatTensor(y_val)
        ).item()
        
        auc = roc_auc_score(y_val, probs)
        
        # 使用0.5作为阈值
        preds = (probs > 0.5).astype(int)
        precision = precision_score(y_val, preds)
        recall = recall_score(y_val, preds)
    
    return loss, auc, precision, recall
```

### 模型注册到MLflow

```python
# MLflow模型注册
with mlflow.start_run():
    # 记录参数
    mlflow.log_params({
        'model_type': 'dcnv2',
        'input_size': 177,
        'hidden_sizes': [256, 128],
        'learning_rate': 0.001,
        'batch_size': 1024,
        'epochs': 10
    })
    
    # 记录指标
    mlflow.log_metric('final_val_auc', val_auc)
    mlflow.log_metric('final_val_loss', val_loss)
    
    # 保存模型
    mlflow.pytorch.log_model(
        model, 
        'model',
        registered_model_name='recommendation_dcnv2'
    )
```

## 线上推理流程

```python
class OnlinePredictor:
    """线上推理服务"""
    
    def __init__(self, model_path):
        self.model = self.load_model(model_path)
        self.feature_processor = FeatureProcessor()
        self.model.eval()
    
    def predict(self, user_features, item_features):
        """
        实时推理
        
        输入:
        - user_features: 用户特征字典
        - item_features: 物品特征字典
        
        输出:
        - score: 推荐分数[0, 1]
        """
        # 特征处理
        features = self.feature_processor.process(
            user_features, item_features
        )
        
        # 模型推理
        with torch.no_grad():
            features_tensor = torch.FloatTensor(features).unsqueeze(0)
            logit = self.model(features_tensor).squeeze()
            score = torch.sigmoid(logit).item()
        
        return score
    
    def batch_predict(self, feature_batch):
        """批量推理，提高吞吐量"""
        with torch.no_grad():
            features_tensor = torch.FloatTensor(feature_batch)
            logits = self.model(features_tensor).squeeze()
            scores = torch.sigmoid(logits).numpy()
        
        return scores
```

## 内存与延迟权衡

### 不同配置下的性能对比

| 配置 | 模型大小 | 内存占用 | 单次推理 | 批量推理(1000) | AUC |
|------|---------|----------|---------|---------------|-----|
| 小型 | 75KB | 50MB | 0.5ms | 10ms | 0.75 |
| 中型 | 150KB | 100MB | 1ms | 20ms | 0.78 |
| 大型 | 600KB | 200MB | 2ms | 50ms | 0.80 |
| 超大 | 2MB | 500MB | 5ms | 200ms | 0.81 |

### 优化建议

1. **开发环境**：使用大型模型，追求最高精度
2. **生产环境**：使用中型模型，平衡精度和延迟
3. **边缘部署**：使用小型模型，优先考虑延迟

**Why — 设计动机与取舍**

训练流程的设计核心是"稳定性"和"效率"。选择BCEWithLogitsLoss而非BCELoss是为了数值稳定性；使用自适应初始化和梯度监控是为了加速收敛；三种pos_weight策略提供了灵活性，可根据具体数据分布选择；模型大小的设计在精度和延迟之间寻求平衡，150K参数的DCNv2模型在大多数场景下都能取得良好效果。整个训练流程通过MLflow追踪，确保实验的可重现性和可比较性。