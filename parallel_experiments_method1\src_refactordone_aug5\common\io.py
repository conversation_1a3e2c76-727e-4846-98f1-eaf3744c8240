"""Common I/O operations for data loading and saving."""

import os
import json
from pathlib import Path
from typing import Union, List, Optional
import pandas as pd
import numpy as np


def ensure_dir(path: Union[str, Path]) -> Path:
    """Ensure directory exists, create if not.
    
    Args:
        path: Directory path (string or Path object)
        
    Returns:
        Path object of the directory
    """
    path_obj = Path(path)
    path_obj.mkdir(parents=True, exist_ok=True)
    return path_obj


def is_s3(path: str) -> bool:
    """Check if path is an S3 path."""
    return isinstance(path, str) and path.startswith('s3://')


def read_parquet(path: Union[str, Path], columns: Optional[List[str]] = None) -> pd.DataFrame:
    """Read parquet file with optional column selection.
    
    Args:
        path: Path to parquet file (local or s3://)
        columns: Optional list of columns to read
        
    Returns:
        DataFrame with the data
    """
    if is_s3(str(path)):
        # Import s3_utils only when needed
        from s3_utils import read_parquet_file
        # s3_utils.read_parquet_file already handles columns parameter
        return read_parquet_file(str(path), columns=columns)
    else:
        # Local file - use pandas
        return pd.read_parquet(path, columns=columns)


def save_npy(array: np.ndarray, path: Union[str, Path], *, atomic: bool = True) -> None:
    """Save numpy array to file, optionally with atomic write.
    
    Args:
        array: Numpy array to save
        path: Path to save the file
        atomic: If True, write to temp file first then rename (safer)
    """
    path_obj = Path(path)
    
    if atomic:
        # Write to temporary file first
        temp_path = path_obj.with_suffix('.tmp')
        np.save(temp_path, array)
        
        # Check file size before replacing
        if temp_path.stat().st_size > 0:
            temp_path.replace(path_obj)
        else:
            temp_path.unlink()
            raise ValueError(f"Failed to save array: temporary file is empty")
    else:
        np.save(path_obj, array)


def read_json(path: Union[str, Path]) -> dict:
    """Read JSON file.
    
    Args:
        path: Path to JSON file
        
    Returns:
        Parsed JSON data
    """
    with open(path, 'r', encoding='utf-8') as f:
        return json.load(f)


def write_json(obj: dict, path: Union[str, Path]) -> None:
    """Write object to JSON file.
    
    Args:
        obj: Object to serialize to JSON
        path: Path to save the file
    """
    with open(path, 'w', encoding='utf-8') as f:
        json.dump(obj, f, indent=2, ensure_ascii=False)


def list_parquet_files(path: Union[str, Path]) -> List[str]:
    """List parquet files in directory.
    
    Args:
        path: Directory path (local or s3://)
        
    Returns:
        List of parquet file paths
    """
    if is_s3(str(path)):
        from s3_utils import list_parquet_files as s3_list_parquet_files
        return s3_list_parquet_files(str(path))
    else:
        # Local directory
        path_obj = Path(path)
        return [str(f) for f in path_obj.glob('*.parquet')]


def path_exists(path: Union[str, Path]) -> bool:
    """Check if path exists.
    
    Args:
        path: Path to check (local or s3://)
        
    Returns:
        True if path exists, False otherwise
    """
    if is_s3(str(path)):
        from s3_utils import path_exists as s3_path_exists
        return s3_path_exists(str(path))
    else:
        return Path(path).exists()


def join_path(*parts: Union[str, Path]) -> str:
    """Join path components.
    
    Args:
        *parts: Path components to join
        
    Returns:
        Joined path as string
    """
    if parts and isinstance(parts[0], str) and parts[0]:
        # Use os.path.join for compatibility with existing code
        return os.path.join(*[str(p) for p in parts])
    return str(Path(*parts))


def log_connection_stats(operation: str = "") -> None:
    """Log S3 connection statistics if using S3.
    
    Args:
        operation: Operation name for logging
    """
    # Only import and use if we're actually working with S3
    path = os.getenv('TRAIN_DATA_DIR', '')
    if path.startswith('s3://'):
        try:
            from s3_utils import log_connection_stats as s3_log_stats
            s3_log_stats(operation)
        except ImportError:
            pass  # S3 utils not available
        except Exception:
            pass  # Ignore any errors in logging


def cleanup_s3_filesystem() -> None:
    """Clean up S3 filesystem connections if using S3."""
    # Only import and use if we're actually working with S3
    path = os.getenv('TRAIN_DATA_DIR', '')
    if path.startswith('s3://'):
        try:
            from s3_utils import cleanup_s3_filesystem as s3_cleanup
            s3_cleanup()
        except ImportError:
            pass  # S3 utils not available
        except Exception:
            pass  # Ignore any errors in cleanup


def safe_filename(name: str) -> str:
    """Convert string to safe filename (placeholder for future implementation).
    
    Args:
        name: Original filename
        
    Returns:
        Safe filename
    """
    # TODO: Implement filename sanitization in future steps
    return name