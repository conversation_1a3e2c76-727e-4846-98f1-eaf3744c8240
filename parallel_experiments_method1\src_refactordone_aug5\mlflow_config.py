# mlflow_config.py
"""
MLflow配置和工具函数
用于实验跟踪、参数记录、指标记录和模型注册
"""

import os
import mlflow
import mlflow.pytorch
import torch
import json
from datetime import datetime
from pathlib import Path
import logging

# MLflow配置
MLFLOW_TRACKING_URI = "file:./mlruns"  # 本地文件系统存储
EXPERIMENT_NAME = "recommendation_models_method1"

# 设置MLflow跟踪URI
mlflow.set_tracking_uri(MLFLOW_TRACKING_URI)

def setup_mlflow_experiment(experiment_name=EXPERIMENT_NAME):
    """
    设置MLflow实验
    
    Args:
        experiment_name: 实验名称
    
    Returns:
        experiment_id: 实验ID
    """
    try:
        # 尝试获取现有实验
        experiment = mlflow.get_experiment_by_name(experiment_name)
        if experiment is None:
            # 创建新实验
            experiment_id = mlflow.create_experiment(experiment_name)
            logging.info(f"Created new MLflow experiment: {experiment_name} (ID: {experiment_id})")
        else:
            experiment_id = experiment.experiment_id
            logging.info(f"Using existing MLflow experiment: {experiment_name} (ID: {experiment_id})")
        
        mlflow.set_experiment(experiment_name)
        return experiment_id
    except Exception as e:
        logging.error(f"Error setting up MLflow experiment: {e}")
        raise

def start_mlflow_run(run_name=None, tags=None):
    """
    开始MLflow运行
    
    Args:
        run_name: 运行名称
        tags: 标签字典
    
    Returns:
        run: MLflow运行对象
    """
    if run_name is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        run_name = f"run_{timestamp}"
    
    if tags is None:
        tags = {}
    
    # 添加默认标签
    tags.update({
        "timestamp": datetime.now().isoformat(),
        "framework": "pytorch",
        "method": "parallel_method1"
    })
    
    run = mlflow.start_run(run_name=run_name, tags=tags)
    logging.info(f"Started MLflow run: {run_name} (ID: {run.info.run_id})")
    return run

def log_model_params(model_type, config, input_dim):
    """
    记录模型参数
    
    Args:
        model_type: 模型类型
        config: 配置字典
        input_dim: 输入维度
    """
    # 记录基本参数
    mlflow.log_param("model_type", model_type)
    mlflow.log_param("input_dim", input_dim)
    
    # 记录配置参数
    for key, value in config.items():
        if isinstance(value, (int, float, str, bool)):
            mlflow.log_param(key, value)
        elif isinstance(value, (list, tuple)):
            mlflow.log_param(key, str(value))
        else:
            mlflow.log_param(key, str(value))

def log_training_metrics(epoch, train_loss, val_loss, val_auc, learning_rate=None):
    """
    记录训练指标
    
    Args:
        epoch: 当前轮次
        train_loss: 训练损失
        val_loss: 验证损失
        val_auc: 验证AUC
        learning_rate: 学习率
    """
    mlflow.log_metric("train_loss", train_loss, step=epoch)
    mlflow.log_metric("val_loss", val_loss, step=epoch)
    mlflow.log_metric("val_auc", val_auc, step=epoch)
    
    if learning_rate is not None:
        mlflow.log_metric("learning_rate", learning_rate, step=epoch)

def log_test_metrics(test_loss, test_auc):
    """
    记录测试指标
    
    Args:
        test_loss: 测试损失
        test_auc: 测试AUC
    """
    mlflow.log_metric("test_loss", test_loss)
    mlflow.log_metric("test_auc", test_auc)

def log_model_artifact(model, model_type, epoch, timestamp):
    """
    记录模型文件
    
    Args:
        model: PyTorch模型
        model_type: 模型类型
        epoch: 最佳轮次
        timestamp: 时间戳
    """
    # 保存模型到临时文件
    model_filename = f"{model_type}_best_epoch_{epoch}_{timestamp}.pth"
    model_path = f"./temp_{model_filename}"
    
    try:
        torch.save(model.state_dict(), model_path)
        
        # 记录模型文件
        mlflow.log_artifact(model_path, "models")
        
        # 记录PyTorch模型（用于模型注册）
        mlflow.pytorch.log_model(
            pytorch_model=model,
            artifact_path="pytorch_model",
            registered_model_name=f"{model_type}_model_method1"
        )
        
        logging.info(f"Logged model artifact: {model_filename}")
        
    except Exception as e:
        logging.error(f"Error logging model artifact: {e}")
    finally:
        # 清理临时文件
        if os.path.exists(model_path):
            os.remove(model_path)

def log_config_file(config_dict, filename="config.json"):
    """
    记录配置文件
    
    Args:
        config_dict: 配置字典
        filename: 文件名
    """
    try:
        # 保存配置到临时文件
        temp_config_path = f"./temp_{filename}"
        with open(temp_config_path, 'w') as f:
            json.dump(config_dict, f, indent=2, default=str)
        
        # 记录配置文件
        mlflow.log_artifact(temp_config_path, "configs")
        
        logging.info(f"Logged config file: {filename}")
        
    except Exception as e:
        logging.error(f"Error logging config file: {e}")
    finally:
        # 清理临时文件
        if os.path.exists(temp_config_path):
            os.remove(temp_config_path)

def end_mlflow_run():
    """
    结束MLflow运行
    """
    mlflow.end_run()
    logging.info("Ended MLflow run")

def get_best_run(experiment_name=EXPERIMENT_NAME, metric_name="val_auc", ascending=False):
    """
    获取最佳运行
    
    Args:
        experiment_name: 实验名称
        metric_name: 指标名称
        ascending: 是否升序排列
    
    Returns:
        best_run: 最佳运行信息
    """
    try:
        experiment = mlflow.get_experiment_by_name(experiment_name)
        if experiment is None:
            logging.warning(f"Experiment {experiment_name} not found")
            return None
        
        runs = mlflow.search_runs(
            experiment_ids=[experiment.experiment_id],
            order_by=[f"metrics.{metric_name} {'ASC' if ascending else 'DESC'}"]
        )
        
        if len(runs) == 0:
            logging.warning(f"No runs found in experiment {experiment_name}")
            return None
        
        best_run = runs.iloc[0]
        logging.info(f"Best run: {best_run['run_id']} with {metric_name}={best_run[f'metrics.{metric_name}']}")
        return best_run
        
    except Exception as e:
        logging.error(f"Error getting best run: {e}")
        return None

def compare_models(experiment_name=EXPERIMENT_NAME, metric_name="val_auc"):
    """
    比较不同模型的性能
    
    Args:
        experiment_name: 实验名称
        metric_name: 比较指标
    
    Returns:
        comparison_df: 比较结果DataFrame
    """
    try:
        experiment = mlflow.get_experiment_by_name(experiment_name)
        if experiment is None:
            logging.warning(f"Experiment {experiment_name} not found")
            return None
        
        runs = mlflow.search_runs(experiment_ids=[experiment.experiment_id])
        
        if len(runs) == 0:
            logging.warning(f"No runs found in experiment {experiment_name}")
            return None
        
        # 选择关键列进行比较
        comparison_cols = [
            'run_id', 'start_time', 'status',
            'params.model_type', 'params.learning_rate', 'params.batch_size',
            f'metrics.{metric_name}', 'metrics.test_auc', 'metrics.train_loss'
        ]
        
        available_cols = [col for col in comparison_cols if col in runs.columns]
        comparison_df = runs[available_cols].copy()
        
        # 按指标排序
        if f'metrics.{metric_name}' in comparison_df.columns:
            comparison_df = comparison_df.sort_values(f'metrics.{metric_name}', ascending=False)
        
        logging.info(f"Compared {len(comparison_df)} runs")
        return comparison_df
        
    except Exception as e:
        logging.error(f"Error comparing models: {e}")
        return None
