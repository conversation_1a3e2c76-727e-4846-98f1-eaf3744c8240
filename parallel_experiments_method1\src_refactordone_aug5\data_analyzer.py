# data_analyzer.py
"""
数据分析模块 - 自动分析Parquet文件的结构和特征
"""

import pandas as pd
import numpy as np
import glob
import os
import json
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
from config import (
    TRAIN_DATA_DIR, VALIDATION_DATA_DIR, TEST_DATA_DIR,
    ANALYSIS_OUTPUT_DIR, LABEL_CANDIDATES, LABEL_COLUMN_NAME, MIN_ARRAY_LENGTH, ANALYSIS_SAMPLE_SIZE,
    USE_S3, is_s3_path
)
from common import io as common_io

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class DataAnalyzer:
    """
    数据分析器 - 自动分析Parquet文件结构
    """
    
    def __init__(self):
        self.analysis_results = {}
        
    def analyze_single_file(self, file_path: str, sample_size: int = ANALYSIS_SAMPLE_SIZE) -> Dict[str, Any]:
        """
        分析单个Parquet文件（支持本地和S3）

        Args:
            file_path: Parquet文件路径（本地或S3）
            sample_size: 采样大小

        Returns:
            分析结果字典
        """
        file_name = file_path.split('/')[-1] if '/' in file_path else os.path.basename(file_path)
        logging.info(f"Analyzing file: {file_name}")

        try:
            # 读取文件（采样以提高速度）- 支持S3和本地
            logging.info(f"Attempting to read parquet file: {file_path}")
            logging.info(f"File is S3 path: {is_s3_path(file_path)}")

            df = common_io.read_parquet(file_path)
            logging.info(f"Successfully read file. Shape: {df.shape}")
            logging.info(f"Columns: {list(df.columns)}")

            if len(df) > sample_size:
                logging.info(f"Sampling {sample_size} rows from {len(df)} total rows")
                df = df.sample(n=sample_size, random_state=42)
            else:
                logging.info(f"Using all {len(df)} rows (less than sample size {sample_size})")
            
            analysis = {
                'file_path': file_path,
                'total_rows': len(df),
                'total_columns': len(df.columns),
                'columns': {},
                'label_column': None,
                'array_columns': [],
                'numeric_columns': [],
                'categorical_columns': []
            }
            
            # 分析每一列
            for col in df.columns:
                col_analysis = self._analyze_column(df[col], col)
                analysis['columns'][col] = col_analysis
                
                # 分类列类型
                if col_analysis['is_array']:
                    analysis['array_columns'].append(col)
                elif col_analysis['is_numeric']:
                    analysis['numeric_columns'].append(col)
                else:
                    analysis['categorical_columns'].append(col)
                    
                # 检测标签列 - 优先使用用户指定的LABEL_COLUMN_NAME
                if col == LABEL_COLUMN_NAME:
                    if col_analysis['is_binary'] or col_analysis['dtype'] in ['int64', 'int32', 'float64', 'float32']:
                        analysis['label_column'] = col
                        logging.info(f"Found user-specified label column: {col}")
                    else:
                        logging.warning(f"User-specified label column '{col}' found but may not be suitable (not binary/numeric)")
                        analysis['label_column'] = col
                elif analysis['label_column'] is None and col.lower() in [c.lower() for c in LABEL_CANDIDATES]:
                    # 如果用户指定的列不存在，则使用自动检测
                    if col_analysis['is_binary'] or col_analysis['dtype'] in ['int64', 'int32', 'float64', 'float32']:
                        analysis['label_column'] = col
                        logging.info(f"Auto-detected potential label column: {col}")

            # 检查是否找到标签列
            if analysis['label_column'] is None:
                logging.error(f"No label column found. User specified: '{LABEL_COLUMN_NAME}', Available columns: {list(df.columns)}")
                logging.error(f"Please check if LABEL_COLUMN_NAME in config.py matches an actual column name")

            return analysis
            
        except Exception as e:
            logging.error(f"Error analyzing file {file_path}: {str(e)}")
            return None
    
    def _analyze_column(self, series: pd.Series, col_name: str) -> Dict[str, Any]:
        """
        分析单个列
        
        Args:
            series: pandas Series
            col_name: 列名
            
        Returns:
            列分析结果
        """
        analysis = {
            'name': col_name,
            'dtype': str(series.dtype),
            'null_count': series.isnull().sum(),
            'null_percentage': series.isnull().sum() / len(series),
            'unique_count': 0,  # 将在后面计算
            'is_array': False,
            'is_numeric': False,
            'is_categorical': False,
            'is_binary': False,
            'array_length': None,
            'sample_values': []
        }
        
        # 获取样本值（非空）
        non_null_values = series.dropna()
        if len(non_null_values) > 0:
            sample_size = min(5, len(non_null_values))
            analysis['sample_values'] = non_null_values.head(sample_size).tolist()
        
        # 检测数组列
        if len(non_null_values) > 0:
            first_value = non_null_values.iloc[0]
            if isinstance(first_value, (list, np.ndarray)):
                analysis['is_array'] = True
                # 检查数组长度一致性
                array_lengths = []
                for val in non_null_values.head(100):  # 检查前100个值
                    if isinstance(val, (list, np.ndarray)):
                        array_lengths.append(len(val))

                if array_lengths:
                    unique_lengths = set(array_lengths)
                    if len(unique_lengths) == 1:
                        analysis['array_length'] = array_lengths[0]
                        logging.info(f"Array column {col_name}: fixed length = {analysis['array_length']}")
                    else:
                        logging.warning(f"Array column {col_name}: variable lengths = {unique_lengths}")
                        analysis['array_length'] = max(array_lengths)  # 使用最大长度

                # 对于数组列，不计算唯一值
                analysis['unique_count'] = len(non_null_values)  # 使用行数作为近似
                return analysis  # 提前返回，避免后续的唯一值计算
        
        # 检测数值列
        if not analysis['is_array']:
            try:
                analysis['unique_count'] = series.nunique()
            except:
                analysis['unique_count'] = len(non_null_values)

            if pd.api.types.is_numeric_dtype(series):
                analysis['is_numeric'] = True
                analysis['min_value'] = float(series.min()) if not series.empty else None
                analysis['max_value'] = float(series.max()) if not series.empty else None
                analysis['mean_value'] = float(series.mean()) if not series.empty else None

            # 智能数据类型推荐
            analysis['recommended_dtype'] = self._recommend_optimal_dtype(series)

            # 检测二分类
            try:
                unique_vals = set(non_null_values.unique())
                if len(unique_vals) == 2 and unique_vals.issubset({0, 1, 0.0, 1.0}):
                    analysis['is_binary'] = True
            except:
                # 如果无法计算唯一值，跳过二分类检测
                    pass
            else:
                analysis['is_categorical'] = True
                try:
                    analysis['unique_values'] = non_null_values.unique().tolist()[:10]  # 最多显示10个唯一值
                except:
                    analysis['unique_values'] = []
        
        return analysis

    def _recommend_optimal_dtype(self, series: pd.Series) -> str:
        """
        根据数据范围推荐最优的数据类型

        Args:
            series: pandas Series

        Returns:
            推荐的数据类型字符串
        """
        if not pd.api.types.is_numeric_dtype(series):
            return str(series.dtype)

        # 获取数据范围
        min_val = series.min()
        max_val = series.max()

        # 检查是否有小数部分
        has_decimals = False
        if pd.api.types.is_float_dtype(series):
            has_decimals = True
        elif pd.api.types.is_integer_dtype(series):
            # 检查是否所有值都是整数
            sample_values = series.dropna().head(1000)  # 采样检查
            has_decimals = not all(float(val).is_integer() for val in sample_values if pd.notna(val))

        if has_decimals:
            # 浮点数类型选择
            if abs(min_val) < 65504 and abs(max_val) < 65504:
                # float16 范围: ±65504
                return 'float16'
            elif abs(min_val) < 3.4e38 and abs(max_val) < 3.4e38:
                # float32 范围: ±3.4e38
                return 'float32'
            else:
                # 需要 float64
                return 'float64'
        else:
            # 整数类型选择
            if min_val >= 0:
                # 无符号整数
                if max_val <= 255:
                    return 'uint8'
                elif max_val <= 65535:
                    return 'uint16'
                elif max_val <= 4294967295:
                    return 'uint32'
                else:
                    return 'uint64'
            else:
                # 有符号整数
                if min_val >= -128 and max_val <= 127:
                    return 'int8'
                elif min_val >= -32768 and max_val <= 32767:
                    return 'int16'
                elif min_val >= -2147483648 and max_val <= 2147483647:
                    return 'int32'
                else:
                    return 'int64'
    
    def analyze_dataset(self, data_dir: str, dataset_name: str) -> Dict[str, Any]:
        """
        分析整个数据集（支持本地和S3）

        Args:
            data_dir: 数据目录（本地或S3路径）
            dataset_name: 数据集名称

        Returns:
            数据集分析结果
        """
        logging.info(f"=== Starting analysis for dataset: {dataset_name} ===")
        logging.info(f"Data directory: {data_dir}")
        logging.info(f"Is S3 path: {is_s3_path(data_dir)}")

        # 检查路径是否存在
        logging.info(f"Checking if path exists: {data_dir}")
        try:
            path_exists_result = common_io.path_exists(data_dir)
            logging.info(f"Path exists result: {path_exists_result}")
            if not path_exists_result:
                logging.error(f"Data directory does not exist: {data_dir}")
                return None
        except Exception as e:
            logging.error(f"Error checking path existence for {data_dir}: {str(e)}")
            return None

        # 获取所有Parquet文件（支持S3和本地）
        logging.info(f"Attempting to list parquet files in: {data_dir}")
        try:
            parquet_files = common_io.list_parquet_files(data_dir)
            logging.info(f"list_parquet_files returned: {parquet_files}")
            logging.info(f"Number of files found: {len(parquet_files) if parquet_files else 0}")

            if not parquet_files:
                logging.error(f"No parquet files found in {data_dir}")
                return None

            # Log first few file paths for debugging
            for i, file_path in enumerate(parquet_files[:3]):
                logging.info(f"File {i+1}: {file_path}")

        except Exception as e:
            logging.error(f"Error listing parquet files in {data_dir}: {str(e)}")
            return None

        logging.info(f"Successfully found {len(parquet_files)} parquet files")

        # 分析第一个文件来获取schema
        first_file = parquet_files[0]
        logging.info(f"Analyzing first file for schema: {first_file}")
        try:
            first_file_analysis = self.analyze_single_file(first_file)
            if not first_file_analysis:
                logging.error(f"Failed to analyze first file: {first_file}")
                return None
            logging.info(f"Successfully analyzed first file. Columns: {len(first_file_analysis.get('columns', {}))}")
        except Exception as e:
            logging.error(f"Exception while analyzing first file {first_file}: {str(e)}")
            return None
        
        # 验证其他文件的schema一致性
        schema_consistent = True
        for file_path in parquet_files[1:3]:  # 检查前几个文件
            try:
                # 读取少量数据来检查schema，然后取前10行
                df_check = common_io.read_parquet(file_path)
                df_check = df_check.head(10)  # 只取前10行

                df_first = common_io.read_parquet(parquet_files[0])
                df_first = df_first.head(10)  # 只取前10行

                if list(df_check.columns) != list(df_first.columns):
                    logging.warning(f"Schema inconsistency detected in {file_path}")
                    schema_consistent = False
            except Exception as e:
                logging.warning(f"Could not verify schema for {file_path}: {str(e)}")
        
        # 计算总行数（采样估算）
        total_rows_estimate = 0
        for file_path in parquet_files[:5]:  # 采样前5个文件
            try:
                df_sample = common_io.read_parquet(file_path)
                total_rows_estimate += len(df_sample)
            except Exception as e:
                logging.warning(f"Could not read {file_path} for row count: {str(e)}")
        
        if len(parquet_files) > 5:
            # 估算总行数
            avg_rows_per_file = total_rows_estimate / min(5, len(parquet_files))
            total_rows_estimate = int(avg_rows_per_file * len(parquet_files))
        
        dataset_analysis = {
            'dataset_name': dataset_name,
            'data_directory': data_dir,
            'num_files': len(parquet_files),
            'schema_consistent': schema_consistent,
            'estimated_total_rows': total_rows_estimate,
            'columns_analysis': first_file_analysis['columns'],
            'label_column': first_file_analysis['label_column'],
            'array_columns': first_file_analysis['array_columns'],
            'numeric_columns': first_file_analysis['numeric_columns'],
            'categorical_columns': first_file_analysis['categorical_columns'],
            'sample_file_analysis': first_file_analysis
        }
        
        return dataset_analysis
    
    def analyze_all_datasets(self) -> Dict[str, Any]:
        """
        分析所有数据集（train, validation, test）
        
        Returns:
            完整的分析结果
        """
        logging.info("Starting comprehensive data analysis...")
        
        datasets = {
            'train': TRAIN_DATA_DIR,
            'validation': VALIDATION_DATA_DIR,
            'test': TEST_DATA_DIR
        }

        # 过滤掉None的数据集路径
        datasets = {k: v for k, v in datasets.items() if v is not None}
        
        all_analysis = {
            'datasets': {},
            'summary': {},
            'recommendations': []
        }
        
        # 分析每个数据集
        logging.info(f"Found {len(datasets)} datasets to analyze: {list(datasets.keys())}")
        for dataset_name, data_dir in datasets.items():
            logging.info(f"Processing dataset '{dataset_name}' at path: {data_dir}")

            try:
                if common_io.path_exists(data_dir):
                    logging.info(f"Path exists for {dataset_name}, starting analysis...")
                    analysis = self.analyze_dataset(data_dir, dataset_name)
                    if analysis:
                        logging.info(f"Successfully analyzed {dataset_name} dataset")
                        all_analysis['datasets'][dataset_name] = analysis
                    else:
                        logging.error(f"Failed to analyze {dataset_name} dataset - analysis returned None")
                else:
                    logging.error(f"Dataset directory not found: {data_dir}")
            except Exception as e:
                logging.error(f"Exception while processing {dataset_name}: {str(e)}")

        logging.info(f"Completed dataset analysis. Successfully analyzed: {list(all_analysis['datasets'].keys())}")
        logging.info(f"Total datasets analyzed: {len(all_analysis['datasets'])}")

        # 生成总结和建议
        logging.info("Generating summary...")
        all_analysis['summary'] = self._generate_summary(all_analysis['datasets'])
        logging.info(f"Generated summary with keys: {list(all_analysis['summary'].keys()) if all_analysis['summary'] else 'EMPTY'}")

        logging.info("Generating recommendations...")
        all_analysis['recommendations'] = self._generate_recommendations(all_analysis['datasets'])
        
        return all_analysis
    
    def _generate_summary(self, datasets_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成分析总结
        """
        if not datasets_analysis:
            return {}
        
        # 使用第一个可用数据集作为参考
        reference_dataset = next(iter(datasets_analysis.values()))
        
        summary = {
            'total_datasets': len(datasets_analysis),
            'available_datasets': list(datasets_analysis.keys()),
            'total_columns': len(reference_dataset['columns_analysis']),
            'detected_label_column': reference_dataset['label_column'],
            'array_columns_count': len(reference_dataset['array_columns']),
            'numeric_columns_count': len(reference_dataset['numeric_columns']),
            'categorical_columns_count': len(reference_dataset['categorical_columns']),
            'array_columns': reference_dataset['array_columns'],
            'numeric_columns': reference_dataset['numeric_columns'],
            'categorical_columns': reference_dataset['categorical_columns']
        }
        
        return summary
    
    def _generate_recommendations(self, datasets_analysis: Dict[str, Any]) -> List[str]:
        """
        生成处理建议
        """
        recommendations = []
        
        if not datasets_analysis:
            recommendations.append("No datasets found for analysis")
            return recommendations
        
        reference_dataset = next(iter(datasets_analysis.values()))
        
        if not reference_dataset['label_column']:
            recommendations.append("Warning: No label column detected. Please manually specify the target column.")
        
        if reference_dataset['array_columns']:
            recommendations.append(f"Found {len(reference_dataset['array_columns'])} array columns. These will be flattened during preprocessing.")
        
        if not reference_dataset['schema_consistent']:
            recommendations.append("Warning: Schema inconsistency detected across files. Please verify data integrity.")
        
        return recommendations
    
    def save_analysis(self, analysis_results: Dict[str, Any], output_file: str = None) -> str:
        """
        保存分析结果到JSON文件
        
        Args:
            analysis_results: 分析结果
            output_file: 输出文件路径
            
        Returns:
            保存的文件路径
        """
        # 创建输出目录
        common_io.ensure_dir(ANALYSIS_OUTPUT_DIR)
        
        if output_file is None:
            output_file = os.path.join(ANALYSIS_OUTPUT_DIR, "data_analysis_results.json")
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_results, f, indent=2, ensure_ascii=False, default=str)
            
            logging.info(f"Analysis results saved to: {output_file}")
            return output_file
            
        except Exception as e:
            logging.error(f"Failed to save analysis results: {str(e)}")
            return None


def main():
    """
    主函数 - 执行数据分析
    """
    analyzer = DataAnalyzer()
    
    # 执行完整分析
    results = analyzer.analyze_all_datasets()
    
    # 保存结果
    output_file = analyzer.save_analysis(results)
    
    # 打印总结
    logging.info("=== FINAL ANALYSIS RESULTS ===")
    logging.info(f"Results keys: {list(results.keys())}")
    logging.info(f"Datasets analyzed: {list(results.get('datasets', {}).keys())}")
    logging.info(f"Summary keys: {list(results.get('summary', {}).keys())}")

    if results['summary']:
        logging.info("=== Data Analysis Summary ===")
        summary = results['summary']
        logging.info(f"Available datasets: {summary['available_datasets']}")
        logging.info(f"Total columns: {summary['total_columns']}")
        logging.info(f"Detected label column: {summary['detected_label_column']}")
        logging.info(f"Array columns ({summary['array_columns_count']}): {summary['array_columns']}")
        logging.info(f"Numeric columns ({summary['numeric_columns_count']}): {summary['numeric_columns']}")
        logging.info(f"Categorical columns ({summary['categorical_columns_count']}): {summary['categorical_columns']}")

        if results['recommendations']:
            logging.info("=== Recommendations ===")
            for rec in results['recommendations']:
                logging.info(f"- {rec}")
    else:
        logging.error("=== NO SUMMARY GENERATED ===")
        logging.error("This indicates that no datasets were successfully analyzed")

    return results


if __name__ == '__main__':
    main()
