{"$schema": "https://json.schemastore.org/claude-code-settings.json", "permissions": {"allow": ["Bash(*)", "Bash(..recommendation_venv_gpuScriptspython.exe -c \"from src import config; print(''config.py import OK''); print(''Has INCLUDE_FEATURE_GROUPS:'', hasattr(config, ''INCLUDE_FEATURE_GROUPS'')); from src import feature_selection; print(''feature_selection.py import OK''); from src import save_feature_metadata; print(''save_feature_metadata.py import OK'')\")", "<PERSON><PERSON>(python:*)", "Bash(../recommendation_venv_gpu/Scripts/python.exe tests/test_compliance.py --quick)", "Bash(../recommendation_venv_gpu/Scripts/python.exe src/run_parallel_processing.py --workers 2 --track-only)", "Bash(../recommendation_venv_gpu/Scripts/python.exe src/train_loss_optimized.py --model_type dcnv2 --epochs 2 --pos_weight_strategy sqrt_balanced)", "Bash(tree:*)", "Bash(find:*)", "Bash(ls:*)", "<PERSON><PERSON>(dir:*)"]}, "defaultMode": "bypassPermissions"}