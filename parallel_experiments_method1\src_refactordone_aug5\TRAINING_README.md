# PyTorch训练脚本说明

本目录包含两个PyTorch训练脚本，各有不同的用途和价值。

## 📁 文件概览

### 1. `train_pytorch.py` - 基础+并行版本
**用途：** 教学演示和基础并行性能对比

**特点：**
- ✅ 正确的PyTorch实现（相比原始NumPy版本）
- ✅ 基础并行配置（PyTorch/OMP/MKL线程 + DataLoader多进程）
- ✅ 完整的梯度下降实现
- ❌ 直接使用原始数据，无标准化
- ❌ 基本模型结构，无高级优化技巧
- ❌ 不处理类别不平衡问题

**预期结果：**
```
Train Loss: 53.85, Val Loss: 22.41, Val AUC: 0.4985
```

**价值：**
- 展示数据预处理的重要性
- 演示基础并行配置的效果
- 作为优化前后的对比基准
- 教学用途：理解为什么需要各种优化技巧

### 2. `train_pytorch_fixed.py` - 生产版本
**用途：** 实际训练和生产环境

**特点：**
- ✅ 数据标准化（StandardScaler）
- ✅ 类别平衡处理（WeightedRandomSampler）
- ✅ 批标准化（BatchNorm1d）
- ✅ 梯度裁剪（防止梯度爆炸）
- ✅ 学习率调度（ReduceLROnPlateau）
- ✅ 加权损失函数（处理类别不平衡）

**预期结果：**
```
Train Loss: 1.9195, Val Loss: 1.0685, Val AUC: 0.5058
```

**价值：**
- 获得稳定的训练效果
- 包含机器学习最佳实践
- 适用于生产环境

## 🚀 使用指南

### 基础+并行版本（对比用）
```bash
python src/train_pytorch.py --model_type mlp --learning_rate 1.2e-3 --epochs 1
```

### 生产版本（推荐）
```bash
python src/train_pytorch_fixed.py --model_type mlp --learning_rate 1.2e-3 --epochs 5
```

## 📊 结果对比

| 指标 | 基础+并行版本 | 生产版本 | 改进 |
|------|---------------|----------|------|
| 训练损失 | 53.85 | 1.92 | ⬇️ 96.4% |
| 验证损失 | 22.41 | 1.07 | ⬇️ 95.2% |
| 验证AUC | 0.4985 | 0.5058 | ⬆️ 1.5% |
| 数值稳定性 | ⚠️ 部分稳定 | ✅ 完全稳定 | 显著改善 |
| 并行配置 | ✅ 基础并行 | ✅ 完整并行 | 都有并行 |

## 🔍 关键改进点

### 1. 并行性能优化
**基础版本改进：** 添加基础并行配置
- PyTorch/OMP/MKL线程设置为CPU核心数
- DataLoader使用4个worker进程
- 启用pin_memory优化

### 2. 数据标准化
**问题：** 原始数据特征尺度差异巨大（0-6991）
**解决：** StandardScaler标准化到均值0，标准差1（仅生产版本）

### 3. 类别平衡
**问题：** 正样本只占10%，严重不平衡
**解决：** WeightedRandomSampler + 加权损失函数（仅生产版本）

### 4. 训练稳定性
**问题：** 梯度爆炸，数值不稳定
**解决：** 批标准化 + 梯度裁剪 + 学习率调度（仅生产版本）

## 🎯 使用建议

### 学习和理解
1. 先运行 `train_pytorch.py` 看到问题
2. 再运行 `train_pytorch_fixed.py` 看到改进
3. 对比结果，理解各种优化技巧的作用

### 实际项目
- 直接使用 `train_pytorch_fixed.py`
- 根据具体数据调整超参数
- 可以基于此版本进一步优化

## 🚀 并行性能对比

### 基础版本并行配置
```
PyTorch线程: 24 (CPU核心数)
OMP线程: 24
MKL线程: 24
DataLoader workers: 4
Pin memory: True (if CUDA available)
```

### 生产版本并行配置
```
PyTorch线程: 24 (CPU核心数)
OMP线程: 24
MKL线程: 24
DataLoader workers: 智能配置 (根据环境)
WeightedRandomSampler: 类别平衡
BatchNorm: 批标准化
梯度裁剪: max_norm=1.0
学习率调度: ReduceLROnPlateau
```

## 📝 技术细节

### 数据统计（标准化前）
```
特征范围: -4.28 到 6991.00
特征均值: 384.10
特征标准差: 1047.98
正样本比例: 9.78%
```

### 模型架构
```
输入层: 17个特征
隐藏层: [256, 128, 64] (生产版本)
输出层: 1个神经元 (二分类)
激活函数: ReLU + Sigmoid
正则化: Dropout + BatchNorm
```

## 🔧 故障排除

### 如果基础版本损失过高
这是正常的！基础版本损失值（53.85）比原始版本（78.72）已有改善，但仍然较高，这正是为了展示数据预处理的重要性。

### 如果生产版本效果仍不好
1. 检查数据质量（可能是合成数据）
2. 增加训练轮次
3. 调整学习率
4. 尝试不同的模型架构

## 📚 学习价值

通过对比这两个版本，你可以深入理解：
- 数据预处理的重要性
- 类别不平衡的处理方法
- 训练稳定性优化技巧
- 机器学习最佳实践

这种对比学习方法比单纯看最终版本更有教育价值！
