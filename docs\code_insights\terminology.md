# 术语表 Terminology

## A

**AdaptiveInitializer** - 自适应初始化器
- 根据数据特征动态调整模型权重初始化策略的组件
- 位于 `adaptive_init.py`

**AUC** - Area Under Curve (曲线下面积)
- 用于评估二分类模型性能的指标，取值范围[0,1]
- 值越接近1表示模型性能越好

## B

**Batch** - 批次
- 训练时一次处理的样本集合
- 默认批大小(batch_size)为1024

**Batch Normalization** - 批标准化
- 对每个批次的数据进行标准化处理，加速训练收敛
- 在 `models.py` 中的DCN模型中使用

**BCE** - Binary Cross Entropy (二元交叉熵)
- 用于二分类任务的损失函数
- 在训练脚本中作为主要损失函数

## C

**C1-C4 Architecture** - 架构层次模型
- C1: System Context (系统上下文)
- C2: Container (容器)
- C3: Component (组件)
- C4: Code (代码)

**Chunk** - 数据块
- 并行处理时的数据分片单位
- 默认chunk_size根据实例类型动态调整

**Click-Through Rate (CTR)** - 点击率
- 推荐系统的核心预测目标
- 本系统主要预测用户是否会点击推荐内容

**Config** - 配置
- 系统配置中心，位于 `config.py`
- 包含数据路径、并行参数、模型参数等

**Cross Feature** - 交叉特征
- 两个或多个原始特征组合生成的新特征
- DCN模型专门处理特征交叉

## D

**DataAnalyzer** - 数据分析器
- 分析原始数据统计特性的组件
- 位于 `data_analyzer.py`

**DataLoader** - 数据加载器
- PyTorch中用于批量加载数据的工具
- 支持并行加载和预处理

**DCN** - Deep & Cross Network (深度交叉网络)
- 专门设计用于特征交叉的深度学习模型
- 包含DCNv1和DCNv2两个版本

**DLRM** - Deep Learning Recommendation Model (深度学习推荐模型)
- Facebook开发的推荐系统模型架构
- 在 `models.py` 中实现

**Dropout** - 随机失活
- 训练时随机关闭部分神经元，防止过拟合
- 默认dropout_rate为0.1

## E

**Embedding** - 嵌入
- 将离散特征映射到连续向量空间的技术
- 用于处理类别特征

**Epoch** - 训练轮次
- 完整遍历一次训练数据集
- 默认训练10-20个epochs

## F

**Feature Engineering** - 特征工程
- 从原始数据中提取和构造特征的过程
- 由 `FeatureManager` 管理

**Feature Group** - 特征组
- 相关特征的逻辑分组
- 包括：user、item、context、click、session、time、income、noise等

**Feature Metadata** - 特征元数据
- 描述特征属性的数据
- 存储在 `feature_metadata_expanded.json`

## G

**GIL** - Global Interpreter Lock (全局解释器锁)
- Python的线程限制机制
- 是选择多进程而非多线程的主要原因

**Gradient** - 梯度
- 损失函数对参数的导数
- 用于更新模型权重

**GradientMonitor** - 梯度监控器
- 监控训练过程中梯度变化的组件
- 位于 `gradient_monitor.py`

**GPU** - Graphics Processing Unit (图形处理单元)
- 用于加速深度学习计算
- 支持CUDA加速

## H

**Hyperparameter** - 超参数
- 模型训练前需要设置的参数
- 如学习率、批大小、层数等

## I

**Imbalanced Dataset** - 不平衡数据集
- 正负样本比例严重失衡的数据集
- 本系统中正样本约占10%

**INPUT_SIZE** - 输入维度
- 模型输入特征的维度
- 默认为177维

## L

**Learning Rate** - 学习率
- 控制模型参数更新步长
- 默认为0.001

**Loss Function** - 损失函数
- 衡量模型预测与真实值差距的函数
- 使用BCEWithLogitsLoss

## M

**Memory Mapping** - 内存映射
- 将文件映射到内存地址空间的技术
- 用于处理大文件

**MLflow** - ML实验管理平台
- 用于追踪实验、记录参数和指标
- 配置在 `mlflow_config.py`

**MLP** - Multi-Layer Perceptron (多层感知器)
- 最基础的全连接神经网络
- 作为baseline模型

**Multiprocessing** - 多进程
- Python的并行处理机制
- 用于数据预处理的并行化

## N

**Normalization** - 标准化
- 将数据缩放到标准范围的过程
- 使用StandardScaler进行特征标准化

**NPY** - NumPy格式文件
- 用于存储预处理后的特征和标签
- 比文本格式更高效

## O

**Optimizer** - 优化器
- 更新模型参数的算法
- 使用Adam优化器

## P

**ParallelProcessor** - 并行处理器
- 核心的多进程数据处理组件
- 位于 `parallel_processor.py`

**Parquet** - 列式存储格式
- Apache开发的高效数据存储格式
- 原始数据的主要格式

**pos_weight** - 正样本权重
- 处理类别不平衡的权重参数
- 有balanced、sqrt_balanced、log_balanced三种策略

**Preprocessing** - 预处理
- 数据清洗和转换的过程
- 由 `IntelligentPreprocessor` 执行

## R

**Recall** - 召回率
- 正确预测为正的样本占所有正样本的比例
- 推荐系统的重要指标

**Regularization** - 正则化
- 防止过拟合的技术
- 包括L1、L2正则和Dropout

## S

**S3** - Simple Storage Service
- Amazon的对象存储服务
- 用于存储原始数据

**Scheduler** - 调度器
- 动态调整学习率的组件
- 使用ReduceLROnPlateau策略

**StandardScaler** - 标准化器
- sklearn的数据标准化工具
- 将特征缩放到均值0、方差1

## T

**Tensor** - 张量
- PyTorch的多维数组数据结构
- 所有模型输入输出都是张量

**Threading** - 多线程
- Python的并发机制
- 因GIL限制，不适合CPU密集型任务

**Throughput** - 吞吐量
- 单位时间处理的数据量
- 关键性能指标，约200K样本/秒

**Track-only Mode** - 仅追踪模式
- 只记录处理过程，不实际处理数据
- 用于调试和性能分析

## V

**Validation** - 验证
- 评估模型性能的过程
- 使用独立的验证集

## W

**Weight Initialization** - 权重初始化
- 设置模型初始权重的策略
- 使用Xavier或He初始化

**Worker** - 工作进程
- 并行处理中的独立进程
- 默认88个workers（r5.24xlarge）

**Workflow** - 工作流
- 完整的处理流程
- 包括数据处理、训练、评估等步骤

## X

**Xavier Initialization** - Xavier初始化
- 一种权重初始化方法
- 适用于tanh和sigmoid激活函数

## 缩写对照表

| 缩写 | 全称 | 中文 |
|------|------|------|
| AUC | Area Under Curve | 曲线下面积 |
| BCE | Binary Cross Entropy | 二元交叉熵 |
| CTR | Click-Through Rate | 点击率 |
| DCN | Deep & Cross Network | 深度交叉网络 |
| DLRM | Deep Learning Recommendation Model | 深度学习推荐模型 |
| GIL | Global Interpreter Lock | 全局解释器锁 |
| GPU | Graphics Processing Unit | 图形处理单元 |
| MLP | Multi-Layer Perceptron | 多层感知器 |
| NPY | NumPy Format | NumPy格式 |
| S3 | Simple Storage Service | 简单存储服务 |

**Why — 设计动机与取舍**

术语表的设计遵循字母顺序排列，便于快速查找。每个术语都包含简要定义和在项目中的具体应用。缩写对照表单独列出，方便新手快速理解文档中的专业术语。选择中英文对照的方式，既保持了技术术语的准确性，又照顾了中文读者的理解需求。