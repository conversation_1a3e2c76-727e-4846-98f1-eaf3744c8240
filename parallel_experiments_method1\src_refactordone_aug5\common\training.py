"""Common training utilities."""
import torch
import torch.nn as nn
import numpy as np
import random
import logging
import time
from sklearn.metrics import roc_auc_score, average_precision_score
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)

def setup_device(use_cuda=True):
    """Setup and return the appropriate device for training."""
    if use_cuda and torch.cuda.is_available():
        device = torch.device("cuda")
        logger.info(f"使用设备: {device}")
        logger.info(f"GPU: {torch.cuda.get_device_name(0)}")
    else:
        device = torch.device("cpu")
        logger.info(f"使用设备: {device}")
    
    return device

def seed_all(seed=42, deterministic=True):
    """Set random seeds for reproducibility."""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
        
    if deterministic:
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False

def training_loop(model, train_loader, val_loader, optimizer, criterion, scheduler,
                  epochs: int, device, amp: bool = False, 
                  gradient_monitor = None) -> Dict[str, Any]:
    """Main training loop extracted from train_loss_optimized.py.
    
    Returns dict with training history and metadata for caller to log.
    """
    best_val_auc = -float('inf')
    best_val_loss = float('inf')
    best_model_state = None
    patience = 3  # Match original implementation
    patience_counter = 0
    
    history = {
        'train_losses': [],
        'val_losses': [],
        'val_aucs': [],
        'epochs_completed': 0,
        'best_val_loss': best_val_loss,
        'best_val_auc': best_val_auc
    }
    
    for epoch in range(epochs):
        start_time = time.time()
        
        # Training phase
        model.train()
        total_train_loss = 0
        num_batches = 0
        
        # Create GradScaler for mixed precision
        scaler = torch.cuda.amp.GradScaler(enabled=amp)
        
        for features, labels in train_loader:
            features, labels = features.to(device), labels.to(device)
            
            optimizer.zero_grad()
            
            # Mixed precision training
            with torch.cuda.amp.autocast(enabled=amp):
                outputs = model(features)
                loss = criterion(outputs, labels.unsqueeze(1))
            
            # Backward pass
            if amp:
                scaler.scale(loss).backward()
                scaler.unscale_(optimizer)
            else:
                loss.backward()
                
            # Gradient clipping
            grad_norm = torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
            
            # Gradient monitoring
            if gradient_monitor:
                gradient_monitor.log_gradients(loss)
            
            # Check for abnormal gradients
            if grad_norm > 10.0:
                logger.warning(f"⚠️ 检测到大梯度：{grad_norm:.4f}")
            elif grad_norm < 1e-6:
                logger.warning(f"⚠️ 检测到梯度消失：{grad_norm:.4e}")
            
            # Optimizer step
            if amp:
                scaler.step(optimizer)
                scaler.update()
            else:
                optimizer.step()
            
            scheduler.step()  # OneCycleLR needs step after each batch
            
            total_train_loss += loss.item()
            num_batches += 1
            
            # Log progress every 1000 batches
            if num_batches % 1000 == 0:
                current_lr = optimizer.param_groups[0]['lr']
                logger.info(f"  Batch {num_batches}: Loss={loss.item():.4f}, Grad Norm={grad_norm:.4f}, LR={current_lr:.6f}")
                
                # Check logits distribution
                with torch.no_grad():
                    logits = outputs.detach()
                    logger.info(f"    Logits: mean={logits.mean().item():.4f}, std={logits.std().item():.4f}, "
                              f"min={logits.min().item():.4f}, max={logits.max().item():.4f}")
                
                # GPU memory monitoring
                if device.type == 'cuda':
                    allocated = torch.cuda.memory_allocated(device) / 1e9
                    reserved = torch.cuda.memory_reserved(device) / 1e9
                    logger.info(f"    GPU内存: 已分配={allocated:.2f}GB, 已预留={reserved:.2f}GB")
        
        avg_train_loss = total_train_loss / num_batches
        
        # Validation phase
        val_metrics = evaluate_epoch(model, val_loader, device, criterion)
        val_loss = val_metrics['loss']
        val_roc_auc = val_metrics['roc_auc']
        val_pr_auc = val_metrics['pr_auc']
        
        # GPU memory cleanup
        if device.type == 'cuda' and (epoch + 1) % 5 == 0:
            torch.cuda.empty_cache()
        
        # Save best model
        if val_roc_auc > best_val_auc + 0.0001:
            best_val_auc = val_roc_auc
            best_val_loss = val_loss
            patience_counter = 0
            # Save model state
            if isinstance(model, nn.DataParallel):
                best_model_state = model.module.state_dict().copy()
            else:
                best_model_state = model.state_dict().copy()
        else:
            patience_counter += 1
            if patience_counter >= patience:
                # Restore best model
                if best_model_state is not None:
                    if isinstance(model, nn.DataParallel):
                        model.module.load_state_dict(best_model_state)
                    else:
                        model.load_state_dict(best_model_state)
                break
        
        epoch_time = time.time() - start_time
        
        # Store history
        history['train_losses'].append(avg_train_loss)
        history['val_losses'].append(val_loss)
        history['val_aucs'].append(val_roc_auc)
        history['epochs_completed'] = epoch + 1
        history['best_val_loss'] = best_val_loss
        history['best_val_auc'] = best_val_auc
        
        # Return info for caller to log
        history['last_epoch_info'] = {
            'epoch': epoch + 1,
            'total_epochs': epochs,
            'avg_train_loss': avg_train_loss,
            'val_loss': val_loss,
            'val_roc_auc': val_roc_auc,
            'val_pr_auc': val_pr_auc,
            'epoch_time': epoch_time,
            'early_stopped': patience_counter >= patience
        }
    
    return history

def evaluate_epoch(model, data_loader, device, criterion) -> Dict[str, float]:
    """Evaluate model on a dataset, returning metrics dict."""
    model.eval()
    total_loss = 0
    all_predictions = []
    all_labels = []
    
    with torch.no_grad():
        for features, labels in data_loader:
            features, labels = features.to(device), labels.to(device)
            outputs = model(features)
            loss = criterion(outputs, labels.unsqueeze(1))
            total_loss += loss.item()
            
            # Collect predictions
            probs = torch.sigmoid(outputs).cpu().numpy().flatten()
            labels_np = labels.cpu().numpy().flatten()
            
            all_predictions.extend(probs)
            all_labels.extend(labels_np)
    
    avg_loss = total_loss / len(data_loader)
    
    # Convert to arrays
    all_predictions = np.array(all_predictions)
    all_labels = np.array(all_labels)
    
    # Calculate metrics
    try:
        roc_auc = roc_auc_score(all_labels, all_predictions)
    except ValueError:
        roc_auc = 0.5
    
    try:
        pr_auc = average_precision_score(all_labels, all_predictions)
    except ValueError:
        pr_auc = 0.5
    
    return {
        'loss': avg_loss,
        'roc_auc': roc_auc,
        'pr_auc': pr_auc
    }