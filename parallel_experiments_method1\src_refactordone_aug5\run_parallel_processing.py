# run_parallel_processing.py
"""
并行处理主运行脚本
用户只需修改config.py中的三个数据路径即可运行
"""

import os
import sys
import logging
import argparse
from pathlib import Path

# 导入配置和处理器
from config import (
    TRAIN_DATA_DIR, VALIDATION_DATA_DIR, TEST_DATA_DIR,
    PARALLEL_CONFIG, PARALLEL_EXTENDED_CONFIG
)
from parallel_processor import ParallelProcessor
from data_analyzer import DataAnalyzer

# 导入通用I/O模块
from common import io as common_io

# 导入进程跟踪器
from minimal_logger import track_request, track_response, track_stats, set_track_only_mode, set_normal_mode

# 设置原始日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_data_paths():
    """检查数据路径配置"""
    logger.info("检查数据路径配置...")
    
    paths = {
        'train': TRAIN_DATA_DIR,
        'validation': VALIDATION_DATA_DIR,
        'test': TEST_DATA_DIR
    }
    
    valid_paths = {}
    for name, path in paths.items():
        if path and path != r"C:\path\to\your\train\data" and path != r"C:\path\to\your\validation\data" and path != r"C:\path\to\your\test\data":
            valid_paths[name] = path
            logger.info(f"[OK] {name} 数据路径: {path}")
        else:
            logger.warning(f"[WARNING] {name} 数据路径未配置或使用默认值")
            
    if not valid_paths:
        logger.error("[ERROR] 没有配置有效的数据路径！")
        logger.error("请在 config.py 中修改以下三行:")
        logger.error("  TRAIN_DATA_DIR = 'your/train/data/path'")
        logger.error("  VALIDATION_DATA_DIR = 'your/validation/data/path'")
        logger.error("  TEST_DATA_DIR = 'your/test/data/path'")
        return False
        
    return valid_paths

def run_data_analysis():
    """运行数据分析"""
    logger.info("="*60)
    logger.info("步骤 1: 数据分析")
    logger.info("="*60)
    
    try:
        analyzer = DataAnalyzer()
        analysis_results = analyzer.analyze_all_datasets()
        analyzer.save_analysis(analysis_results)
        logger.info("[OK] 数据分析完成")
        return True
    except Exception as e:
        logger.error(f"[ERROR] 数据分析失败: {e}")
        return False

def run_parallel_processing(max_workers=None):
    """运行并行处理"""
    logger.info("="*60)
    logger.info("步骤 2: 并行数据处理")
    logger.info("="*60)
    track_request("PARALLEL_PROCESSING")

    try:
        # 创建并行处理器
        track_request("PROCESSOR_CREATE")
        processor = ParallelProcessor(max_workers=max_workers)
        track_response("PROCESSOR_CREATE", "SUCCESS")

        # 处理所有数据集
        datasets = {
            'train': TRAIN_DATA_DIR,
            'validation': VALIDATION_DATA_DIR,
            'test': TEST_DATA_DIR
        }

        success_count = 0
        total_count = 0

        for dataset_name, data_dir in datasets.items():
            if data_dir and data_dir not in [r"C:\path\to\your\train\data", r"C:\path\to\your\validation\data", r"C:\path\to\your\test\data"]:
                total_count += 1
                logger.info(f"处理 {dataset_name} 数据集...")

                # 跟踪数据集处理
                track_request(f"DATASET_{dataset_name.upper()}")

                success = processor.process_dataset(data_dir, dataset_name)
                if success:
                    success_count += 1
                    track_response(f"DATASET_{dataset_name.upper()}", "SUCCESS")
                    logger.info(f"[OK] {dataset_name} 数据集处理成功")
                else:
                    track_response(f"DATASET_{dataset_name.upper()}", "FAILED")
                    logger.error(f"[ERROR] {dataset_name} 数据集处理失败")
            else:
                logger.warning(f"[WARNING] 跳过 {dataset_name} 数据集 (路径未配置)")

        if success_count == total_count and total_count > 0:
            track_response("PARALLEL_PROCESSING", f"SUCCESS_{success_count}/{total_count}")
            logger.info("[OK] 所有数据集处理完成")
            return True
        else:
            track_response("PARALLEL_PROCESSING", f"PARTIAL_{success_count}/{total_count}")
            logger.error(f"[ERROR] 处理完成，但有失败: {success_count}/{total_count} 成功")
            return False
            
    except Exception as e:
        logger.error(f"[ERROR] 并行处理失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='并行数据处理系统')
    parser.add_argument('--workers', type=int, default=None,
                       help='工作进程数 (默认: CPU核心数-2)')
    parser.add_argument('--skip-analysis', action='store_true',
                       help='跳过数据分析步骤')
    parser.add_argument('--analysis-only', action='store_true',
                       help='只运行数据分析')
    parser.add_argument('--track-only', action='store_true',
                       help='只显示TRACK级别日志，隐藏INFO/DEBUG等详细日志')

    args = parser.parse_args()

    # 根据参数设置日志级别
    if args.track_only:
        set_track_only_mode()
    else:
        set_normal_mode()
    
    logger.info("="*80)
    logger.info("[START] 并行数据处理系统启动")
    logger.info("="*80)
    
    # 检查数据路径配置
    valid_paths = check_data_paths()
    if not valid_paths:
        return False
        
    # 显示配置信息
    logger.info(f"配置信息:")
    logger.info(f"  最大Worker数: {args.workers or PARALLEL_CONFIG['max_workers']}")
    logger.info(f"  启动方法: {PARALLEL_CONFIG['multiprocessing_start_method']}")
    logger.info(f"  内存限制: {PARALLEL_CONFIG['memory_limit_gb']}GB")
    logger.info(f"  数据集数量: {len(valid_paths)}")
    
    success = True
    
    # 步骤1: 数据分析
    if not args.skip_analysis:
        analysis_success = run_data_analysis()
        if not analysis_success:
            logger.error("数据分析失败，无法继续")
            return False
            
    if args.analysis_only:
        logger.info("[OK] 仅数据分析模式完成")
        return True
        
    # 步骤2: 并行处理
    processing_success = run_parallel_processing(max_workers=args.workers)
    
    # 总结
    logger.info("="*80)
    if processing_success:
        logger.info("[SUCCESS] 并行数据处理系统运行成功!")
        logger.info("处理后的数据保存在 processed_data/ 目录下")
        logger.info("可以使用以下文件进行模型训练:")
        
        processed_dir = PARALLEL_EXTENDED_CONFIG['processed_data_dir']
        for dataset_name in valid_paths.keys():
            feature_file = common_io.join_path(processed_dir, f"{dataset_name}_features.npy")
            label_file = common_io.join_path(processed_dir, f"{dataset_name}_labels.npy")
            if common_io.path_exists(feature_file) and common_io.path_exists(label_file):
                logger.info(f"  {dataset_name}: {feature_file}, {label_file}")
        
        # Check if metadata was generated
        metadata_file = common_io.join_path(processed_dir, "feature_metadata_expanded.json")
        if common_io.path_exists(metadata_file):
            logger.info("")
            logger.info("[OK] 特征元数据已自动生成: feature_metadata_expanded.json")
            logger.info("   (包含特征索引、分组和展开信息)")
                
    else:
        logger.error("[ERROR] 并行数据处理系统运行失败")
        logger.error("请检查日志信息并修复问题后重试")
        
    logger.info("="*80)

    # 输出详细的进程统计信息
    print("\n" + "="*60)
    print("[STATS] 详细进程统计信息:")
    print("="*60)
    track_stats()
    print("="*60)

    return processing_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
