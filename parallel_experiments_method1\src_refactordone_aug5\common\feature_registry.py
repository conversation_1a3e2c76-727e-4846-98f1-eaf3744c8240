"""Unified feature metadata and grouping utilities."""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field


@dataclass(frozen=True)
class FeatureSpec:
    """Feature specification from data analysis."""
    numeric_columns: List[str] = field(default_factory=list)
    categorical_columns: List[str] = field(default_factory=list)
    array_columns: List[str] = field(default_factory=list) 
    array_dimensions: Dict[str, int] = field(default_factory=dict)
    label_column: Optional[str] = None


def extract_prefix(column_name: str) -> str:
    """Extract column prefix (group name).
    
    Examples:
        "user_embedding" -> "user"
        "click_position" -> "click"
        "age" -> ""
    """
    if '_' in column_name:
        return column_name.split('_')[0]
    return ''


def build_feature_spec(
    *,
    numeric_columns: List[str],
    categorical_columns: List[str],
    array_columns: List[str],
    array_dimensions: Dict[str, int],
    label_column: Optional[str] = None
) -> FeatureSpec:
    """Build feature specification.
    
    Args:
        numeric_columns: List of numeric column names
        categorical_columns: List of categorical column names
        array_columns: List of array column names
        array_dimensions: Mapping of array columns to their dimensions
        label_column: Optional label column name
        
    Returns:
        FeatureSpec object
    """
    return FeatureSpec(
        numeric_columns=numeric_columns,
        categorical_columns=categorical_columns,
        array_columns=array_columns,
        array_dimensions=array_dimensions,
        label_column=label_column
    )


def group_columns(
    columns: List[str],
    spec: FeatureSpec = None,
    *,
    prefix_rules: Optional[Dict[str, str]] = None
) -> Dict[str, List[str]]:
    """Group columns by prefix.
    
    Args:
        columns: List of column names
        spec: Feature specification (unused, for compatibility)
        prefix_rules: Optional prefix override rules
        
    Returns:
        Dict mapping group names to column lists
    """
    groups = {}
    for col in columns:
        if prefix_rules and col in prefix_rules:
            prefix = prefix_rules[col]
        else:
            prefix = extract_prefix(col)
        
        if prefix not in groups:
            groups[prefix] = []
        groups[prefix].append(col)
    
    return groups


def build_metadata(
    spec: FeatureSpec,
    groups: Optional[Dict[str, List[str]]] = None,
    df_sample: Any = None
) -> Dict[str, Any]:
    """Build complete metadata structure matching current JSON schema.
    
    Args:
        spec: Feature specification
        groups: Optional pre-computed column groupings
        df_sample: Optional sample dataframe (unused)
        
    Returns:
        Metadata dict matching current JSON schema exactly
    """
    metadata = {
        'features': [],
        'groups': {},
        'total_features': 0
    }
    
    feature_index = 0
    
    # Process numeric columns
    for col in spec.numeric_columns:
        if col != spec.label_column:
            prefix = extract_prefix(col)
            metadata['features'].append({
                'index': feature_index,
                'name': col,
                'type': 'numeric',
                'group': prefix,
                'original_column': col
            })
            if prefix not in metadata['groups']:
                metadata['groups'][prefix] = []
            metadata['groups'][prefix].append(feature_index)
            feature_index += 1
    
    # Process categorical columns  
    for col in spec.categorical_columns:
        if col != spec.label_column:
            prefix = extract_prefix(col)
            metadata['features'].append({
                'index': feature_index,
                'name': col,
                'type': 'categorical',
                'group': prefix,
                'original_column': col
            })
            if prefix not in metadata['groups']:
                metadata['groups'][prefix] = []
            metadata['groups'][prefix].append(feature_index)
            feature_index += 1
    
    # Process array columns with expansion
    for col in spec.array_columns:
        array_dim = spec.array_dimensions.get(col, 0)
        prefix = extract_prefix(col)
        
        if prefix not in metadata['groups']:
            metadata['groups'][prefix] = []
            
        for i in range(array_dim):
            metadata['features'].append({
                'index': feature_index,
                'name': f'{col}_{i}',
                'type': 'array_element',
                'group': prefix,
                'original_column': col,
                'array_index': i
            })
            metadata['groups'][prefix].append(feature_index)
            feature_index += 1
    
    metadata['total_features'] = feature_index
    return metadata