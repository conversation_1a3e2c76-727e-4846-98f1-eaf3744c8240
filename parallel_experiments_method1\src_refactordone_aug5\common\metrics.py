"""Common metrics utilities."""
import torch
import torch.nn as nn
import numpy as np
import logging

logger = logging.getLogger(__name__)

def analyze_data_distribution(labels, dataset_name=""):
    """分析数据分布"""
    labels_np = labels.numpy() if isinstance(labels, torch.Tensor) else labels
    pos_count = np.sum(labels_np)
    neg_count = len(labels_np) - pos_count
    pos_ratio = pos_count / len(labels_np)
    
    logger.info(f"{dataset_name} 数据分布:")
    logger.info(f"  总样本数: {len(labels_np):,}")
    logger.info(f"  正样本数: {pos_count:,} ({pos_ratio:.4f})")
    logger.info(f"  负样本数: {neg_count:,} ({1-pos_ratio:.4f})")
    logger.info(f"  不平衡比例: {neg_count/pos_count if pos_count > 0 else float('inf'):.2f}:1")
    
    return pos_count, neg_count, pos_ratio

def calculate_pos_weight(y_train, strategy='balanced'):
    """计算最优的pos_weight"""
    pos_count, neg_count, pos_ratio = analyze_data_distribution(y_train, "训练集")
    
    if strategy == 'balanced':
        pos_weight_value = neg_count / pos_count if pos_count > 0 else 1.0
    elif strategy == 'sqrt_balanced':
        ratio = neg_count / pos_count if pos_count > 0 else 1.0
        pos_weight_value = np.sqrt(ratio)
    elif strategy == 'log_balanced':
        ratio = neg_count / pos_count if pos_count > 0 else 1.0
        pos_weight_value = np.log(1 + ratio)
    else:
        pos_weight_value = 1.0
    
    pos_weight_value = np.clip(pos_weight_value, 1.0, 5.0)
    
    logger.info(f"pos_weight计算策略: {strategy}")
    logger.info(f"计算得到的pos_weight: {pos_weight_value:.3f}")
    
    return pos_weight_value

def build_bce_with_logits(pos_weight, device):
    """Build BCEWithLogitsLoss with proper device placement."""
    if isinstance(pos_weight, (int, float)):
        pos_weight = torch.tensor([pos_weight], dtype=torch.float32)
    else:
        pos_weight = torch.as_tensor(pos_weight, dtype=torch.float32)
    
    pos_weight = pos_weight.to(device)
    return nn.BCEWithLogitsLoss(pos_weight=pos_weight)