# 内存管理架构详细分析

## 概述

本文档详细说明推荐系统中的内存管理策略，包括在数据预处理、特征工程、模型训练等各个阶段如何有效管理和优化内存使用。

## 1. 内存管理的必要性

### 1.1 面临的挑战

推荐系统处理的数据规模巨大：
- **用户数量**：可能达到百万或千万级别
- **物品数量**：百万级别的商品或内容
- **交互记录**：数十亿条用户行为记录
- **特征维度**：177个特征，包括高维稀疏特征和稠密特征
- **批处理需求**：需要同时处理大量样本

### 1.2 内存瓶颈出现的位置

1. **数据加载阶段**：原始数据文件可能达到几十GB
2. **特征工程阶段**：特征展开和交叉可能使数据量膨胀10倍以上
3. **预处理阶段**：中间结果需要大量临时存储
4. **训练阶段**：模型参数、梯度、优化器状态占用显存
5. **批次处理**：DataLoader需要预取和缓存数据

## 2. 内存管理策略层次

### 2.1 系统级内存管理

```python
import psutil
import gc
import os

class SystemMemoryManager:
    def __init__(self):
        self.total_memory = psutil.virtual_memory().total
        self.available_memory = psutil.virtual_memory().available
        self.memory_limit = self._calculate_safe_limit()
    
    def _calculate_safe_limit(self):
        """
        计算安全的内存使用上限
        保留20%的系统内存，避免系统崩溃
        """
        return int(self.total_memory * 0.8)
    
    def get_memory_limit(self):
        """
        获取当前可用的内存限制
        这个函数在多个地方被调用
        """
        current_usage = psutil.Process().memory_info().rss
        remaining = self.memory_limit - current_usage
        return max(remaining, 1024 * 1024 * 1024)  # 至少保留1GB
    
    def check_memory_availability(self, required_bytes):
        """
        检查是否有足够的内存执行操作
        """
        available = psutil.virtual_memory().available
        if available < required_bytes:
            # 触发垃圾回收
            gc.collect()
            available = psutil.virtual_memory().available
            
            if available < required_bytes:
                raise MemoryError(f"需要 {required_bytes / 1e9:.2f}GB 内存，"
                                f"但只有 {available / 1e9:.2f}GB 可用")
        return True
    
    def optimize_memory(self):
        """
        主动优化内存使用
        """
        # 强制垃圾回收
        gc.collect()
        
        # 清理Python对象缓存
        import sys
        sys.intern.clear() if hasattr(sys.intern, 'clear') else None
        
        # 返回释放的内存量
        return psutil.virtual_memory().available
```

## 3. 数据预处理阶段的内存管理

### 3.1 分块处理策略

```python
class ChunkedDataProcessor:
    def __init__(self, chunk_size=None):
        if chunk_size is None:
            # 根据可用内存动态决定块大小
            memory_limit = SystemMemoryManager().get_memory_limit()
            # 假设每条记录占用1KB，留50%余量
            self.chunk_size = int(memory_limit / 1024 / 2)
        else:
            self.chunk_size = chunk_size
    
    def process_large_file(self, input_file, output_file):
        """
        分块处理大文件，避免一次性加载到内存
        """
        processed_chunks = []
        
        # 使用迭代器逐块读取
        for chunk_idx, chunk_data in enumerate(self.read_chunks(input_file)):
            # 处理当前块
            processed = self.process_chunk(chunk_data)
            
            # 立即写入磁盘，释放内存
            self.save_chunk(processed, output_file, chunk_idx)
            
            # 清理内存
            del chunk_data
            del processed
            gc.collect()
            
            # 记录进度
            processed_chunks.append(chunk_idx)
            
        return processed_chunks
    
    def read_chunks(self, file_path):
        """
        生成器模式读取数据块
        """
        with open(file_path, 'r') as f:
            chunk = []
            for line in f:
                chunk.append(line)
                if len(chunk) >= self.chunk_size:
                    yield chunk
                    chunk = []
            if chunk:  # 处理最后一块
                yield chunk
```

### 3.2 内存映射文件（Memory-Mapped Files）

```python
import numpy as np
import h5py

class MemoryMappedDataHandler:
    def __init__(self, file_path):
        self.file_path = file_path
        
    def create_mmap_array(self, shape, dtype=np.float32):
        """
        创建内存映射数组，数据存储在磁盘上
        只有访问时才加载到内存
        """
        return np.memmap(
            self.file_path,
            dtype=dtype,
            mode='w+',
            shape=shape
        )
    
    def process_with_mmap(self, data_shape):
        """
        使用内存映射处理超大数组
        """
        # 创建内存映射数组
        mmap_array = self.create_mmap_array(data_shape)
        
        # 分块处理，每次只加载一部分到内存
        chunk_size = 10000
        for i in range(0, data_shape[0], chunk_size):
            end = min(i + chunk_size, data_shape[0])
            
            # 只有这一块会被加载到内存
            chunk = mmap_array[i:end]
            
            # 处理这一块
            processed = self.process_chunk_data(chunk)
            
            # 写回结果
            mmap_array[i:end] = processed
            
            # 刷新到磁盘
            if i % (chunk_size * 10) == 0:
                mmap_array.flush()
        
        return mmap_array
```

## 4. 特征工程中的内存优化

### 4.1 稀疏矩阵处理

```python
from scipy.sparse import csr_matrix, save_npz, load_npz

class SparseFeatureProcessor:
    def __init__(self):
        self.sparse_features = None
        self.dense_features = None
    
    def process_categorical_features(self, data):
        """
        将高维稀疏的类别特征转换为稀疏矩阵
        大幅减少内存使用
        """
        # 假设类别特征是one-hot编码，大部分为0
        # 使用稀疏矩阵可以节省90%以上的内存
        
        rows = []
        cols = []
        values = []
        
        for row_idx, record in enumerate(data):
            for col_idx, value in enumerate(record):
                if value != 0:  # 只存储非零值
                    rows.append(row_idx)
                    cols.append(col_idx)
                    values.append(value)
        
        # 创建稀疏矩阵
        sparse_matrix = csr_matrix(
            (values, (rows, cols)),
            shape=(len(data), len(data[0]))
        )
        
        # 内存占用对比
        dense_size = len(data) * len(data[0]) * 4  # float32
        sparse_size = len(values) * 12  # 索引和值
        compression_ratio = dense_size / sparse_size
        
        print(f"稀疏矩阵压缩率: {compression_ratio:.2f}x")
        
        return sparse_matrix
    
    def save_sparse_features(self, sparse_matrix, file_path):
        """
        保存稀疏矩阵到磁盘
        """
        save_npz(file_path, sparse_matrix)
    
    def load_sparse_features(self, file_path):
        """
        从磁盘加载稀疏矩阵
        """
        return load_npz(file_path)
```

### 4.2 特征哈希（Feature Hashing）

```python
class FeatureHasher:
    def __init__(self, n_features=2**20):
        """
        使用哈希技巧减少特征维度
        将高维特征映射到固定大小的空间
        """
        self.n_features = n_features
    
    def hash_features(self, features):
        """
        哈希特征到固定维度空间
        """
        import hashlib
        
        hashed_features = np.zeros(self.n_features)
        
        for feature_name, feature_value in features.items():
            # 计算特征的哈希值
            hash_value = int(hashlib.md5(
                feature_name.encode()
            ).hexdigest(), 16)
            
            # 映射到固定空间
            index = hash_value % self.n_features
            
            # 累加特征值（处理哈希冲突）
            hashed_features[index] += feature_value
        
        return hashed_features
```

## 5. DataLoader中的内存管理

### 5.1 数据预取和缓存策略

```python
import torch
from torch.utils.data import Dataset, DataLoader
import queue
import threading

class MemoryEfficientDataset(Dataset):
    def __init__(self, data_path, cache_size=1000):
        self.data_path = data_path
        self.cache_size = cache_size
        self.cache = {}
        self.cache_queue = queue.Queue(maxsize=cache_size)
        
        # 获取数据集大小而不加载全部数据
        self.length = self._get_dataset_length()
    
    def _get_dataset_length(self):
        """
        获取数据集大小，不加载数据
        """
        with open(f"{self.data_path}/metadata.json", 'r') as f:
            metadata = json.load(f)
        return metadata['total_samples']
    
    def __len__(self):
        return self.length
    
    def __getitem__(self, idx):
        """
        延迟加载策略：只在需要时加载数据
        """
        # 检查缓存
        if idx in self.cache:
            return self.cache[idx]
        
        # 从磁盘加载
        data = self._load_sample(idx)
        
        # 更新缓存（LRU策略）
        self._update_cache(idx, data)
        
        return data
    
    def _load_sample(self, idx):
        """
        从磁盘加载单个样本
        """
        # 使用内存映射文件快速访问
        sample_file = f"{self.data_path}/sample_{idx}.npz"
        with np.load(sample_file, mmap_mode='r') as data:
            features = data['features']
            label = data['label']
        
        return {'features': features, 'label': label}
    
    def _update_cache(self, idx, data):
        """
        LRU缓存更新策略
        """
        if self.cache_queue.full():
            # 移除最老的缓存项
            old_idx = self.cache_queue.get()
            del self.cache[old_idx]
        
        # 添加新缓存项
        self.cache[idx] = data
        self.cache_queue.put(idx)

class MemoryEfficientDataLoader:
    def __init__(self, dataset, batch_size, num_workers=4):
        self.dataset = dataset
        self.batch_size = batch_size
        
        # 根据可用内存调整预取数量
        memory_limit = SystemMemoryManager().get_memory_limit()
        samples_per_gb = 1000  # 估计值
        max_prefetch = int(memory_limit / 1e9 * samples_per_gb)
        
        # 创建优化的DataLoader
        self.loader = DataLoader(
            dataset,
            batch_size=batch_size,
            num_workers=num_workers,
            pin_memory=True,  # 使用页锁定内存加速GPU传输
            prefetch_factor=min(2, max_prefetch // batch_size),
            persistent_workers=True  # 保持工作进程活跃
        )
    
    def get_loader(self):
        return self.loader
```

### 5.2 动态批大小调整

```python
class AdaptiveBatchSizeManager:
    def __init__(self, initial_batch_size=32):
        self.batch_size = initial_batch_size
        self.memory_usage_history = []
        self.oom_count = 0
    
    def adjust_batch_size(self, memory_usage, oom_error=False):
        """
        根据内存使用情况动态调整批大小
        """
        if oom_error:
            # 内存溢出，减小批大小
            self.batch_size = max(1, self.batch_size // 2)
            self.oom_count += 1
            print(f"OOM错误，批大小减小到: {self.batch_size}")
            return self.batch_size
        
        # 记录内存使用
        self.memory_usage_history.append(memory_usage)
        
        # 计算内存使用趋势
        if len(self.memory_usage_history) >= 10:
            recent_usage = np.mean(self.memory_usage_history[-10:])
            memory_limit = SystemMemoryManager().get_memory_limit()
            
            usage_ratio = recent_usage / memory_limit
            
            if usage_ratio < 0.5:
                # 内存使用率低，可以增大批大小
                new_batch_size = min(self.batch_size * 2, 512)
                if new_batch_size > self.batch_size:
                    print(f"内存充足，批大小增加到: {new_batch_size}")
                    self.batch_size = new_batch_size
            elif usage_ratio > 0.9:
                # 内存使用率高，减小批大小
                new_batch_size = max(self.batch_size // 2, 1)
                if new_batch_size < self.batch_size:
                    print(f"内存紧张，批大小减小到: {new_batch_size}")
                    self.batch_size = new_batch_size
        
        return self.batch_size
```

## 6. 模型训练中的内存优化

### 6.1 梯度累积

```python
class GradientAccumulationTrainer:
    def __init__(self, model, accumulation_steps=4):
        self.model = model
        self.accumulation_steps = accumulation_steps
    
    def train_with_accumulation(self, dataloader, optimizer):
        """
        使用梯度累积来模拟大批量训练
        减少内存占用
        """
        self.model.train()
        
        for batch_idx, batch in enumerate(dataloader):
            # 前向传播
            outputs = self.model(batch['features'])
            loss = self.compute_loss(outputs, batch['labels'])
            
            # 标准化损失（因为要累积）
            loss = loss / self.accumulation_steps
            
            # 反向传播
            loss.backward()
            
            # 只在累积足够步数后更新参数
            if (batch_idx + 1) % self.accumulation_steps == 0:
                optimizer.step()
                optimizer.zero_grad()
                
                # 清理中间变量
                torch.cuda.empty_cache() if torch.cuda.is_available() else None
```

### 6.2 混合精度训练

```python
from torch.cuda.amp import autocast, GradScaler

class MixedPrecisionTrainer:
    def __init__(self, model):
        self.model = model
        self.scaler = GradScaler()
    
    def train_with_amp(self, dataloader, optimizer):
        """
        使用自动混合精度训练
        减少50%的显存使用
        """
        for batch in dataloader:
            optimizer.zero_grad()
            
            # 自动混合精度上下文
            with autocast():
                outputs = self.model(batch['features'])
                loss = self.compute_loss(outputs, batch['labels'])
            
            # 梯度缩放
            self.scaler.scale(loss).backward()
            self.scaler.step(optimizer)
            self.scaler.update()
```

## 7. 内存监控和诊断

### 7.1 实时内存监控

```python
import tracemalloc
import logging

class MemoryMonitor:
    def __init__(self, alert_threshold=0.9):
        self.alert_threshold = alert_threshold
        self.logger = logging.getLogger(__name__)
        tracemalloc.start()
    
    def monitor_memory_usage(self):
        """
        监控内存使用并发出警告
        """
        current, peak = tracemalloc.get_traced_memory()
        
        # 系统内存
        system_memory = psutil.virtual_memory()
        usage_percent = system_memory.percent / 100
        
        if usage_percent > self.alert_threshold:
            self.logger.warning(
                f"内存使用率过高: {usage_percent:.1%}\n"
                f"当前Python内存: {current / 1e9:.2f}GB\n"
                f"峰值Python内存: {peak / 1e9:.2f}GB"
            )
            
            # 获取内存使用最多的代码位置
            snapshot = tracemalloc.take_snapshot()
            top_stats = snapshot.statistics('lineno')[:10]
            
            self.logger.info("内存使用TOP 10:")
            for stat in top_stats:
                self.logger.info(f"{stat}")
        
        return current, peak, usage_percent
    
    def profile_function_memory(self, func):
        """
        分析函数的内存使用
        """
        def wrapper(*args, **kwargs):
            snapshot_before = tracemalloc.take_snapshot()
            
            result = func(*args, **kwargs)
            
            snapshot_after = tracemalloc.take_snapshot()
            stats = snapshot_after.compare_to(snapshot_before, 'lineno')
            
            self.logger.info(f"函数 {func.__name__} 内存使用变化:")
            for stat in stats[:10]:
                self.logger.info(f"{stat}")
            
            return result
        
        return wrapper
```

## 8. 内存优化最佳实践

### 8.1 数据类型优化

```python
def optimize_dtypes(df):
    """
    优化DataFrame的数据类型以减少内存使用
    """
    for col in df.columns:
        col_type = df[col].dtype
        
        if col_type != 'object':
            c_min = df[col].min()
            c_max = df[col].max()
            
            if str(col_type)[:3] == 'int':
                if c_min > np.iinfo(np.int8).min and c_max < np.iinfo(np.int8).max:
                    df[col] = df[col].astype(np.int8)
                elif c_min > np.iinfo(np.int16).min and c_max < np.iinfo(np.int16).max:
                    df[col] = df[col].astype(np.int16)
                elif c_min > np.iinfo(np.int32).min and c_max < np.iinfo(np.int32).max:
                    df[col] = df[col].astype(np.int32)
            else:
                if c_min > np.finfo(np.float16).min and c_max < np.finfo(np.float16).max:
                    df[col] = df[col].astype(np.float16)
                elif c_min > np.finfo(np.float32).min and c_max < np.finfo(np.float32).max:
                    df[col] = df[col].astype(np.float32)
    
    return df
```

## 9. 总结

内存管理贯穿整个推荐系统的生命周期：

1. **预处理阶段**：使用分块处理和内存映射文件
2. **特征工程**：使用稀疏矩阵和特征哈希
3. **数据加载**：使用延迟加载和智能缓存
4. **模型训练**：使用梯度累积和混合精度
5. **监控诊断**：实时监控和内存分析

通过这些策略，可以在有限的内存资源下处理大规模数据集，提高系统的可扩展性和稳定性。