# ADR-0003: 超参数调优策略

## 状态
已采纳

## 上下文
深度学习模型有众多超参数需要调整：
- 模型架构：层数、隐藏单元数、激活函数
- 训练过程：学习率、批大小、优化器
- 正则化：Dropout率、权重衰减、批标准化
- 特定参数：pos_weight策略、特征选择

需要在以下方面权衡：
- 调优效果 vs 计算成本
- 自动化 vs 手工调整
- 全局最优 vs 快速迭代

## 决策
采用**分层递进式调优策略**：
1. 第一层：固定合理默认值
2. 第二层：手工调整关键参数
3. 第三层：局部网格搜索
4. 不使用全自动调优（如贝叶斯优化）

## 原因

### 为什么不用自动调优？

1. **计算成本过高**
   - 完整网格搜索需要1000+次训练
   - 贝叶斯优化需要100+次迭代
   - 单次训练15-30分钟，总计需要数天

2. **收益递减**
   - 自动调优通常只能提升1-2% AUC
   - 大部分提升来自前5-10次调整
   - 边际收益不值得额外成本

3. **领域知识重要**
   - 推荐系统有特定模式
   - 人工经验往往更高效
   - 某些组合明显不合理

### 分层策略详解

#### 第一层：默认值（覆盖80%场景）
```python
DEFAULT_HYPERPARAMS = {
    # 模型架构
    'hidden_sizes': [256, 128],      # 经验最优
    'num_cross_layers': 3,            # DCN标准配置
    
    # 训练过程
    'learning_rate': 0.001,           # Adam默认值
    'batch_size': 1024,               # 平衡速度和收敛
    'epochs': 10,                     # 通常足够
    
    # 正则化
    'dropout_rate': 0.1,              # 轻度正则
    'weight_decay': 1e-5,             # 标准L2
    
    # 类别平衡
    'pos_weight_strategy': 'sqrt_balanced'  # 实践最佳
}
```

#### 第二层：手工调整（解决特定问题）
| 问题 | 调整参数 | 调整方向 |
|------|----------|----------|
| 过拟合 | dropout_rate | 0.1 → 0.3 |
| 欠拟合 | hidden_sizes | [256,128] → [512,256] |
| 收敛慢 | learning_rate | 0.001 → 0.01 |
| 震荡 | batch_size | 1024 → 2048 |
| 类别不平衡严重 | pos_weight_strategy | sqrt → balanced |

#### 第三层：局部网格搜索（精细调优）
```python
# 只对最影响性能的3-4个参数网格搜索
GRID_SEARCH_PARAMS = {
    'learning_rate': [0.0005, 0.001, 0.002],
    'dropout_rate': [0.1, 0.2, 0.3],
    'num_cross_layers': [2, 3, 4]
}
# 总共3×3×3=27次实验，可接受
```

## 实施方法

### 1. 快速基线
```bash
# 使用默认参数建立基线
python src/train_loss_optimized.py --model_type dcnv2
# 预期AUC: 0.75-0.76
```

### 2. 问题诊断
```python
# 分析训练曲线
def diagnose_training(history):
    if val_loss_increasing and train_loss_decreasing:
        return "过拟合"
    elif both_loss_high:
        return "欠拟合"
    elif loss_oscillating:
        return "学习率过大"
    elif loss_plateau:
        return "学习率过小"
```

### 3. 针对性调整
```bash
# 过拟合：增加正则化
python src/train_loss_optimized.py \
    --dropout_rate 0.3 \
    --weight_decay 1e-4

# 类别不平衡：调整策略
python src/train_loss_optimized.py \
    --pos_weight_strategy balanced
```

### 4. 局部优化
```python
# grid_search.py
for lr in [0.0005, 0.001, 0.002]:
    for dropout in [0.1, 0.2, 0.3]:
        run_experiment(lr=lr, dropout=dropout)
        log_to_mlflow()
```

## 关键超参数影响分析

### 实验结果（基于内部数据）
| 参数 | 变化范围 | AUC影响 | 训练时间影响 |
|------|----------|---------|--------------|
| pos_weight_strategy | sqrt→balanced | +2-3% | 无 |
| learning_rate | 0.001→0.01 | ±1% | -30% |
| hidden_sizes | [256,128]→[512,256] | +0.5% | +40% |
| dropout_rate | 0.1→0.3 | -0.5% | +5% |
| batch_size | 1024→4096 | ±0.2% | -20% |
| num_cross_layers | 3→5 | +0.3% | +15% |

### 最佳实践顺序
1. **优先调整**：pos_weight_strategy（影响最大）
2. **其次调整**：learning_rate（影响收敛速度）
3. **再次调整**：dropout_rate（控制过拟合）
4. **最后调整**：模型容量（hidden_sizes等）

## 后果

### 积极后果
- 调优时间从数天减少到数小时
- 保留了人工经验的价值
- 可解释性强，易于复现
- 计算资源使用可控

### 消极后果
- 可能错过全局最优
- 需要人工参与
- 依赖经验和直觉
- 不同数据集需要重新调整

## 监控和验证

### MLflow实验追踪
```python
# 记录所有超参数
mlflow.log_params({
    'model_type': 'dcnv2',
    'hidden_sizes': str(hidden_sizes),
    'learning_rate': learning_rate,
    'batch_size': batch_size,
    'dropout_rate': dropout_rate,
    'pos_weight_strategy': pos_weight_strategy,
    'num_cross_layers': num_cross_layers
})

# 记录关键指标
mlflow.log_metrics({
    'best_val_auc': best_val_auc,
    'final_train_loss': final_train_loss,
    'convergence_epoch': convergence_epoch,
    'training_time': training_time
})
```

### 超参数敏感性分析
```python
# 生成敏感性报告
def sensitivity_analysis(experiment_results):
    for param in HYPERPARAMS:
        impact = calculate_auc_variance(param)
        print(f"{param}: AUC变化 {impact:.3f}")
```

## 替代方案

### 未选方案1：贝叶斯优化
- 优点：理论上最优
- 缺点：实施复杂，调试困难
- 未选原因：收益不明显

### 未选方案2：随机搜索
- 优点：简单，覆盖面广
- 缺点：效率低，浪费资源
- 未选原因：不如手工+网格高效

### 未选方案3：进化算法
- 优点：可找到非凸最优
- 缺点：极其耗时
- 未选原因：过度工程化

## 工具支持

### 配置文件模板
```yaml
# hyperparams.yaml
default:
  model_type: dcnv2
  hidden_sizes: [256, 128]
  learning_rate: 0.001
  batch_size: 1024
  dropout_rate: 0.1
  pos_weight_strategy: sqrt_balanced

overfit:
  dropout_rate: 0.3
  weight_decay: 1e-4

underfit:
  hidden_sizes: [512, 256, 128]
  num_cross_layers: 4
```

### 自动实验脚本
```bash
# run_experiments.sh
#!/bin/bash

# 基线
python train.py --config default

# 过拟合场景
python train.py --config overfit

# 网格搜索
python grid_search.py --params lr,dropout
```

## 参考
- [1] Bergstra & Bengio, "Random Search for Hyper-Parameter Optimization", 2012
- [2] 内部调优报告 hyperparameter_study_2024.pdf
- [3] MLflow实验 experiment_id: hyperparameter_tuning