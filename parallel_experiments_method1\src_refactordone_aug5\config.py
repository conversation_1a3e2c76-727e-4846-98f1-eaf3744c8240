# config.py
"""
并行处理系统配置文件
用户只需修改三个数据路径即可运行
"""
import os
import platform

# --- 用户配置区域 (只需修改这三行) ---
# 数据源路径 - 支持本地路径和S3路径
# 直接复用原始系统的简单配置方式

# 🔧 请修改为你的实际S3数据路径
# TRAIN_DATA_DIR = os.getenv('TRAIN_DATA_DIR', "s3://your-bucket/data/train/")
# VALIDATION_DATA_DIR = os.getenv('VALIDATION_DATA_DIR', "s3://your-bucket/data/validation/")
# TEST_DATA_DIR = os.getenv('TEST_DATA_DIR', "s3://your-bucket/data/test/")

# 如果要使用本地测试数据，取消注释下面的行：
TRAIN_DATA_DIR = os.getenv('TRAIN_DATA_DIR', r"local_test_data\small\train")
VALIDATION_DATA_DIR = os.getenv('VALIDATION_DATA_DIR', r"local_test_data\small\validation")
TEST_DATA_DIR = os.getenv('TEST_DATA_DIR', r"local_test_data\small\test")

# S3路径示例（如果使用S3）:
# TRAIN_DATA_DIR = "s3://your-bucket/data/train/"
# VALIDATION_DATA_DIR = "s3://your-bucket/data/validation/"
# TEST_DATA_DIR = "s3://your-bucket/data/test/"

# --- 系统配置 (通常不需要修改) ---
BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # parallel_experiments根目录

# 输出路径
PROCESSED_DATA_DIR = os.path.join(BASE_DIR, "processed_data")
MODEL_OUTPUT_DIR = os.path.join(BASE_DIR, "model_outputs")
ANALYSIS_OUTPUT_DIR = os.path.join(BASE_DIR, "data_analysis")

# 数据配置
LABEL_COLUMN_NAME = "click"  # 标签列名称
EXCLUDE_COLUMNS = ["user_id", "item_id", "timestamp"]  # 排除的列

# 强制指定的列类型
FORCE_COLUMN_TYPES = {
    'numeric': ["conversion", "attribution", "click", "click_pos", "click_nb", "cost", "cpo", "time_since_last_click"],
    'categorical': ["cat1", "cat2", "cat3", "cat4", "cat5", "cat6", "cat7", "cat8", "cat9"],
    'array': [],  # 数组列将自动检测
}

# --- 平台和存储类型检测 ---
PLATFORM = platform.system().lower()
IS_UNIX = PLATFORM in ['linux', 'darwin']
IS_WINDOWS = PLATFORM == 'windows'

# 检测是否在EC2环境中运行
IS_EC2_DEPLOYMENT = os.getenv('IS_EC2_DEPLOYMENT', 'false').lower() == 'true'

def is_s3_path(path):
    """检测路径是否为S3路径"""
    return isinstance(path, str) and path.startswith('s3://')

def detect_storage_type():
    """检测存储类型：mock_s3, real_s3, 或 local"""
    if any(is_s3_path(path) for path in [TRAIN_DATA_DIR, VALIDATION_DATA_DIR, TEST_DATA_DIR]):
        if os.getenv('USE_MOCK_S3', 'false').lower() == 'true':
            return 'mock_s3'
        else:
            return 'real_s3'
    else:
        return 'local'

STORAGE_TYPE = detect_storage_type()
USE_S3 = STORAGE_TYPE in ['mock_s3', 'real_s3']

# --- S3配置 (直接复用原始系统的配置) ---
S3_CONFIG = {
    'key': None,  # AWS Access Key ID (如果不设置，使用默认凭证)
    'secret': None,  # AWS Secret Access Key
    'token': None,  # AWS Session Token (可选)
    'use_ssl': True,
    'client_kwargs': {
        'region_name': os.getenv('AWS_DEFAULT_REGION', 'us-east-1')
    }
}

# --- 简化的环境变量覆盖 ---
def apply_environment_overrides():
    """应用环境变量覆盖（仅核心并行配置）"""
    global NUM_WORKERS, CHUNK_ROWS, PARALLEL_CONFIG

    # 只处理核心的并行配置覆盖
    if os.getenv('FORCE_NUM_WORKERS'):
        NUM_WORKERS = int(os.getenv('FORCE_NUM_WORKERS'))
        print(f"🔧 强制设置Worker数: {NUM_WORKERS}")

    if os.getenv('FORCE_CHUNK_ROWS'):
        CHUNK_ROWS = int(os.getenv('FORCE_CHUNK_ROWS'))
        print(f"🔧 强制设置Chunk大小: {CHUNK_ROWS}")

    if os.getenv('FORCE_MEMORY_LIMIT_GB'):
        memory_limit = int(os.getenv('FORCE_MEMORY_LIMIT_GB'))
        PARALLEL_CONFIG['memory_limit_gb'] = memory_limit
        print(f"🔧 强制设置内存限制: {memory_limit}GB")

    # 更新PARALLEL_CONFIG
    PARALLEL_CONFIG.update({
        'max_workers': NUM_WORKERS,
        'chunk_size': CHUNK_ROWS,
    })

# --- 并行处理配置 ---
import multiprocessing as mp

# 动态配置基于实例类型
def get_instance_config():
    """基于CPU核心数和内存自动配置实例参数"""
    cpu_count = mp.cpu_count()

    # 检测是否为大型实例 (r5.24xlarge: 96 vCPU)
    if cpu_count >= 90:  # r5.24xlarge或更大
        return {
            'instance_type': 'r5.24xlarge',
            'max_workers': 88,  # 保留8个核心给系统和其他进程
            'chunk_rows': 200_000,  # 大内存允许更大的chunk
            'memory_limit_gb': 700,  # 使用约90%的768GB内存
            'batch_size_multiplier': 4,  # 更大的批次大小
            'io_workers': 32,  # 高I/O并发
        }
    elif cpu_count >= 45:  # r5.12xlarge: 48 vCPU
        return {
            'instance_type': 'r5.12xlarge',
            'max_workers': 44,  # 保留4个核心给系统
            'chunk_rows': 100_000,
            'memory_limit_gb': 350,  # 使用约90%的384GB内存
            'batch_size_multiplier': 2,
            'io_workers': 16,
        }
    elif cpu_count >= 30:  # r5.8xlarge: 32 vCPU
        return {
            'instance_type': 'r5.8xlarge',
            'max_workers': 28,
            'chunk_rows': 75_000,
            'memory_limit_gb': 230,  # 使用约90%的256GB内存
            'batch_size_multiplier': 2,
            'io_workers': 12,
        }
    elif cpu_count >= 15:  # r5.4xlarge: 16 vCPU
        return {
            'instance_type': 'r5.4xlarge',
            'max_workers': 14,
            'chunk_rows': 50_000,
            'memory_limit_gb': 115,  # 使用约90%的128GB内存
            'batch_size_multiplier': 1,
            'io_workers': 8,
        }
    else:  # 小型实例或开发环境
        return {
            'instance_type': 'small',
            'max_workers': max(1, cpu_count - 2),
            'chunk_rows': 25_000,
            'memory_limit_gb': 8,
            'batch_size_multiplier': 1,
            'io_workers': 4,
        }

INSTANCE_CONFIG = get_instance_config()

# 基本配置
NUM_WORKERS = INSTANCE_CONFIG['max_workers']
CHUNK_ROWS = INSTANCE_CONFIG['chunk_rows']

# 并行处理配置
PARALLEL_CONFIG = {
    'max_workers': NUM_WORKERS,
    'multiprocessing_start_method': 'spawn' if IS_WINDOWS else 'fork',
    'memory_limit_gb': INSTANCE_CONFIG['memory_limit_gb'],
    'chunk_size': CHUNK_ROWS,
    'use_multiprocessing': True,
    'timeout_seconds': 7200,  # 2小时超时，适应大数据处理
    'instance_type': INSTANCE_CONFIG['instance_type'],
    'io_workers': INSTANCE_CONFIG['io_workers'],
}

# 扩展配置
PARALLEL_EXTENDED_CONFIG = {
    'processed_data_dir': PROCESSED_DATA_DIR,
    'train_data_dir': TRAIN_DATA_DIR,
    'validation_data_dir': VALIDATION_DATA_DIR,
    'test_data_dir': TEST_DATA_DIR,
    'analysis_output_dir': ANALYSIS_OUTPUT_DIR,
    'model_output_dir': MODEL_OUTPUT_DIR,
}

# --- 性能配置 ---
def get_performance_config():
    """基于平台、存储类型和实例规格的性能配置"""

    # 获取实例配置
    instance_config = INSTANCE_CONFIG
    cpu_count = mp.cpu_count()

    # Windows = 保守配置但支持大实例
    if IS_WINDOWS:
        return {
            'dataloader_workers': 0,
            'pin_memory': False,
            'prefetch_factor': 2,
            'persistent_workers': False,
            'use_multiprocessing_in_preprocessing': True,  # 并行处理系统支持Windows
            'batch_size_multiplier': instance_config['batch_size_multiplier'],
            'max_batch_size': 4096 * instance_config['batch_size_multiplier'],
            'torch_threads': min(16, cpu_count // 4),  # 限制线程数避免过载
            'omp_num_threads': min(16, cpu_count // 4),
            'mkl_num_threads': min(16, cpu_count // 4),
            'use_async_io': False,
            'io_buffer_size': 32 * 1024 * 1024,  # 增大I/O缓冲
            'mmap_threshold': instance_config['memory_limit_gb'] * 1024 * 1024 * 1024 // 4,
            'reason': f'Windows + {STORAGE_TYPE} + {instance_config["instance_type"]} (optimized for parallel processing)'
        }

    # Unix + S3 = 针对大实例和EC2环境优化的配置
    elif IS_UNIX and STORAGE_TYPE in ['mock_s3', 'real_s3']:
        # EC2环境可以更激进地使用多进程，因为网络和存储性能更好
        if IS_EC2_DEPLOYMENT:
            use_multiprocessing = cpu_count >= 16  # EC2上r5.4xlarge及以上使用多进程
            io_workers = instance_config['io_workers']
        else:
            # 非EC2环境保守一些
            use_multiprocessing = cpu_count >= 32  # r5.8xlarge及以上使用多进程
            io_workers = instance_config['io_workers'] if use_multiprocessing else 0

        return {
            'dataloader_workers': io_workers,
            'pin_memory': True if cpu_count >= 16 else False,
            'prefetch_factor': 8 if IS_EC2_DEPLOYMENT and cpu_count >= 64 else 4 if cpu_count >= 32 else 2,
            'persistent_workers': use_multiprocessing,
            'use_multiprocessing_in_preprocessing': use_multiprocessing,
            'batch_size_multiplier': instance_config['batch_size_multiplier'],
            'max_batch_size': 8192 * instance_config['batch_size_multiplier'],
            'torch_threads': cpu_count,
            'omp_num_threads': cpu_count,
            'mkl_num_threads': cpu_count,
            'use_async_io': True,
            'io_buffer_size': 128 * 1024 * 1024 if IS_EC2_DEPLOYMENT else 64 * 1024 * 1024,  # EC2上更大的缓冲
            'mmap_threshold': instance_config['memory_limit_gb'] * 1024 * 1024 * 1024 // 2,
            'reason': f'Unix + {STORAGE_TYPE} + {instance_config["instance_type"]} ({"EC2" if IS_EC2_DEPLOYMENT else "local"} S3 optimized)'
        }

    # Unix + 本地文件 = 充分利用大实例的多进程配置
    else:  # IS_UNIX and STORAGE_TYPE == 'local'
        return {
            'dataloader_workers': instance_config['io_workers'],
            'pin_memory': True,
            'prefetch_factor': 8 if cpu_count >= 64 else 4,
            'persistent_workers': True,
            'use_multiprocessing_in_preprocessing': True,
            'batch_size_multiplier': instance_config['batch_size_multiplier'],
            'max_batch_size': 16384 * instance_config['batch_size_multiplier'],  # 大实例支持更大批次
            'torch_threads': cpu_count,
            'omp_num_threads': cpu_count,
            'mkl_num_threads': cpu_count,
            'use_async_io': True,
            'io_buffer_size': 128 * 1024 * 1024,  # 128MB I/O缓冲
            'mmap_threshold': instance_config['memory_limit_gb'] * 1024 * 1024 * 1024 // 2,
            'reason': f'Unix + local + {instance_config["instance_type"]} (fully optimized)'
        }

PERFORMANCE_CONFIG = get_performance_config()

# 应用环境特定的配置覆盖
apply_environment_overrides()

# --- GPU配置 ---
def get_gpu_config():
    """获取GPU配置"""
    try:
        import torch
        gpu_available = torch.cuda.is_available()
        device_count = torch.cuda.device_count() if gpu_available else 0
        
        return {
            'enabled': gpu_available,
            'device_count': device_count,
            'mixed_precision': True,  # 默认启用混合精度
            'gradient_accumulation_steps': 1,
            'multi_gpu_strategy': 'dp',  # 'dp' (DataParallel) or 'ddp' (DistributedDataParallel)
            'gpu_batch_size_multiplier': 4,  # GPU可处理更大批次
        }
    except ImportError:
        # 如果torch未安装，返回默认配置
        return {
            'enabled': False,
            'device_count': 0,
            'mixed_precision': False,
            'gradient_accumulation_steps': 1,
            'multi_gpu_strategy': 'dp',
            'gpu_batch_size_multiplier': 1,
        }

GPU_CONFIG = get_gpu_config()

# 根据GPU可用性动态调整性能配置
if GPU_CONFIG['enabled']:
    # GPU训练时的优化配置
    gpu_updates = {
        'dataloader_workers': 4,  # GPU可以并行加载数据
        'pin_memory': True,  # 加速CPU到GPU传输
        'prefetch_factor': 2,
        'persistent_workers': True,
    }
    
    # 只更新DataLoader相关配置，保持其他配置不变
    for key, value in gpu_updates.items():
        if key in PERFORMANCE_CONFIG:
            # Windows已经有自己的dataloader_workers配置，不要覆盖
            if key == 'dataloader_workers' and IS_WINDOWS:
                continue
            PERFORMANCE_CONFIG[key] = value
    
    # 记录GPU配置信息
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"检测到{GPU_CONFIG['device_count']}个GPU，已启用GPU优化配置")

# --- 训练配置 ---
# 基于实例规格动态调整训练配置
def get_train_config():
    """基于实例规格获取训练配置"""
    instance_config = INSTANCE_CONFIG
    performance_config = get_performance_config()

    base_batch_size = performance_config['max_batch_size']

    return {
        'epochs': 30,
        'batch_size': base_batch_size,
        'learning_rate': 1e-3,
        'early_stopping_patience': 8,
        'validation_split': 0.2,
        'random_seed': 42,
        'device': 'auto',
        'gradient_accumulation_steps': 1 if instance_config['memory_limit_gb'] >= 100 else 2,
        'mixed_precision': True if instance_config['memory_limit_gb'] >= 200 else False,
    }

GLOBAL_TRAIN_CONFIG = get_train_config()

def get_model_specific_config():
    """基于实例规格获取模型特定配置"""
    instance_config = INSTANCE_CONFIG
    performance_config = get_performance_config()

    base_batch_size = performance_config['max_batch_size']

    # 大实例可以使用更复杂的模型架构
    if instance_config['memory_limit_gb'] >= 500:  # r5.24xlarge
        hidden_dims_mlp = [1024, 512, 256, 128]
        deep_layers_dcnv2 = [1024, 512, 256, 128]
        cross_layers = 4
    elif instance_config['memory_limit_gb'] >= 200:  # r5.12xlarge
        hidden_dims_mlp = [768, 384, 192, 96]
        deep_layers_dcnv2 = [768, 384, 192, 96]
        cross_layers = 3
    else:  # 较小实例
        hidden_dims_mlp = [512, 256, 128]
        deep_layers_dcnv2 = [512, 256, 128]
        cross_layers = 3

    return {
        'mlp': {
            'learning_rate': 1.2e-3,
            'batch_size': base_batch_size,
            'epochs': 30,
            'early_stopping_patience': 8,
            'hidden_dims': hidden_dims_mlp,
            'dropout_rate': 0.2,
            'activation': 'relu',
            'gradient_accumulation_steps': GLOBAL_TRAIN_CONFIG['gradient_accumulation_steps'],
            'mixed_precision': GLOBAL_TRAIN_CONFIG['mixed_precision'],
        },
        'dcnv2': {
            'learning_rate': 2e-4,
            'batch_size': max(base_batch_size // 2, 1024),  # DCNv2需要更多内存
            'epochs': 30,
            'early_stopping_patience': 10,
            'cross_layers': cross_layers,
            'deep_layers': deep_layers_dcnv2,
            'dropout_rate': 0.2,
            'activation': 'relu',
            'gradient_accumulation_steps': GLOBAL_TRAIN_CONFIG['gradient_accumulation_steps'],
            'mixed_precision': GLOBAL_TRAIN_CONFIG['mixed_precision'],
        },
        'dcnv1': {
            'learning_rate': 1.5e-3,
            'batch_size': max(base_batch_size // 2, 1024),
            'epochs': 30,
            'early_stopping_patience': 10,
            'cross_layers': cross_layers,
            'deep_dims': (512, 256, 128),
            'dropout_rate': 0.2,
            'activation': 'relu',
            'gradient_accumulation_steps': GLOBAL_TRAIN_CONFIG['gradient_accumulation_steps'],
            'mixed_precision': GLOBAL_TRAIN_CONFIG['mixed_precision'],
        },
        'dlrm': {
            'learning_rate': 1e-3,
            'batch_size': max(base_batch_size // 4, 512),  # DLRM需要更多内存
            'epochs': 20,
            'early_stopping_patience': 8,
            'bot_dims': (512, 256),
            'top_dims': (256, 128, 1),
            'dropout_rate': 0.2,
            'activation': 'relu',
            'gradient_accumulation_steps': GLOBAL_TRAIN_CONFIG['gradient_accumulation_steps'],
            'mixed_precision': GLOBAL_TRAIN_CONFIG['mixed_precision'],
        }
    }

MODEL_SPECIFIC_CONFIG = get_model_specific_config()

# --- 数据分析配置 ---
LABEL_CANDIDATES = ['label', 'target', 'y', 'clicked', 'click', 'conversion', 'converted']
MIN_ARRAY_LENGTH = 2
ANALYSIS_SAMPLE_SIZE = 10000

# --- 日志配置 ---
LOGGING_CONFIG = {
    'level': 'INFO',
    'save_to_file': True,
    'log_dir': os.path.join(BASE_DIR, 'logs'),
}

# 检查是否应该显示详细路径信息（避免在TRACK模式下显示）
import sys
show_paths = '--track-only' not in sys.argv
show_config = '--help' not in sys.argv and len(sys.argv) > 1  # 只有在实际运行时才显示配置

# 环境信息输出（简化版，专注于并行配置）
# 修复Windows subprocess中文编码问题
# 只在主进程中打印，避免多进程重复输出
import multiprocessing
if show_config and multiprocessing.current_process().name == 'MainProcess':
    try:
        print(f"并行处理系统配置:")
        print(f"  平台: {PLATFORM}")
        print(f"  实例类型: {INSTANCE_CONFIG['instance_type']}")
        print(f"  CPU核心数: {mp.cpu_count()}")
        print(f"  存储类型: {STORAGE_TYPE}")
        print(f"  性能配置: {PERFORMANCE_CONFIG['reason']}")
        print(f"  最大Worker数: {PARALLEL_CONFIG['max_workers']}")
        print(f"  内存限制: {PARALLEL_CONFIG['memory_limit_gb']}GB")
        print(f"  Chunk大小: {PARALLEL_CONFIG['chunk_size']:,}行")
        print(f"  I/O Worker数: {PARALLEL_CONFIG['io_workers']}")
        print(f"  启动方法: {PARALLEL_CONFIG['multiprocessing_start_method']}")
        print(f"  批次大小: {GLOBAL_TRAIN_CONFIG['batch_size']}")
    except UnicodeEncodeError:
        # Windows subprocess环境下的fallback
        print("Parallel Processing System Configuration:")
        print(f"  Platform: {PLATFORM}")
        print(f"  Instance Type: {INSTANCE_CONFIG['instance_type']}")
        print(f"  CPU Cores: {mp.cpu_count()}")
        print(f"  Storage Type: {STORAGE_TYPE}")
        print(f"  Performance Config: {PERFORMANCE_CONFIG['reason']}")
        print(f"  Max Workers: {PARALLEL_CONFIG['max_workers']}")
        print(f"  Memory Limit: {PARALLEL_CONFIG['memory_limit_gb']}GB")
        print(f"  Chunk Size: {PARALLEL_CONFIG['chunk_size']:,} rows")
        print(f"  I/O Workers: {PARALLEL_CONFIG['io_workers']}")
        print(f"  Start Method: {PARALLEL_CONFIG['multiprocessing_start_method']}")
        print(f"  Batch Size: {GLOBAL_TRAIN_CONFIG['batch_size']}")



if show_config:
    if USE_S3:
        try:
            print(f"  S3环境检测:")
            if show_paths:
                print(f"    训练数据: {TRAIN_DATA_DIR}")
                print(f"    验证数据: {VALIDATION_DATA_DIR}")
                print(f"    测试数据: {TEST_DATA_DIR}")
            else:
                print(f"    训练数据: [配置已设置]")
                print(f"    验证数据: [配置已设置]")
                print(f"    测试数据: [配置已设置]")
            print(f"    🔐 认证方式: 默认凭证/IAM角色")
        except UnicodeEncodeError:
            print("  S3 Environment Detection:")
            if show_paths:
                print(f"    Train Data: {TRAIN_DATA_DIR}")
                print(f"    Validation Data: {VALIDATION_DATA_DIR}")
                print(f"    Test Data: {TEST_DATA_DIR}")
            else:
                print(f"    Train Data: [Configured]")
                print(f"    Validation Data: [Configured]")
                print(f"    Test Data: [Configured]")
            print(f"    Auth Method: Default Credentials/IAM Role")
    else:
        try:
            print(f"  本地环境: 使用本地文件系统")
        except UnicodeEncodeError:
            print("  Local Environment: Using Local File System")

    # 性能优化提示
    try:
        if INSTANCE_CONFIG['instance_type'] == 'r5.24xlarge':
            print(f"\n🚀 r5.24xlarge优化配置已启用:")
            print(f"   - 88个并行Worker (保留8核给系统)")
            print(f"   - 700GB内存限制 (90%利用率)")
            print(f"   - 200K行Chunk大小 (优化大内存)")
            print(f"   - 32个I/O Worker (高并发I/O)")
            print(f"   - 混合精度训练: {GLOBAL_TRAIN_CONFIG.get('mixed_precision', False)}")
        elif INSTANCE_CONFIG['instance_type'] == 'r5.12xlarge':
            print(f"\n⚡ r5.12xlarge优化配置已启用:")
            print(f"   - 44个并行Worker")
            print(f"   - 350GB内存限制")
            print(f"   - 100K行Chunk大小")
            print(f"   - 16个I/O Worker")
    except UnicodeEncodeError:
        if INSTANCE_CONFIG['instance_type'] == 'r5.24xlarge':
            print(f"\nr5.24xlarge Optimized Configuration Enabled:")
            print(f"   - 88 Parallel Workers (8 cores reserved for system)")
            print(f"   - 700GB Memory Limit (90% utilization)")
            print(f"   - 200K Row Chunk Size (optimized for large memory)")
            print(f"   - 32 I/O Workers (high concurrency I/O)")
            print(f"   - Mixed Precision Training: {GLOBAL_TRAIN_CONFIG.get('mixed_precision', False)}")
        elif INSTANCE_CONFIG['instance_type'] == 'r5.12xlarge':
            print(f"\nr5.12xlarge Optimized Configuration Enabled:")
            print(f"   - 44 Parallel Workers")
            print(f"   - 350GB Memory Limit")
            print(f"   - 100K Row Chunk Size")
            print(f"   - 16 I/O Workers")
