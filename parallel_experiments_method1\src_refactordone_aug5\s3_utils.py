# s3_utils.py
"""
S3工具模块 - 提供S3文件系统访问功能
增强版：添加连接池管理、重试机制和详细的并发调试信息
"""

import os
import logging
import threading
import time
import random
from typing import Optional, List, Union
import pandas as pd

try:
    import s3fs
    import boto3
    from botocore.config import Config
    S3_AVAILABLE = True
except ImportError:
    S3_AVAILABLE = False
    logging.warning("S3 dependencies not available. Install with: pip install s3fs boto3")

try:
    from .config import S3_CONFIG, is_s3_path
    from .common import io as common_io
except ImportError:
    # Fallback for direct execution
    import sys
    import os
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    from src.config import S3_CONFIG, is_s3_path
    from src.common import io as common_io

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 全局连接统计和管理
_connection_stats = {
    'total_connections': 0,
    'active_connections': 0,
    'failed_connections': 0,
    'retry_attempts': 0,
    'connection_times': [],
    'process_connections': {}  # pid -> connection_count
}
_stats_lock = threading.Lock()

def log_connection_stats(operation=""):
    """记录连接统计信息"""
    with _stats_lock:
        pid = os.getpid()
        process_conn = _connection_stats['process_connections'].get(pid, 0)

        # 导入跟踪函数
        try:
            from minimal_logger import track_s3_request
            track_s3_request("CONNECTION_STATS",
                           f"pid={pid} process_conn={process_conn} "
                           f"total={_connection_stats['total_connections']} "
                           f"active={_connection_stats['active_connections']} "
                           f"failed={_connection_stats['failed_connections']} "
                           f"retries={_connection_stats['retry_attempts']} {operation}")
        except:
            pass

        logging.info(f"S3连接统计 [PID:{pid}] - 进程连接数:{process_conn}, "
                    f"全局总连接:{_connection_stats['total_connections']}, "
                    f"活跃连接:{_connection_stats['active_connections']}, "
                    f"失败连接:{_connection_stats['failed_connections']}, "
                    f"重试次数:{_connection_stats['retry_attempts']} {operation}")

def update_connection_stats(operation, success=True):
    """更新连接统计"""
    with _stats_lock:
        pid = os.getpid()
        if operation == 'create':
            _connection_stats['total_connections'] += 1
            _connection_stats['active_connections'] += 1
            _connection_stats['process_connections'][pid] = _connection_stats['process_connections'].get(pid, 0) + 1
        elif operation == 'close':
            _connection_stats['active_connections'] = max(0, _connection_stats['active_connections'] - 1)
            if pid in _connection_stats['process_connections']:
                _connection_stats['process_connections'][pid] = max(0, _connection_stats['process_connections'][pid] - 1)
        elif operation == 'retry':
            _connection_stats['retry_attempts'] += 1
        elif operation == 'fail':
            _connection_stats['failed_connections'] += 1

class S3FileSystem:
    """
    S3文件系统封装类
    """
    
    def __init__(self, config: dict = None):
        """
        初始化S3文件系统
        
        Args:
            config: S3配置字典
        """
        if not S3_AVAILABLE:
            raise ImportError("S3 dependencies not available. Install with: pip install s3fs boto3")
        
        self.config = config or S3_CONFIG
        self.fs = None
        self._initialize_fs()
    
    def _initialize_fs(self):
        """
        初始化s3fs文件系统 - 增强版，包含连接池配置和重试机制
        """
        pid = os.getpid()
        logging.info(f"=== Initializing s3fs filesystem [PID:{pid}] ===")
        logging.info(f"S3 config: {self.config}")
        log_connection_stats("before_init")

        max_retries = 3
        retry_delay = 1.0

        for attempt in range(max_retries):
            try:
                # 构建s3fs参数 - 使用最简单的配置避免版本兼容问题
                # 🔧 完全避免config参数冲突 - 使用最基本的配置
                base_client_kwargs = self.config.get('client_kwargs', {})
                client_kwargs = {}

                # 只复制基本参数，完全避免config相关配置
                for key, value in base_client_kwargs.items():
                    if key not in ['config']:  # 完全跳过任何config相关参数
                        client_kwargs[key] = value
                    else:
                        logging.warning(f"Skipping '{key}' in client_kwargs to avoid conflicts [PID:{pid}]")

                # 确保有region_name
                if 'region_name' not in client_kwargs:
                    client_kwargs['region_name'] = 'us-east-1'

                # 🔧 使用最简单的fs_kwargs，不包含任何自定义config
                fs_kwargs = {
                    'use_ssl': self.config.get('use_ssl', True),
                    'client_kwargs': client_kwargs
                }
                logging.info(f"Base fs_kwargs [PID:{pid}]: {fs_kwargs}")

                # 添加认证信息（如果提供）
                if self.config.get('key') and self.config.get('secret'):
                    fs_kwargs['key'] = self.config['key']
                    fs_kwargs['secret'] = self.config['secret']
                    if self.config.get('token'):
                        fs_kwargs['token'] = self.config['token']
                    logging.info(f"Added AWS credentials to fs_kwargs [PID:{pid}]")
                else:
                    logging.info(f"No explicit AWS credentials provided, using default credential chain [PID:{pid}]")

                logging.info(f"Final fs_kwargs [PID:{pid}]: {fs_kwargs}")
                logging.info(f"Creating s3fs.S3FileSystem [PID:{pid}] (attempt {attempt + 1}/{max_retries})...")

                start_time = time.time()
                self.fs = s3fs.S3FileSystem(**fs_kwargs)
                init_time = time.time() - start_time

                update_connection_stats('create')
                logging.info(f"S3 file system initialized successfully [PID:{pid}] in {init_time:.2f}s")
                logging.info(f"S3 filesystem object [PID:{pid}]: {self.fs}")
                log_connection_stats("after_init_success")
                return

            except Exception as e:
                update_connection_stats('fail')
                logging.error(f"Failed to initialize S3 file system [PID:{pid}] (attempt {attempt + 1}/{max_retries}): {str(e)}")

                if attempt < max_retries - 1:
                    update_connection_stats('retry')
                    delay = retry_delay * (2 ** attempt) + random.uniform(0, 1)  # 指数退避 + 随机抖动
                    logging.info(f"Retrying S3 initialization [PID:{pid}] in {delay:.2f}s...")
                    time.sleep(delay)
                else:
                    import traceback
                    logging.error(f"All S3 initialization attempts failed [PID:{pid}]. Traceback: {traceback.format_exc()}")
                    log_connection_stats("after_init_failed")
                    raise
    
    def list_files(self, s3_path: str, pattern: str = "*.parquet") -> List[str]:
        """
        列出S3路径下的文件

        Args:
            s3_path: S3路径
            pattern: 文件模式

        Returns:
            文件路径列表
        """
        logging.info(f"=== S3FileSystem.list_files called ===")
        logging.info(f"S3 path: {s3_path}")
        logging.info(f"Pattern: {pattern}")
        logging.info(f"Process ID: {os.getpid()}")

        try:
            # 移除s3://前缀
            clean_path = s3_path.replace('s3://', '')
            logging.info(f"Clean path (removed s3://): {clean_path}")

            if not clean_path.endswith('/'):
                clean_path += '/'
                logging.info(f"Added trailing slash: {clean_path}")

            # 列出文件
            glob_pattern = clean_path + pattern
            logging.info(f"Using glob pattern: {glob_pattern}")

            # 🔧 Fork-Safe修复: 使用进程本地的s3fs实例
            logging.info("Creating process-local s3fs instance for fork-safe operation")
            process_local_fs = self._create_process_local_fs()

            files = process_local_fs.glob(glob_pattern)
            logging.info(f"Raw glob result: {files}")

            # 添加s3://前缀
            s3_files = [f"s3://{f}" for f in files]

            logging.info(f"Final S3 files list: {s3_files}")
            logging.info(f"Found {len(s3_files)} files in {s3_path}")
            return s3_files

        except Exception as e:
            logging.error(f"Error listing files in {s3_path}: {str(e)}")
            import traceback
            logging.error(f"Traceback: {traceback.format_exc()}")
            return []
    
    def exists(self, s3_path: str) -> bool:
        """
        检查S3路径是否存在

        Args:
            s3_path: S3路径

        Returns:
            是否存在
        """
        logging.info(f"=== S3FileSystem.exists called ===")
        logging.info(f"S3 path: {s3_path}")
        logging.info(f"Process ID: {os.getpid()}")

        try:
            clean_path = s3_path.replace('s3://', '')
            logging.info(f"Clean path (removed s3://): {clean_path}")

            # 🔧 Fork-Safe修复: 使用进程本地的s3fs实例
            logging.info("Creating process-local s3fs instance for fork-safe operation")
            process_local_fs = self._create_process_local_fs()

            exists = process_local_fs.exists(clean_path)
            logging.info(f"S3 filesystem exists result: {exists}")
            return exists
        except Exception as e:
            logging.error(f"Error checking existence of {s3_path}: {str(e)}")
            import traceback
            logging.error(f"Traceback: {traceback.format_exc()}")
            return False
    
    def _create_process_local_fs(self):
        """
        创建进程本地的s3fs实例，避免fork-safe问题
        增强版：包含连接池管理和重试机制

        Returns:
            s3fs.S3FileSystem实例
        """
        pid = os.getpid()
        logging.info(f"Creating process-local s3fs for PID: {pid}")
        log_connection_stats("before_local_fs_create")

        import s3fs
        from botocore.config import Config

        max_retries = 3
        retry_delay = 0.5

        for attempt in range(max_retries):
            try:
                # 构建参数，包含连接池配置
                # 🔧 安全地构建client_kwargs，避免config参数冲突
                base_client_kwargs = self.config.get('client_kwargs', {})
                client_kwargs = {}

                # 复制除了'config'之外的所有参数
                for key, value in base_client_kwargs.items():
                    if key != 'config':
                        client_kwargs[key] = value
                    else:
                        logging.warning(f"Skipping existing 'config' in client_kwargs to avoid conflicts [PID:{pid}]")

                # 确保有region_name
                if 'region_name' not in client_kwargs:
                    client_kwargs['region_name'] = 'us-east-1'

                # 创建我们自己的botocore配置
                # 🔧 使用最简单的配置，避免版本兼容问题
                # 不添加任何自定义的botocore配置

                fs_kwargs = {
                    'use_ssl': self.config.get('use_ssl', True),
                    'client_kwargs': client_kwargs
                }

                # 添加认证信息（如果提供且不为None）
                if self.config.get('key'):
                    fs_kwargs['key'] = self.config['key']
                if self.config.get('secret'):
                    fs_kwargs['secret'] = self.config['secret']
                if self.config.get('token'):
                    fs_kwargs['token'] = self.config['token']

                logging.info(f"Process-local fs_kwargs [PID:{pid}] (attempt {attempt + 1}/{max_retries}): {fs_kwargs}")

                start_time = time.time()
                fs = s3fs.S3FileSystem(**fs_kwargs)
                create_time = time.time() - start_time

                update_connection_stats('create')
                logging.info(f"Process-local s3fs created successfully [PID:{pid}] in {create_time:.2f}s")
                log_connection_stats("after_local_fs_create_success")
                return fs

            except Exception as e:
                update_connection_stats('fail')
                logging.error(f"Failed to create process-local s3fs [PID:{pid}] (attempt {attempt + 1}/{max_retries}): {str(e)}")

                if attempt < max_retries - 1:
                    update_connection_stats('retry')
                    delay = retry_delay * (2 ** attempt) + random.uniform(0, 0.5)
                    logging.info(f"Retrying process-local s3fs creation [PID:{pid}] in {delay:.2f}s...")
                    time.sleep(delay)
                else:
                    log_connection_stats("after_local_fs_create_failed")
                    raise

    def read_parquet(self, s3_path: str, **kwargs) -> pd.DataFrame:
        """
        读取S3上的Parquet文件 - 增强版，包含重试机制和详细调试

        Args:
            s3_path: S3文件路径
            **kwargs: pandas.read_parquet的额外参数

        Returns:
            DataFrame
        """
        pid = os.getpid()
        filename = os.path.basename(s3_path)

        # 检查当前日志级别，决定是否显示路径
        show_paths = logging.getLogger().level < 45  # TRACK级别是45

        logging.info(f"=== S3FileSystem.read_parquet called [PID:{pid}] ===")
        if show_paths:
            logging.info(f"S3 path: {s3_path}")
            logging.debug(f"Full S3 path: {s3_path}")
        else:
            logging.info(f"Filename: {filename}")
        logging.info(f"Kwargs: {kwargs}")
        log_connection_stats("before_read_parquet")

        max_retries = 3
        retry_delay = 1.0

        for attempt in range(max_retries):
            try:
                if show_paths:
                    logging.info(f"Reading parquet file [PID:{pid}] (attempt {attempt + 1}/{max_retries}): {s3_path}")
                else:
                    logging.info(f"Reading parquet file [PID:{pid}] (attempt {attempt + 1}/{max_retries}): {filename}")

                # 🔧 Fork-Safe修复: 在每次读取时创建新的s3fs实例
                # 避免在多进程环境中共享s3fs连接
                logging.info(f"Creating process-local s3fs instance for fork-safe operation [PID:{pid}]")

                # 创建进程本地的s3fs实例
                process_local_fs = self._create_process_local_fs()

                # 使用进程本地的文件系统读取
                start_time = time.time()
                df = pd.read_parquet(s3_path, filesystem=process_local_fs, **kwargs)
                read_time = time.time() - start_time

                if show_paths:
                    logging.info(f"Successfully read {len(df)} rows, {len(df.columns)} columns from {s3_path} [PID:{pid}] in {read_time:.2f}s")
                else:
                    logging.info(f"Successfully read {len(df)} rows, {len(df.columns)} columns from {filename} [PID:{pid}] in {read_time:.2f}s")

                logging.debug(f"DataFrame columns [PID:{pid}]: {list(df.columns)}")
                logging.debug(f"DataFrame dtypes [PID:{pid}]: {dict(df.dtypes)}")
                log_connection_stats("after_read_parquet_success")

                # 导入跟踪函数记录成功读取
                try:
                    from minimal_logger import track_s3_response
                    track_s3_response("READ_PARQUET", "SUCCESS", f"pid={pid} file={filename} rows={len(df)} time={read_time:.1f}s")
                except:
                    pass

                return df

            except Exception as e:
                error_type = type(e).__name__
                logging.error(f"Error reading parquet file {filename} [PID:{pid}] (attempt {attempt + 1}/{max_retries}): {error_type} - {str(e)}")

                # 导入跟踪函数记录错误
                try:
                    from minimal_logger import track_s3_response
                    track_s3_response("READ_PARQUET", f"ERROR_{error_type}", f"pid={pid} file={filename} attempt={attempt + 1}")
                except:
                    pass

                if attempt < max_retries - 1:
                    update_connection_stats('retry')
                    delay = retry_delay * (2 ** attempt) + random.uniform(0, 1)
                    logging.info(f"Retrying parquet read [PID:{pid}] in {delay:.2f}s...")
                    time.sleep(delay)
                else:
                    import traceback
                    logging.error(f"All parquet read attempts failed [PID:{pid}] for {filename}. Traceback: {traceback.format_exc()}")
                    log_connection_stats("after_read_parquet_failed")
                    raise

# 进程本地S3文件系统实例 - 避免fork-safe问题
# 使用延迟初始化确保在所有环境中都能正常工作
_thread_local = None

def _get_thread_local():
    """获取线程本地存储，延迟初始化"""
    global _thread_local
    if _thread_local is None:
        _thread_local = threading.local()
    return _thread_local

def get_s3_filesystem() -> Optional[S3FileSystem]:
    """
    获取进程本地S3文件系统实例

    每个进程/线程都会有自己独立的S3连接，避免fork-safe问题

    Returns:
        S3FileSystem实例或None
    """
    logging.info("=== get_s3_filesystem called ===")
    logging.info(f"Process ID: {os.getpid()}")
    logging.info(f"S3_AVAILABLE: {S3_AVAILABLE}")

    if not S3_AVAILABLE:
        logging.error("S3 not available - missing dependencies")
        return None

    # 获取线程本地存储
    thread_local = _get_thread_local()

    # 检查当前进程/线程是否已有S3文件系统实例
    if not hasattr(thread_local, 's3_fs') or thread_local.s3_fs is None:
        logging.info(f"Creating new S3 filesystem for process {os.getpid()}...")
        try:
            thread_local.s3_fs = S3FileSystem()
            logging.info(f"S3 filesystem created successfully for process {os.getpid()}")
        except Exception as e:
            logging.error(f"Failed to create S3 file system: {str(e)}")
            import traceback
            logging.error(f"Traceback: {traceback.format_exc()}")
            return None
    else:
        logging.info(f"Using existing S3 filesystem instance for process {os.getpid()}")

    return thread_local.s3_fs

def cleanup_s3_filesystem():
    """
    清理当前进程的S3文件系统实例 - 增强版，包含连接统计清理

    在进程结束时调用，确保正确释放S3连接资源
    """
    pid = os.getpid()
    try:
        log_connection_stats("before_cleanup")

        thread_local = _get_thread_local()
        if hasattr(thread_local, 's3_fs') and thread_local.s3_fs is not None:
            logging.info(f"Cleaning up S3 filesystem for process {pid}")
            try:
                # 尝试关闭S3连接
                if hasattr(thread_local.s3_fs, 'fs') and thread_local.s3_fs.fs is not None:
                    # s3fs没有显式的close方法，但我们可以清除引用
                    thread_local.s3_fs.fs = None
                thread_local.s3_fs = None
                update_connection_stats('close')
                logging.info(f"S3 filesystem cleaned up for process {pid}")
            except Exception as e:
                logging.warning(f"Error during S3 cleanup for process {pid}: {e}")

        # 清理进程连接统计
        with _stats_lock:
            if pid in _connection_stats['process_connections']:
                process_conn_count = _connection_stats['process_connections'][pid]
                logging.info(f"Process {pid} had {process_conn_count} connections, cleaning up...")
                # 减少活跃连接数
                _connection_stats['active_connections'] = max(0, _connection_stats['active_connections'] - process_conn_count)
                del _connection_stats['process_connections'][pid]

        log_connection_stats("after_cleanup")

    except Exception as e:
        logging.warning(f"Error accessing thread local storage during cleanup for process {pid}: {e}")

def list_parquet_files(data_path: str) -> List[str]:
    """
    列出数据路径下的所有Parquet文件（支持本地和S3）

    Args:
        data_path: 数据路径（本地或S3）

    Returns:
        Parquet文件路径列表
    """
    logging.info(f"=== list_parquet_files called with path: {data_path} ===")
    logging.info(f"Is S3 path: {is_s3_path(data_path)}")

    if is_s3_path(data_path):
        # S3路径
        logging.info("Processing as S3 path...")
        try:
            s3_fs = get_s3_filesystem()
            if s3_fs is None:
                logging.error("S3 file system not available")
                return []

            logging.info("S3 filesystem obtained, calling list_files...")
            files = s3_fs.list_files(data_path, "*.parquet")
            logging.info(f"S3 list_files returned: {files}")
            return files
        except Exception as e:
            logging.error(f"Exception in S3 list_parquet_files: {str(e)}")
            return []
    else:
        # 本地路径
        logging.info("Processing as local path...")
        import glob
        if not os.path.exists(data_path):
            logging.error(f"Local path does not exist: {data_path}")
            return []

        pattern = os.path.join(data_path, "*.parquet")
        logging.info(f"Using glob pattern: {pattern}")
        files = glob.glob(pattern)
        logging.info(f"Local glob returned {len(files)} files: {files}")
        return files

def read_parquet_file(file_path: str, **kwargs) -> pd.DataFrame:
    """
    读取Parquet文件（支持本地和S3）

    Args:
        file_path: 文件路径（本地或S3）
        **kwargs: pandas.read_parquet的额外参数

    Returns:
        DataFrame
    """
    logging.info(f"=== read_parquet_file called with: {file_path} ===")
    logging.info(f"Is S3 path: {is_s3_path(file_path)}")
    logging.info(f"Additional kwargs: {kwargs}")

    try:
        if is_s3_path(file_path):
            # S3文件
            logging.info("Processing as S3 file...")
            s3_fs = get_s3_filesystem()
            if s3_fs is None:
                raise RuntimeError("S3 file system not available")

            logging.info("S3 filesystem obtained, calling read_parquet...")
            df = s3_fs.read_parquet(file_path, **kwargs)
            logging.info(f"S3 read_parquet successful. DataFrame shape: {df.shape}")
            return df
        else:
            # 本地文件
            logging.info("Processing as local file...")
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Local file does not exist: {file_path}")

            df = common_io.read_parquet(file_path, columns=kwargs.get('columns'))
            logging.info(f"Local read_parquet successful. DataFrame shape: {df.shape}")
            return df
    except Exception as e:
        logging.error(f"Exception in read_parquet_file for {file_path}: {str(e)}")
        raise

def path_exists(path: str) -> bool:
    """
    检查路径是否存在（支持本地和S3）

    Args:
        path: 路径（本地或S3）

    Returns:
        是否存在
    """
    logging.info(f"=== path_exists called with: {path} ===")
    logging.info(f"Is S3 path: {is_s3_path(path)}")

    try:
        if is_s3_path(path):
            # S3路径
            logging.info("Checking S3 path existence...")
            s3_fs = get_s3_filesystem()
            if s3_fs is None:
                logging.error("S3 filesystem not available for path_exists")
                return False

            exists = s3_fs.exists(path)
            logging.info(f"S3 path exists result: {exists}")
            return exists
        else:
            # 本地路径
            logging.info("Checking local path existence...")
            exists = os.path.exists(path)
            logging.info(f"Local path exists result: {exists}")
            return exists
    except Exception as e:
        logging.error(f"Exception in path_exists for {path}: {str(e)}")
        return False
