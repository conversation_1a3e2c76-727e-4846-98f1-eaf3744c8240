# ADR-0001: 模型类型选择

## 状态
已采纳

## 上下文
在构建推荐系统时，需要在多种深度学习模型架构中做出选择。主要候选包括：
- 传统的多层感知器（MLP）
- 专为推荐设计的Deep & Cross Network（DCN）
- Facebook的Deep Learning Recommendation Model（DLRM）
- 其他如Wide & Deep、DeepFM等

## 决策
选择DCNv2作为主要模型架构，同时保留MLP、DCNv1和DLRM作为对比基线。

## 原因

### 为什么选择DCNv2？

1. **特征交叉能力强**
   - DCNv2专门设计用于自动学习特征交叉
   - Cross Network可以学习任意阶的特征交互
   - 不需要手工特征工程

2. **参数效率高**
   - 相比MLP，DCNv2用更少的参数达到更好效果
   - Cross层参数量为O(d)，而非O(d²)
   - 总参数约150K，适合中等规模数据

3. **训练稳定**
   - 相比DCNv1，v2版本改进了梯度流
   - 残差连接避免梯度消失
   - 批标准化提高训练稳定性

4. **性能表现优异**
   - 在多个推荐数据集上表现SOTA
   - AUC通常比MLP高2-3%
   - 推理速度快，延迟<2ms

### 模型对比

| 模型 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| MLP | 简单通用 | 无法自动特征交叉 | 基线对比 |
| DCNv1 | 显式特征交叉 | 梯度不稳定 | 早期版本 |
| DCNv2 | 稳定高效 | 需要调参 | **主要选择** |
| DLRM | Facebook背书 | 复杂度高 | 大规模场景 |

### 实验结果

在内部数据集上的表现：
```
模型    | AUC   | 参数量 | 训练时间 | 推理延迟
--------|-------|--------|----------|----------
MLP     | 0.75  | 75K    | 10min    | <1ms
DCNv1   | 0.77  | 100K   | 12min    | <2ms
DCNv2   | 0.78  | 150K   | 15min    | <2ms
DLRM    | 0.79  | 500K   | 30min    | <5ms
```

## 后果

### 积极后果
- 模型性能提升2-3%
- 自动特征交叉减少特征工程工作
- 训练稳定，容易调参
- 推理速度满足线上要求

### 消极后果
- 需要理解Cross Network原理
- 相比MLP增加了复杂度
- 需要调整特定超参数（cross层数等）

## 替代方案

1. **Wide & Deep**
   - 优点：Google验证，广泛使用
   - 缺点：需要手工特征工程
   - 未选原因：DCN是其改进版本

2. **DeepFM**
   - 优点：结合FM和DNN
   - 缺点：计算复杂度高
   - 未选原因：性能提升有限

3. **AutoInt**
   - 优点：使用注意力机制
   - 缺点：计算成本高
   - 未选原因：不适合实时推理

## 实施细节

### 模型配置
```python
DCNv2(
    input_size=177,           # 特征维度
    hidden_sizes=[256, 128],  # DNN部分
    num_cross_layers=3,       # Cross层数
    dropout_rate=0.1,         # Dropout率
    use_bn=True              # 批标准化
)
```

### 关键超参数
- `num_cross_layers`: 3（经验最优）
- `hidden_sizes`: [256, 128]（逐层递减）
- `dropout_rate`: 0.1（防止过拟合）

## 参考
- [1] Wang et al., "DCN V2: Improved Deep & Cross Network", 2020
- [2] 内部实验报告 #2024-07
- [3] MLflow实验记录 experiment_id: dcnv2_comparison