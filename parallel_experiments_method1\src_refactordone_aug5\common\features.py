"""
Common feature engineering functions.

This module provides pure functions for feature transformation without I/O operations.
"""

import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, List
import logging


def transform_features(
    df: pd.DataFrame,
    *,
    feature_config: Optional[Dict[str, Any]] = None
) -> Optional[np.ndarray]:
    """
    Transform DataFrame into feature array.
    
    Pure function that only transforms the input DataFrame without any I/O operations.
    
    Args:
        df: Input DataFrame
        feature_config: Feature configuration dictionary containing:
            - numeric_columns: List of numeric column names
            - categorical_columns: List of categorical column names
            - array_columns: List of array column names
            - array_dimensions: Dict mapping array columns to their dimensions
            - label_column: Label column name to exclude
            
    Returns:
        np.ndarray: 2D feature array (n_samples, n_features) with dtype=float32,
                    or None if no valid features
    """
    if feature_config is None:
        return None
        
    feature_arrays = []
    label_column = feature_config.get('label_column')
    
    # Process numeric columns
    numeric_columns = feature_config.get('numeric_columns', [])
    for col in numeric_columns:
        if col in df.columns and col != label_column:
            values = pd.to_numeric(df[col], errors='coerce').fillna(0).values
            feature_arrays.append(values.reshape(-1, 1))
    
    # Process categorical columns
    categorical_columns = feature_config.get('categorical_columns', [])
    for col in categorical_columns:
        if col in df.columns and col != label_column:
            # Simple label encoding using occurrence order
            unique_values = df[col].unique()
            value_to_idx = {val: idx for idx, val in enumerate(unique_values)}
            encoded_values = df[col].map(value_to_idx).fillna(0).values
            feature_arrays.append(encoded_values.reshape(-1, 1))
    
    # Process array columns
    array_columns = feature_config.get('array_columns', [])
    array_dimensions = feature_config.get('array_dimensions', {})
    for col in array_columns:
        if col in df.columns:
            array_dim = array_dimensions.get(col, 0)
            if array_dim > 0:
                # Expand array column
                array_features = _expand_array_column_impl(df[col], array_dim)
                if array_features is not None:
                    feature_arrays.append(array_features)
    
    # Concatenate all features
    if feature_arrays:
        features = np.concatenate(feature_arrays, axis=1).astype(np.float32)
        return features
    else:
        return None


def _expand_array_column_impl(series: pd.Series, expected_dim: int) -> Optional[np.ndarray]:
    """
    Expand array column into multiple feature columns.
    
    Args:
        series: Series containing arrays
        expected_dim: Expected array dimension
        
    Returns:
        np.ndarray: Expanded feature matrix, or None on failure
    """
    try:
        expanded_arrays = []
        
        for item in series:
            if isinstance(item, (list, np.ndarray)):
                arr = np.array(item, dtype=np.float32)
                if len(arr) == expected_dim:
                    expanded_arrays.append(arr)
                else:
                    # Pad or truncate to expected dimension
                    if len(arr) < expected_dim:
                        padded = np.zeros(expected_dim, dtype=np.float32)
                        padded[:len(arr)] = arr
                        expanded_arrays.append(padded)
                    else:
                        expanded_arrays.append(arr[:expected_dim])
            else:
                # If not array, fill with zeros
                expanded_arrays.append(np.zeros(expected_dim, dtype=np.float32))
                
        if expanded_arrays:
            return np.array(expanded_arrays, dtype=np.float32)
        else:
            return None
            
    except Exception as e:
        logging.error(f"数组列展开失败: {e}")
        return None