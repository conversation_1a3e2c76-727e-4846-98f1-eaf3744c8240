# 系统架构 Architecture

## C1: 系统上下文图 (System Context)

```mermaid
graph TB
    User[数据科学家/ML工程师]
    
    subgraph "推荐系统训练平台"
        System[Recommendation Training System]
    end
    
    subgraph "外部系统"
        S3[AWS S3<br/>数据存储]
        MLflow[MLflow<br/>实验追踪]
        Monitoring[监控系统]
    end
    
    subgraph "数据源"
        RawData[原始行为数据]
        Features[特征数据]
        Labels[标签数据]
    end
    
    User -->|配置和运行训练| System
    System -->|读取数据| S3
    System -->|记录实验| MLflow
    System -->|发送指标| Monitoring
    RawData -->|存储| S3
    Features -->|存储| S3
    Labels -->|存储| S3
    System -->|保存模型| S3
```

## C2: 容器图 (Container Diagram)

```mermaid
graph TB
    subgraph "推荐系统训练平台"
        subgraph "数据处理容器"
            PP[ParallelProcessor<br/>Python多进程]
            DA[DataAnalyzer<br/>数据分析]
            Pre[Preprocessor<br/>预处理器]
        end
        
        subgraph "特征工程容器"
            FM[FeatureManager<br/>特征管理]
            FE[FeatureExtractor<br/>特征提取]
            FS[FeatureSelector<br/>特征选择]
        end
        
        subgraph "训练容器"
            Trainer[ModelTrainer<br/>PyTorch]
            Models[Models<br/>MLP/DCN/DLRM]
            Optimizer[Optimizer<br/>损失优化]
        end
        
        subgraph "工具容器"
            Logger[MinimalLogger<br/>日志追踪]
            Monitor[GradientMonitor<br/>梯度监控]
            Init[AdaptiveInit<br/>自适应初始化]
        end
        
        subgraph "存储层"
            Cache[NPY缓存<br/>文件系统]
            Config[配置文件<br/>JSON/Python]
        end
    end
    
    PP --> DA
    DA --> Pre
    Pre --> Cache
    Cache --> FM
    FM --> FE
    FE --> FS
    FS --> Trainer
    Trainer --> Models
    Models --> Optimizer
    Trainer --> Monitor
    Trainer --> Init
    All --> Logger
```

## C3: 组件图 (Component Diagram)

### 数据处理组件

```mermaid
graph LR
    subgraph "parallel_processor.py"
        PPC[ParallelProcessor类]
        PPM1[process_dataset方法]
        PPM2[process_files_parallel方法]
        PPM3[_process_single_file_worker方法]
        PPM4[_merge_and_save_results方法]
        
        PPC --> PPM1
        PPM1 --> PPM2
        PPM2 --> PPM3
        PPM2 --> PPM4
    end
    
    subgraph "data_analyzer.py"
        DAC[DataAnalyzer类]
        DAM1[analyze_dataset方法]
        DAM2[get_normalization_params方法]
        DAM3[save_analysis_results方法]
        
        DAC --> DAM1
        DAC --> DAM2
        DAC --> DAM3
    end
    
    subgraph "preprocess.py"
        IPC[IntelligentPreprocessor类]
        IPM1[process_parquet_chunk方法]
        IPM2[_extract_features方法]
        IPM3[_normalize_features方法]
        
        IPC --> IPM1
        IPM1 --> IPM2
        IPM1 --> IPM3
    end
    
    PPM3 --> IPM1
    DAM2 --> IPM3
```

### 模型训练组件

```mermaid
graph LR
    subgraph "train_loss_optimized.py"
        TLO[main函数]
        TLO1[load_and_normalize_data]
        TLO2[train_model_optimized]
        TLO3[evaluate_model]
        
        TLO --> TLO1
        TLO --> TLO2
        TLO2 --> TLO3
    end
    
    subgraph "models.py"
        MC[模型类]
        MLP[MLP模型]
        DCN1[DCNv1模型]
        DCN2[DCNv2模型]
        DLRM[DLRM模型]
        
        MC --> MLP
        MC --> DCN1
        MC --> DCN2
        MC --> DLRM
    end
    
    subgraph "common/training.py"
        CT[training_loop函数]
        CT1[train_epoch]
        CT2[validate_epoch]
        CT3[save_checkpoint]
        
        CT --> CT1
        CT --> CT2
        CT --> CT3
    end
    
    TLO2 --> CT
    TLO --> MC
```

### 特征管理组件

```mermaid
graph LR
    subgraph "feature_manager.py"
        FMC[FeatureManager类]
        FMM1[get_feature_indices]
        FMM2[get_feature_groups]
        FMM3[validate_features]
        
        FMC --> FMM1
        FMC --> FMM2
        FMC --> FMM3
    end
    
    subgraph "feature_selection.py"
        FSC[select_features函数]
        FSC1[compute_importance]
        FSC2[filter_by_importance]
        FSC3[remove_correlated]
        
        FSC --> FSC1
        FSC --> FSC2
        FSC --> FSC3
    end
    
    subgraph "save_feature_metadata.py"
        SFM[extract_feature_metadata]
        SFM1[scan_processed_data]
        SFM2[generate_metadata]
        SFM3[save_to_json]
        
        SFM --> SFM1
        SFM --> SFM2
        SFM --> SFM3
    end
    
    FMM1 --> FSC
    SFM2 --> FMC
```

## C4: 代码结构图 (Code Structure)

### 项目目录结构

```
parallel_experiments_method1/
├── src/
│   ├── common/                    # 公共模块
│   │   ├── __init__.py
│   │   ├── io.py                 # I/O工具
│   │   ├── utils.py              # 通用工具
│   │   └── training.py           # 训练工具
│   │
│   ├── config.py                 # 配置中心
│   ├── parallel_processor.py     # 并行处理器
│   ├── data_analyzer.py          # 数据分析器
│   ├── preprocess.py             # 预处理器
│   ├── feature_manager.py        # 特征管理器
│   ├── feature_selection.py      # 特征选择
│   ├── save_feature_metadata.py  # 特征元数据
│   │
│   ├── models.py                 # 基础模型
│   ├── models_o3.py              # 优化模型v3
│   ├── models_o3_cc.py           # 优化模型v3(cc版)
│   │
│   ├── train.py                  # 基础训练
│   ├── train_pytorch.py          # PyTorch训练
│   ├── train_pytorch_fixed.py    # 修复版训练
│   ├── train_loss_optimized.py   # 优化版训练
│   │
│   ├── gradient_monitor.py       # 梯度监控
│   ├── adaptive_init.py          # 自适应初始化
│   ├── minimal_logger.py         # 最小日志器
│   ├── mlflow_config.py          # MLflow配置
│   │
│   ├── run_parallel_processing.py # 主运行脚本
│   └── check_data.py             # 数据检查
│
├── tests/                         # 测试目录
│   ├── test_compliance.py        # 合规测试
│   ├── test_gradient_debug.py    # 梯度测试
│   └── test_loss_optimization.py # 损失测试
│
├── processed_data/                # 处理后数据
│   ├── train/
│   ├── validation/
│   └── test/
│
├── artifacts/                     # 实验产出
│   └── models/
│
└── mlruns/                       # MLflow追踪
```

### 核心类依赖关系

```mermaid
classDiagram
    class ParallelProcessor {
        +num_workers: int
        +chunk_size: int
        +process_dataset()
        +process_files_parallel()
        -_process_single_file_worker()
    }
    
    class DataAnalyzer {
        +stats: dict
        +analyze_dataset()
        +get_normalization_params()
        +save_analysis_results()
    }
    
    class FeatureManager {
        +feature_groups: dict
        +get_feature_indices()
        +validate_features()
    }
    
    class DCNv2 {
        +input_size: int
        +hidden_sizes: list
        +num_cross_layers: int
        +forward()
        +init_weights()
    }
    
    class AdaptiveInitializer {
        +data_stats: dict
        +initialize()
        -_calculate_scale()
    }
    
    class GradientMonitor {
        +model: nn.Module
        +get_gradient_stats()
        +check_gradient_flow()
    }
    
    ParallelProcessor ..> DataAnalyzer : uses
    ParallelProcessor ..> IntelligentPreprocessor : uses
    DCNv2 ..> AdaptiveInitializer : initialized by
    DCNv2 ..> GradientMonitor : monitored by
    FeatureManager ..> DataAnalyzer : uses stats
```

## 数据流架构

```mermaid
graph LR
    subgraph "输入层"
        Raw[原始Parquet文件]
    end
    
    subgraph "处理层"
        Raw --> Chunk[分块读取<br/>10000行/块]
        Chunk --> Process[并行处理<br/>88进程]
        Process --> Merge[合并结果]
    end
    
    subgraph "存储层"
        Merge --> NPY[NPY文件<br/>features.npy<br/>labels.npy]
        Merge --> Meta[元数据<br/>feature_metadata.json]
    end
    
    subgraph "训练层"
        NPY --> Load[加载数据]
        Meta --> Load
        Load --> Train[模型训练]
        Train --> Model[训练好的模型]
    end
    
    subgraph "服务层"
        Model --> Inference[推理服务]
        Inference --> API[REST API]
    end
```

## 部署架构

```mermaid
graph TB
    subgraph "AWS Infrastructure"
        subgraph "计算层"
            EC2_1[EC2 r5.24xlarge<br/>96 vCPU, 768GB RAM]
            EC2_2[EC2 g4dn.xlarge<br/>GPU训练]
        end
        
        subgraph "存储层"
            S3_Raw[S3: raw-data]
            S3_Proc[S3: processed-data]
            S3_Model[S3: model-artifacts]
        end
        
        subgraph "管理层"
            MLflow_Server[MLflow Server]
            CloudWatch[CloudWatch监控]
        end
    end
    
    EC2_1 -->|读取| S3_Raw
    EC2_1 -->|写入| S3_Proc
    EC2_2 -->|读取| S3_Proc
    EC2_2 -->|写入| S3_Model
    EC2_1 --> MLflow_Server
    EC2_2 --> MLflow_Server
    EC2_1 --> CloudWatch
    EC2_2 --> CloudWatch
```

## 技术栈

### 核心技术
- **语言**: Python 3.8+
- **深度学习框架**: PyTorch 1.9+
- **数据处理**: NumPy, Pandas, PyArrow
- **并行处理**: multiprocessing, concurrent.futures
- **实验追踪**: MLflow
- **存储**: AWS S3, 本地文件系统

### 主要依赖
```python
# requirements.txt 核心依赖
torch>=1.9.0
numpy>=1.19.0
pandas>=1.2.0
scikit-learn>=0.24.0
pyarrow>=4.0.0
mlflow>=1.20.0
boto3>=1.18.0  # S3访问
psutil>=5.8.0  # 系统监控
tqdm>=4.60.0   # 进度条
```

## 设计模式

### 1. 工厂模式 - 模型创建
```python
def build_model(model_type, input_size):
    """模型工厂"""
    if model_type == 'mlp':
        return MLP(input_size)
    elif model_type == 'dcnv2':
        return DCNv2(input_size)
    # ...
```

### 2. 策略模式 - 损失策略
```python
class PosWeightStrategy:
    """损失权重策略"""
    def calculate(self, pos_count, neg_count):
        pass

class BalancedStrategy(PosWeightStrategy):
    def calculate(self, pos_count, neg_count):
        return neg_count / pos_count
```

### 3. 观察者模式 - 梯度监控
```python
class GradientMonitor:
    """观察者：监控训练过程"""
    def update(self, model, epoch, loss):
        # 记录梯度统计
        pass
```

### 4. 单例模式 - 配置管理
```python
class Config:
    """全局配置单例"""
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
```

## 性能考虑

### 并发模型
- **数据处理**: 多进程（CPU密集型）
- **数据加载**: 多线程（I/O密集型）
- **模型训练**: 单进程+GPU（计算密集型）

### 内存管理
- **分块处理**: 避免内存溢出
- **内存映射**: 大文件高效访问
- **垃圾回收**: 显式调用gc.collect()

### 扩展性设计
- **水平扩展**: 支持多机分布式处理
- **垂直扩展**: 自适应利用可用资源
- **模块化**: 组件独立，易于替换

**Why — 设计动机与取舍**

架构设计遵循"高内聚低耦合"原则。分层架构确保了关注点分离：数据处理层专注于高效的数据转换，特征工程层负责特征的提取和选择，训练层实现模型的训练和优化。选择多进程而非分布式是因为单机资源（96核768GB内存）足够处理TB级数据，避免了分布式系统的复杂性。模块化设计使得每个组件都可以独立测试和优化，提高了系统的可维护性。整体架构在性能、可扩展性和开发效率之间找到了平衡点。