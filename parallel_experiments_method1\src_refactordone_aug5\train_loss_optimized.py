# train_loss_optimized.py
"""
专门优化loss问题的训练脚本

🎯 主要优化点：
1. 修复pos_weight计算逻辑
2. 统一训练和评估的loss计算
3. 避免WeightedRandomSampler和pos_weight双重平衡
4. 优化学习率设置
5. 添加详细的loss分析和调试信息

📊 目标：将loss从1.0+降低到0.5以下
"""

import os as _os
from config import PERFORMANCE_CONFIG as _PERF_CFG

# 设置环境变量
_os.environ.setdefault("OMP_NUM_THREADS", str(_PERF_CFG['omp_num_threads']))
_os.environ.setdefault("MKL_NUM_THREADS", str(_PERF_CFG['mkl_num_threads']))

import torch
torch.set_num_threads(_PERF_CFG['torch_threads'])
torch.set_num_interop_threads(min(4, _PERF_CFG['torch_threads'] // 2))

import torch.nn as nn
import numpy as np
from torch.utils.data import TensorDataset, DataLoader
from sklearn.metrics import roc_auc_score, average_precision_score
from sklearn.preprocessing import StandardScaler
import argparse
import os
import logging
import json
import time
from datetime import datetime

from config import PARALLEL_EXTENDED_CONFIG, MODEL_SPECIFIC_CONFIG, PERFORMANCE_CONFIG, GPU_CONFIG
from gpu_utils import get_device_config, auto_select_batch_size, log_gpu_memory_usage
from common import training as common_training
from common import metrics as common_metrics
from common import models as common_models

# ============================================================================
# 🔧 模型版本配置 - 只需修改这一行即可切换模型版本
# ============================================================================
MODEL_VERSION = "models_o3_cc"  # 选择: "models", "models_o3_orig", "models_o3", "models_o3_cc"
# ============================================================================

# 自动导入对应的模型构建函数
MODEL_IMPORTS = {
    "models": "from models import build_model",
    "models_o3_orig": "from models_o3_orig import build_model",
    "models_o3": "from models_o3 import build_model",
    "models_o3_cc": "from models_o3_cc import build_model"
}

if MODEL_VERSION not in MODEL_IMPORTS:
    raise ValueError(f"Unknown MODEL_VERSION: {MODEL_VERSION}. Available: {list(MODEL_IMPORTS.keys())}")

# 动态导入 - moved to common.models
# exec(MODEL_IMPORTS[MODEL_VERSION])  # Now handled in common.models.build_model

# 如果使用旧版本模型，发出警告
if MODEL_VERSION in ["models_o3", "models_o3_orig"]:
    logger.warning(f"⚠️ 使用{MODEL_VERSION}可能存在初始化问题，建议使用models_o3_cc")

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 尝试导入梯度监控器（可选）
try:
    from gradient_monitor import GradientMonitor, diagnose_model_initialization
    GRADIENT_MONITOR_AVAILABLE = True
except ImportError:
    logger.warning("梯度监控器不可用，将跳过详细的梯度分析")
    GRADIENT_MONITOR_AVAILABLE = False

# 导入自适应初始化模块
try:
    from adaptive_init import AdaptiveInitializer, robust_data_preprocessing
    ADAPTIVE_INIT_AVAILABLE = True
except ImportError:
    logger.warning("自适应初始化模块不可用")
    ADAPTIVE_INIT_AVAILABLE = False

# 导入特征选择模块
from feature_selection import select_features_by_group, load_feature_metadata
from feature_manager import FeatureManager, FeatureAccessor


def load_and_normalize_data(include_groups=None, exclude_groups=None):
    """加载并标准化数据，增加特征裁剪以防止极端值
    
    Args:
        include_groups: List of group prefixes to include (None = include all)
        exclude_groups: List of group prefixes to exclude
    """
    processed_dir = PARALLEL_EXTENDED_CONFIG['processed_data_dir']
    
    # Create FeatureManager instance
    metadata_file = os.path.join(processed_dir, 'feature_metadata_expanded.json')
    
    if not os.path.exists(metadata_file):
        logger.warning("Feature metadata not found, using all features")
        feature_manager = None
        selected_feature_indices = None
    else:
        feature_manager = FeatureManager(metadata_file)
        logger.info(f"Loaded feature metadata: {feature_manager.total_features} features")
        
        # Use FeatureManager for feature selection
        if include_groups or exclude_groups:
            selected_feature_indices = feature_manager.get_feature_indices(
                by_groups=include_groups,
                exclude_groups=exclude_groups
            )
            
            # Log selected features info
            logger.info(f"Feature selection: {len(selected_feature_indices)} out of {feature_manager.total_features} features selected")
            
            # Show feature group distribution if not too many
            if include_groups and len(include_groups) <= 5:
                for group in include_groups:
                    group_info = feature_manager.get_group_info(group)
                    if group_info['exists']:
                        logger.info(f"  Group '{group}': {group_info['count']} features")
                        
            if exclude_groups and len(exclude_groups) <= 5:
                for group in exclude_groups:
                    group_info = feature_manager.get_group_info(group)
                    if group_info['exists']:
                        logger.info(f"  Excluded group '{group}': {group_info['count']} features")
        else:
            selected_feature_indices = None
    
    datasets = {}
    for dataset_name in ['train', 'validation', 'test']:
        feature_file = os.path.join(processed_dir, f"{dataset_name}_features.npy")
        label_file = os.path.join(processed_dir, f"{dataset_name}_labels.npy")
        
        if os.path.exists(feature_file) and os.path.exists(label_file):
            features = np.load(feature_file)
            labels = np.load(label_file)
            
            # Apply feature selection if specified
            if selected_feature_indices is not None:
                features = features[:, selected_feature_indices]
                logger.info(f"Applied feature selection: {len(selected_feature_indices)} features selected")
            
            datasets[dataset_name] = (features, labels)
            logger.info(f"加载 {dataset_name} 数据: 特征 {features.shape}, 标签 {labels.shape}")
        else:
            logger.error(f"数据文件不存在: {feature_file} 或 {label_file}")
            return None
    
    if 'train' not in datasets:
        logger.error("训练数据不存在")
        return None
    
    # 使用训练数据拟合标准化器
    X_train, y_train = datasets['train']
    
    # Step 1: 裁剪极端值（使用percentile方法）
    logger.info("裁剪特征极端值...")
    for i in range(X_train.shape[1]):
        # 计算0.1%和99.9%分位数
        lower = np.percentile(X_train[:, i], 0.1)
        upper = np.percentile(X_train[:, i], 99.9)
        X_train[:, i] = np.clip(X_train[:, i], lower, upper)
    
    # Step 2: 标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    
    # Step 3: 额外的范围限制（防止标准化后仍有极端值）
    X_train_scaled = np.clip(X_train_scaled, -5, 5)
    
    logger.info("数据标准化完成")
    logger.info(f"标准化后训练数据: mean={X_train_scaled.mean():.6f}, std={X_train_scaled.std():.6f}")
    logger.info(f"标准化后训练数据范围: min={X_train_scaled.min():.6f}, max={X_train_scaled.max():.6f}")
    
    # 标准化其他数据集
    result = {}
    result['train'] = (torch.from_numpy(X_train_scaled).float(), torch.from_numpy(y_train).float())
    
    for dataset_name in ['validation', 'test']:
        if dataset_name in datasets:
            X, y = datasets[dataset_name]
            # 对验证/测试集应用相同的裁剪和标准化
            for i in range(X.shape[1]):
                # 使用训练集的分位数进行裁剪
                lower = np.percentile(datasets['train'][0][:, i], 0.1)
                upper = np.percentile(datasets['train'][0][:, i], 99.9)
                X[:, i] = np.clip(X[:, i], lower, upper)
            
            X_scaled = scaler.transform(X)
            X_scaled = np.clip(X_scaled, -5, 5)  # 相同的范围限制
            
            result[dataset_name] = (torch.from_numpy(X_scaled).float(), torch.from_numpy(y).float())
            logger.info(f"标准化 {dataset_name} 数据: mean={X_scaled.mean():.6f}, std={X_scaled.std():.6f}")
            logger.info(f"标准化 {dataset_name} 数据范围: min={X_scaled.min():.6f}, max={X_scaled.max():.6f}")
    
    # Add feature_manager and selected_indices to result
    result['feature_manager'] = feature_manager
    result['selected_indices'] = selected_feature_indices
    
    return result

def create_simple_dataloader(features, labels, batch_size, shuffle=True, force_single_worker=False, device_type='cpu'):
    """创建简单的数据加载器，不使用加权采样
    
    Args:
        force_single_worker: 强制使用单进程加载（用于调试或避免多进程问题）
        device_type: 设备类型 ('cpu', 'single_gpu', 'multi_gpu')
    """
    # 基础配置
    loader_kwargs = {
        'batch_size': batch_size,
        'shuffle': shuffle,
    }
    
    if force_single_worker or device_type == 'cpu':
        # CPU训练时的保守配置
        loader_kwargs.update({
            'num_workers': 0,  # 使用主进程加载
            'pin_memory': False,  # CPU训练不需要pin_memory
        })
        logger.info(f"使用单进程数据加载（{'强制' if force_single_worker else 'CPU'}模式）")
    else:
        # GPU训练时使用优化配置
        perf_cfg = PERFORMANCE_CONFIG
        num_workers = perf_cfg['dataloader_workers']
        
        # 构建DataLoader参数
        loader_kwargs.update({
            'pin_memory': perf_cfg['pin_memory'],
            'num_workers': num_workers,
        })
        
        # 只有在使用多进程时才设置prefetch_factor和persistent_workers
        if num_workers > 0:
            loader_kwargs.update({
                'prefetch_factor': perf_cfg['prefetch_factor'],
                'persistent_workers': perf_cfg['persistent_workers'],
            })
        
        logger.info(f"使用{'多' if num_workers > 0 else '单'}进程数据加载（GPU模式，{num_workers}个worker）")

    dataset = TensorDataset(features, labels)
    return DataLoader(dataset, **loader_kwargs)


def adjust_dcnv2_config_by_features(config, input_dim):
    """根据feature数量动态调整DCNv2网络结构
    
    基于embedding展开后的实际feature维度（从NPY文件）来调整网络架构
    """
    logger.info(f"🔧 根据特征维度({input_dim})动态调整DCNv2网络结构...")
    
    if input_dim <= 20:
        # 小型网络：特征数<=20
        config['cross_layers'] = 2
        config['deep_layers'] = [128, 64]
        logger.info("  使用小型网络配置: cross_layers=2, deep_layers=[128, 64]")
    elif input_dim <= 50:
        # 中型网络：20<特征数<=50
        config['cross_layers'] = 3
        config['deep_layers'] = [256, 128]
        logger.info("  使用中型网络配置: cross_layers=3, deep_layers=[256, 128]")
    elif input_dim <= 100:
        # 标准网络：50<特征数<=100
        config['cross_layers'] = 3
        config['deep_layers'] = [512, 256, 128]
        logger.info("  使用标准网络配置: cross_layers=3, deep_layers=[512, 256, 128]")
    elif input_dim <= 200:
        # 大型网络：100<特征数<=200
        config['cross_layers'] = 4
        config['deep_layers'] = [768, 384, 192, 96]
        logger.info("  使用大型网络配置: cross_layers=4, deep_layers=[768, 384, 192, 96]")
    else:
        # 超大型网络：特征数>200
        config['cross_layers'] = 4
        config['deep_layers'] = [1024, 512, 256, 128]
        logger.info("  使用超大型网络配置: cross_layers=4, deep_layers=[1024, 512, 256, 128]")
    
    return config



def train_model_optimized(model_type='dcnv2', epochs=10, learning_rate=None, batch_size=1024, 
                         pos_weight_strategy='balanced', use_amp=None, enable_gradient_monitor=False,
                         enable_adaptive_init=True, weight_decay=0.01, force_cpu=False,
                         include_groups=None, exclude_groups=None):
    """优化的训练函数
    
    Args:
        use_amp: 是否使用混合精度训练。None=自动决定（GPU启用，CPU禁用）
        enable_gradient_monitor: 是否启用梯度监控（会略微降低训练速度）
        enable_adaptive_init: 是否启用自适应初始化（推荐用于新数据集）
        include_groups: List of feature group prefixes to include (None = include all)
        exclude_groups: List of feature group prefixes to exclude
    """
    logger.info(f"开始优化训练 {model_type} 模型...")
    logger.info(f"pos_weight策略: {pos_weight_strategy}")
    
    # 获取设备配置
    device_type, device_count, device = get_device_config()
    if force_cpu:
        device_type = 'cpu'
        device = torch.device('cpu')
    
    # Set random seed for reproducibility
    common_training.seed_all(42)
    
    if force_cpu:
        logger.info("强制使用CPU训练")
    
    # 加载数据
    data = load_and_normalize_data(include_groups=include_groups, exclude_groups=exclude_groups)
    if data is None:
        return None
    
    X_train, y_train = data['train']
    X_val, y_val = data['validation']
    feature_manager = data.get('feature_manager')
    selected_indices = data.get('selected_indices')
    
    # 分析数据分布
    common_metrics.analyze_data_distribution(y_train, "训练集")
    common_metrics.analyze_data_distribution(y_val, "验证集")
    
    # 计算最优pos_weight
    pos_weight_value = common_metrics.calculate_pos_weight(y_train, pos_weight_strategy)
    pos_weight = torch.tensor([pos_weight_value])
    
    # 根据设备类型调整批次大小
    if device_type != 'cpu' and GPU_CONFIG['enabled']:
        # GPU可以处理更大的批次
        original_batch_size = batch_size
        batch_size = batch_size * GPU_CONFIG['gpu_batch_size_multiplier']
        logger.info(f"GPU训练：批次大小从{original_batch_size}增加到{batch_size}")
    
    # 设置学习率（降低以解决梯度爆炸）
    if learning_rate is None:
        if model_type == 'dcnv2':
            learning_rate = 0.0001  # 从0.0005降至0.0001
        else:
            learning_rate = 0.0002  # 从0.001降至0.0002
    
    logger.info(f"训练配置:")
    logger.info(f"  模型类型: {model_type}")
    logger.info(f"  设备: {device} ({device_type})")
    logger.info(f"  Epochs: {epochs}")
    logger.info(f"  Learning Rate: {learning_rate}")
    logger.info(f"  Batch Size: {batch_size}")
    logger.info(f"  Weight Decay: {weight_decay}")
    logger.info(f"  pos_weight: {pos_weight_value:.3f}")
    
    # 创建数据加载器
    train_loader = create_simple_dataloader(X_train, y_train, batch_size, shuffle=True, device_type=device_type)
    val_loader = create_simple_dataloader(X_val, y_val, batch_size, shuffle=False, device_type=device_type)
    
    # 创建模型
    input_dim = X_train.shape[1]
    logger.info(f"📊 实际特征维度（从NPY文件）: {input_dim}")
    
    # 获取基础配置
    config = MODEL_SPECIFIC_CONFIG.get(model_type, MODEL_SPECIFIC_CONFIG['mlp']).copy()
    config['norm_type'] = 'layer'  # 使用LayerNorm
    
    # 根据feature数量动态调整DCNv2网络结构
    if model_type == 'dcnv2':
        config = adjust_dcnv2_config_by_features(config, input_dim)
    
    model = common_models.build_model(model_type, input_dim, config, model_version=MODEL_VERSION)
    
    # 将模型移到设备上
    model.to(device)
    logger.info(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 如果是多GPU，使用DataParallel
    if device_type == 'multi_gpu' and GPU_CONFIG['multi_gpu_strategy'] == 'dp':
        model = nn.DataParallel(model)
        logger.info(f"使用DataParallel在{device_count}个GPU上并行训练")
    
    # 决定是否使用混合精度
    if use_amp is None:
        use_amp = device.type == "cuda"  # 仅GPU启用AMP
    
    if use_amp and device.type == "cpu":
        logger.warning("⚠️ CPU不支持混合精度训练，已禁用AMP")
        use_amp = False
    
    logger.info(f"混合精度训练: {use_amp}")
    
    # 梯度监控器初始化
    gradient_monitor = None
    if enable_gradient_monitor and GRADIENT_MONITOR_AVAILABLE:
        logger.info("启用梯度监控器")
        gradient_monitor = GradientMonitor(model, log_interval=1000)
        # 诊断模型初始化
        diagnose_model_initialization(model, input_dim, device=device)
    
    # 损失函数和优化器
    criterion = common_metrics.build_bce_with_logits(pos_weight, device)
    optimizer = torch.optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=weight_decay)
    
    # 使用OneCycleLR替代ReduceLROnPlateau，更适合处理梯度爆炸
    scheduler = torch.optim.lr_scheduler.OneCycleLR(
        optimizer, 
        max_lr=learning_rate,
        steps_per_epoch=len(train_loader),
        epochs=epochs,
        pct_start=0.1,  # 10%用于warmup
        div_factor=10,  # 初始lr = max_lr/10
        final_div_factor=100  # 最终lr = max_lr/100
    )
    
    # 训练循环
    best_val_auc = 0.0
    best_val_loss = float('inf')
    patience_counter = 0
    patience = 3  # 早停耐心值

    # 自适应初始化（如果启用）
    if enable_adaptive_init and ADAPTIVE_INIT_AVAILABLE:
        logger.info("🔧 执行自适应初始化...")
        
        # 获取一小批数据用于初始化
        sample_size = min(1000, len(X_train))
        sample_indices = torch.randperm(len(X_train))[:sample_size]
        sample_data = X_train[sample_indices]
        sample_labels = y_train[sample_indices]
        
        # 创建自适应初始化器
        initializer = AdaptiveInitializer(
            target_output_std=1.0,
            max_attempts=5,
            verbose=True
        )
        
        # 执行自适应初始化
        init_stats = initializer.smart_init_model(
            model=model,
            sample_data=sample_data,
            sample_labels=sample_labels,
            device=device
        )
        
        if not init_stats['success']:
            logger.warning("⚠️ 自适应初始化未完全成功，但可以继续训练")
    
    # 🔍 评估初始状态 (第一个epoch之前)
    logger.info("🔍 评估模型初始状态...")
    
    # 检查模型初始化
    with torch.no_grad():
        test_input = torch.randn(32, input_dim).to(device)
        test_output = model(test_input)
        logger.info(f"模型初始化测试: output mean={test_output.mean().item():.4f}, std={test_output.std().item():.4f}")
        if test_output.std().item() > 100:
            logger.error("🚨 警告：模型初始输出方差过大！可能存在初始化问题！")
            
            # 如果没有启用自适应初始化，现在尝试修复
            if not enable_adaptive_init and ADAPTIVE_INIT_AVAILABLE:
                logger.info("尝试紧急修复初始化...")
                initializer = AdaptiveInitializer(target_output_std=1.0, max_attempts=3, verbose=False)
                initializer.smart_init_model(model, test_input, torch.zeros(32), device)
    
    # 使用小批量评估初始状态（加快速度）
    sample_train_loader = create_simple_dataloader(X_train[:10000], y_train[:10000], batch_size, shuffle=False, device_type=device_type)
    init_train_metrics = common_training.evaluate_epoch(model, sample_train_loader, device, criterion)
    init_train_loss = init_train_metrics['loss']
    init_train_roc_auc = init_train_metrics['roc_auc']
    init_train_pr_auc = init_train_metrics['pr_auc']
    logger.info("初始训练集(采样) 详细分析:")
    logger.info(f"  平均Loss: {init_train_loss:.6f}")
    logger.info(f"  ROC AUC: {init_train_roc_auc:.6f}")
    logger.info(f"  PR AUC: {init_train_pr_auc:.6f}")
    
    init_val_metrics = common_training.evaluate_epoch(model, val_loader, device, criterion)
    init_val_loss = init_val_metrics['loss']
    init_val_roc_auc = init_val_metrics['roc_auc']
    init_val_pr_auc = init_val_metrics['pr_auc']
    logger.info("初始验证集 详细分析:")
    logger.info(f"  平均Loss: {init_val_loss:.6f}")
    logger.info(f"  ROC AUC: {init_val_roc_auc:.6f}")
    logger.info(f"  PR AUC: {init_val_pr_auc:.6f}")

    logger.info("📊 初始状态指标:")
    logger.info(f"  训练集 - Loss: {init_train_loss:.6f}, ROC AUC: {init_train_roc_auc:.6f}, PR AUC: {init_train_pr_auc:.6f}")
    logger.info(f"  验证集 - Loss: {init_val_loss:.6f}, ROC AUC: {init_val_roc_auc:.6f}, PR AUC: {init_val_pr_auc:.6f}")
    logger.info("="*60)

    # Use common training loop
    history = common_training.training_loop(
        model, train_loader, val_loader, optimizer, criterion, scheduler,
        epochs=epochs, device=device, amp=use_amp, gradient_monitor=gradient_monitor
    )
    
    # Log epoch results as before
    for epoch_idx in range(history['epochs_completed']):
        epoch = epoch_idx + 1
        
        # Get metrics from history
        avg_train_loss = history['train_losses'][epoch_idx]
        val_loss = history['val_losses'][epoch_idx]
        val_roc_auc = history['val_aucs'][epoch_idx]
        
        # Sample training set evaluation every 2 epochs
        if epoch_idx % 2 == 0:
            train_sample_size = min(50000, len(X_train))
            sample_indices = torch.randperm(len(X_train))[:train_sample_size]
            sample_train_loader = create_simple_dataloader(
                X_train[sample_indices], 
                y_train[sample_indices], 
                batch_size, 
                shuffle=False,
                device_type=device_type
            )
            train_metrics = common_training.evaluate_epoch(
                model, sample_train_loader, device, criterion
            )
            logger.info(f"训练集采样 Epoch {epoch} ({train_sample_size}样本) 详细分析:")
            logger.info(f"  平均Loss: {train_metrics['loss']:.6f}")
            logger.info(f"  ROC AUC: {train_metrics['roc_auc']:.6f}")
            logger.info(f"  PR AUC: {train_metrics['pr_auc']:.6f}")
            train_roc_auc = train_metrics['roc_auc']
        else:
            train_roc_auc = None
        
        # Validation logging
        logger.info(f"验证集 Epoch {epoch} 详细分析:")
        logger.info(f"  平均Loss: {val_loss:.6f}")
        logger.info(f"  ROC AUC: {val_roc_auc:.6f}")
        logger.info(f"  PR AUC: {history['last_epoch_info']['val_pr_auc']:.6f}")
        
        # Construct log message
        log_msg = f"Epoch {epoch}/{epochs}: Train Loss: {avg_train_loss:.6f}"
        if train_roc_auc is not None:
            log_msg += f" (Sampled AUC: {train_roc_auc:.6f})"
        log_msg += f", Val Loss: {val_loss:.6f}, Val ROC AUC: {val_roc_auc:.6f}, Val PR AUC: {history['last_epoch_info']['val_pr_auc']:.6f}, Time: {history['last_epoch_info']['epoch_time']:.2f}s"
        
        logger.info(log_msg)
        
        # Best model logging
        if epoch_idx == history['epochs_completed'] - 1:  # Last epoch
            if history['last_epoch_info'].get('early_stopped', False):
                logger.info(f"⚠️ 早停触发！已经{patience}个epoch没有改善")
        
        # Check if this was a new best
        if epoch_idx > 0 and val_roc_auc > history['val_aucs'][epoch_idx-1] + 0.0001:
            logger.info(f"🎯 新的最佳验证ROC AUC: {val_roc_auc:.6f}, PR AUC: {history['last_epoch_info']['val_pr_auc']:.6f}")
        
        # GPU memory logging
        if device.type == 'cuda' and epoch % 5 == 0:
            log_gpu_memory_usage(f"Epoch {epoch} 完成后")
    
    # Update best values from history
    best_val_auc = history['best_val_auc']
    best_val_loss = history['best_val_loss']
    
    # 测试评估
    test_loss = None
    test_roc_auc = None
    test_pr_auc = None
    if 'test' in data:
        X_test, y_test = data['test']
        common_metrics.analyze_data_distribution(y_test, "测试集")
        test_loader = create_simple_dataloader(X_test, y_test, batch_size, shuffle=False, device_type=device_type)
        test_metrics = common_training.evaluate_epoch(model, test_loader, device, criterion)
        test_loss = test_metrics['loss']
        test_roc_auc = test_metrics['roc_auc']
        test_pr_auc = test_metrics['pr_auc']
        logger.info("最终测试集 详细分析:")
        logger.info(f"  平均Loss: {test_loss:.6f}")
        logger.info(f"  ROC AUC: {test_roc_auc:.6f}")
        logger.info(f"  PR AUC: {test_pr_auc:.6f}")
    
    logger.info("="*60)
    logger.info("🎯 训练完成 - 最终结果:")
    logger.info(f"  最佳验证Loss: {float(best_val_loss):.6f}")
    logger.info(f"  最佳验证ROC AUC: {best_val_auc:.6f}")
    if test_loss is not None:
        logger.info(f"  测试Loss: {test_loss:.6f}")
        logger.info(f"  测试ROC AUC: {test_roc_auc:.6f}")
        logger.info(f"  测试PR AUC: {test_pr_auc:.6f}")
    logger.info("="*60)
    
    # 生成梯度分析报告
    if gradient_monitor:
        try:
            # 生成图表（PNG文件）
            gradient_monitor.plot_gradient_history(f'gradient_history_{model_type}_{pos_weight_strategy}.png')
            # 生成文本报告
            gradient_monitor.generate_text_report(f'gradient_report_{model_type}_{pos_weight_strategy}.txt')
        except Exception as e:
            logger.error(f"生成梯度报告时出错: {e}")

    return {
        'model_type': model_type,
        'pos_weight_strategy': pos_weight_strategy,
        'pos_weight_value': float(pos_weight_value),
        'epochs': epochs,
        'learning_rate': float(learning_rate),
        'batch_size': batch_size,
        'best_val_loss': float(best_val_loss),
        'best_val_roc_auc': float(best_val_auc),
        'test_loss': float(test_loss) if test_loss is not None else None,
        'test_roc_auc': float(test_roc_auc) if test_roc_auc is not None else None,
        'test_pr_auc': float(test_pr_auc) if test_pr_auc is not None else None,
        'model_params': sum(p.numel() for p in model.parameters()),
        'device': str(device)
    }

def main():
    parser = argparse.ArgumentParser(description='Loss优化训练脚本')
    parser.add_argument('--model_type', choices=['mlp', 'dcnv2', 'dcnv1', 'dlrm'], default='dcnv2')
    parser.add_argument('--epochs', type=int, default=10)
    parser.add_argument('--batch_size', type=int, default=1024)
    parser.add_argument('--learning_rate', type=float, default=None)
    parser.add_argument('--pos_weight_strategy', choices=['balanced', 'sqrt_balanced', 'log_balanced', 'none'], 
                       default='balanced', help='pos_weight计算策略')
    parser.add_argument('--weight_decay', type=float, default=0.01, help='AdamW权重衰减系数')
    parser.add_argument('--no_amp', action='store_true', help='禁用混合精度训练')
    parser.add_argument('--gradient_monitor', action='store_true', help='启用梯度监控（生成分析报告）')
    parser.add_argument('--no_adaptive_init', action='store_true', help='禁用自适应初始化')
    parser.add_argument('--include_groups', type=str, nargs='*', help='Feature groups to include (e.g., user item context)')
    parser.add_argument('--exclude_groups', type=str, nargs='*', help='Feature groups to exclude (e.g., noise)')
    
    args = parser.parse_args()
    
    logger.info("="*60)
    logger.info("🚀 Loss优化训练开始")
    logger.info("="*60)
    
    results = train_model_optimized(
        model_type=args.model_type,
        epochs=args.epochs,
        learning_rate=args.learning_rate,
        batch_size=args.batch_size,
        pos_weight_strategy=args.pos_weight_strategy,
        weight_decay=args.weight_decay,
        use_amp=not args.no_amp,
        enable_gradient_monitor=args.gradient_monitor,
        enable_adaptive_init=not args.no_adaptive_init,
        include_groups=args.include_groups,
        exclude_groups=args.exclude_groups
    )
    
    if results:
        print("TRAINING_RESULTS_START")
        print(json.dumps(results, indent=2))
        print("TRAINING_RESULTS_END")
        return results
    else:
        logger.error("❌ 训练失败")
        return None

if __name__ == "__main__":
    main()
