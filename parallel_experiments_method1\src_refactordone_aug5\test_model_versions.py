#!/usr/bin/env python3
"""
测试不同模型版本的脚本
用于验证 models.py, models_o3_orig.py, models_o3.py 的兼容性
"""

import torch
import numpy as np
from typing import Dict, Any

def test_model_version(version: str, model_type: str = 'mlp', input_dim: int = 100):
    """测试指定版本的模型"""
    print(f"\n{'='*50}")
    print(f"测试模型版本: {version}")
    print(f"模型类型: {model_type}")
    print(f"{'='*50}")
    
    try:
        # 动态导入
        if version == "models":
            from models import build_model
        elif version == "models_o3_orig":
            from models_o3_orig import build_model
        elif version == "models_o3":
            from models_o3 import build_model
        else:
            raise ValueError(f"Unknown version: {version}")
        
        # 测试配置
        cfg: Dict[str, Any] = {
            'hidden_dims': [256, 128],
            'dropout_rate': 0.3,
            'cross_layers': 3,
            'deep_layers': [256, 128],
        }
        
        # 如果是 models_o3，测试不同的 norm_type
        if version == "models_o3":
            for norm_type in ['layer', 'batch']:
                print(f"\n  测试归一化类型: {norm_type}")
                cfg['norm_type'] = norm_type
                
                model = build_model(model_type, input_dim, cfg)
                
                # 创建测试数据
                batch_size = 32
                x = torch.randn(batch_size, input_dim)
                
                # 前向传播测试
                model.eval()
                with torch.no_grad():
                    output = model(x)
                
                print(f"    ✅ 模型创建成功")
                print(f"    📊 输入形状: {x.shape}")
                print(f"    📊 输出形状: {output.shape}")
                print(f"    🔢 参数数量: {sum(p.numel() for p in model.parameters()):,}")
                print(f"    📈 输出范围: [{output.min().item():.4f}, {output.max().item():.4f}]")
        else:
            model = build_model(model_type, input_dim, cfg)
            
            # 创建测试数据
            batch_size = 32
            x = torch.randn(batch_size, input_dim)
            
            # 前向传播测试
            model.eval()
            with torch.no_grad():
                output = model(x)
            
            print(f"  ✅ 模型创建成功")
            print(f"  📊 输入形状: {x.shape}")
            print(f"  📊 输出形状: {output.shape}")
            print(f"  🔢 参数数量: {sum(p.numel() for p in model.parameters()):,}")
            print(f"  📈 输出范围: [{output.min().item():.4f}, {output.max().item():.4f}]")
            
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("🧪 模型版本兼容性测试")
    
    # 测试参数
    input_dim = 100
    model_types = ['mlp', 'dcnv2', 'dcnv1', 'dlrm']
    versions = ['models', 'models_o3_orig', 'models_o3']
    
    for version in versions:
        for model_type in model_types:
            test_model_version(version, model_type, input_dim)
    
    print(f"\n{'='*50}")
    print("🎉 所有测试完成")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()
